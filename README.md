[![Build Status](http://51.91.248.181/badge/faf509a3?branch=dev)](http://51.91.248.181/repos/25)

# Recruitable [GO-Platform]

## Project setup

```
npm install
```

### Compiles and hot-reloads for development

```
npm run serve
```

### Compiles and minifies for production

```
npm run build
```

### Lints and fixes files

```
npm run lint
```

### Configuration

See [Configuration Reference](https://cli.vuejs.org/config/).

#### Contributing to the Project

1. **Create an Issue:**

    - Before adding a new feature or making significant changes, create an issue on the project repository. Clearly describe the proposed feature or the problem you're addressing.
    - Assign the issue to the team member responsible for implementing the feature.

2. **Branching Strategy:**

    - Create a new branch for the feature or bug fix you're working on. Use a descriptive branch name that reflects the purpose of your changes.

    ```bash
    git checkout -b new-feature-branch
    ```

3. **Development:**

    - Work on your assigned feature or bug fix within the created branch. Make sure to follow the project's coding standards and guidelines.

4. **Committing Changes:**

    - Commit your changes regularly with clear and concise commit messages.

    ```bash
    git commit -m "Add new-feature functionality"
    ```

5. **Push Changes:**

    - Push your branch to the remote repository.

    ```bash
    git push origin new-feature-branch
    ```

6. **Create a Pull Request:**

    - Open a pull request (PR) on the project repository.
    - Provide a detailed description of the changes made, reference the related issue, and mention any relevant information.

7. **Code Review:**

    - Team leaders and other designated reviewers will conduct a thorough code review. Address any feedback or comments provided during the review process.

8. **Merge:**
    - Once the changes have been approved, the pull request will be merged into the main development branch.

This collaborative workflow helps maintain a structured development process, ensures proper communication among team members, and facilitates effective code reviews. It also encourages a transparent and organized approach to feature development and bug fixing within the project.
