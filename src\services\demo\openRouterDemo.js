/**
 * Demo script for testing OpenRouter integration
 * This script demonstrates how to use the OpenRouter service
 * 
 * To run this demo:
 * 1. Set up a real OpenRouter API key in your .env file
 * 2. Run: node src/services/demo/openRouterDemo.js
 */

import openRouterService from '../openRouterService.js';

// Sample job descriptions for testing
const sampleJobDescriptions = {
    softwareDeveloper: `
        We are seeking a Senior Full Stack Developer to join our innovative team. 
        
        Key Responsibilities:
        - Develop and maintain web applications using JavaScript, React, and Node.js
        - Design and implement RESTful APIs and microservices
        - Collaborate with cross-functional teams to deliver high-quality software
        - Participate in code reviews and maintain coding standards
        - Troubleshoot and debug applications
        
        Required Skills:
        - 5+ years of experience in JavaScript development
        - Proficiency in React.js and modern frontend frameworks
        - Experience with Node.js and Express.js
        - Strong knowledge of HTML5, CSS3, and responsive design
        - Experience with SQL and NoSQL databases (PostgreSQL, MongoDB)
        - Familiarity with version control systems (Git)
        - Understanding of Agile development methodologies
        
        Preferred Skills:
        - Experience with cloud platforms (AWS, Azure, or GCP)
        - Knowledge of containerization technologies (Docker, Kubernetes)
        - Experience with CI/CD pipelines
        - Understanding of testing frameworks (Jest, Cypress)
        - Strong problem-solving and analytical skills
        - Excellent communication and teamwork abilities
        - Leadership experience and mentoring skills
    `,
    
    dataScientist: `
        We are looking for a Data Scientist to analyze large amounts of raw information 
        to find patterns that will help improve our company.
        
        Responsibilities:
        - Identify valuable data sources and automate collection processes
        - Undertake preprocessing of structured and unstructured data
        - Analyze large amounts of information to discover trends and patterns
        - Build predictive models and machine-learning algorithms
        - Present information using data visualization techniques
        
        Requirements:
        - Proven experience as a Data Scientist or Data Analyst
        - Experience in data mining and statistical analysis
        - Knowledge of R, SQL and Python; familiarity with Scala, Java or C++ is an asset
        - Experience using business intelligence tools (e.g. Tableau) and data frameworks (e.g. Hadoop)
        - Analytical mind and business acumen
        - Strong math skills (e.g. statistics, algebra)
        - Problem-solving aptitude
        - Excellent communication and presentation skills
        - BSc/BA in Computer Science, Engineering or relevant field; graduate degree in Data Science or other quantitative field is preferred
    `,
    
    marketingManager: `
        We are seeking a dynamic Marketing Manager to develop, implement and execute 
        strategic marketing plans for our entire organization.
        
        Key Responsibilities:
        - Develop and implement comprehensive marketing strategies
        - Manage and coordinate marketing campaigns across multiple channels
        - Analyze market trends and competitor activities
        - Oversee social media marketing and content creation
        - Manage marketing budget and ROI analysis
        - Lead and mentor marketing team members
        - Collaborate with sales team to generate leads
        
        Required Qualifications:
        - Bachelor's degree in Marketing, Business, or related field
        - 3+ years of marketing experience
        - Strong analytical and project management skills
        - Experience with digital marketing tools and platforms
        - Proficiency in Google Analytics, SEO/SEM, and social media platforms
        - Excellent written and verbal communication skills
        - Creative thinking and problem-solving abilities
        - Leadership and team management experience
        - Strong presentation and public speaking skills
    `
};

async function runDemo() {
    console.log('🚀 OpenRouter Service Demo Starting...\n');
    
    // Test validation
    console.log('📋 Testing Validation:');
    console.log('Empty description:', openRouterService.validateJobDescription(''));
    console.log('Short description:', openRouterService.validateJobDescription('Too short'));
    console.log('Valid description:', openRouterService.validateJobDescription(sampleJobDescriptions.softwareDeveloper));
    console.log('');
    
    // Test validation errors
    console.log('🚨 Testing Validation Errors:');
    console.log('Empty:', openRouterService.getValidationError(''));
    console.log('Short:', openRouterService.getValidationError('Short'));
    console.log('Valid:', openRouterService.getValidationError(sampleJobDescriptions.softwareDeveloper));
    console.log('');
    
    // Test prompt creation
    console.log('📝 Testing Prompt Creation:');
    const prompt = openRouterService.createSkillExtractionPrompt('Sample job description');
    console.log('Prompt length:', prompt.length);
    console.log('Contains technical_skills:', prompt.includes('technical_skills'));
    console.log('');
    
    // Test skill formatting
    console.log('🔧 Testing Skill Formatting:');
    const mockSkillsData = {
        technical_skills: ['JavaScript', 'React', 'Node.js'],
        soft_skills: ['Communication', 'Leadership'],
        tools_and_technologies: ['Git', 'Docker'],
        certifications: ['AWS Certified Developer']
    };
    
    const formattedSkills = openRouterService.formatSkillsForApplication(mockSkillsData);
    console.log('Formatted skills count:', formattedSkills.length);
    console.log('Sample skill:', formattedSkills[0]);
    console.log('');
    
    // Test actual API call (only if API key is available)
    console.log('🌐 Testing API Integration:');
    
    if (process.env.VUE_APP_OPENROUTER_API_KEY && process.env.VUE_APP_OPENROUTER_API_KEY !== 'sk-or-v1-dummy-key-for-demo') {
        console.log('Real API key detected. Testing with Software Developer job description...');
        
        try {
            const skills = await openRouterService.extractSkillsFromJobDescription(sampleJobDescriptions.softwareDeveloper);
            console.log('✅ API call successful!');
            console.log('Extracted skills count:', skills.length);
            console.log('Skills by category:');
            
            const categories = ['technical', 'soft', 'tools', 'certification'];
            categories.forEach(category => {
                const categorySkills = skills.filter(skill => skill.type === category);
                if (categorySkills.length > 0) {
                    console.log(`  ${category}:`, categorySkills.map(s => s.name).join(', '));
                }
            });
            
        } catch (error) {
            console.log('❌ API call failed:', error.message);
        }
    } else {
        console.log('⚠️  Using dummy API key. Set VUE_APP_OPENROUTER_API_KEY to test real API calls.');
        console.log('For testing purposes, here\'s what the response would look like:');
        
        const mockResponse = {
            choices: [{
                message: {
                    content: JSON.stringify({
                        technical_skills: ['JavaScript', 'React', 'Node.js', 'HTML5', 'CSS3', 'PostgreSQL', 'MongoDB'],
                        soft_skills: ['Problem-solving', 'Communication', 'Teamwork', 'Leadership', 'Analytical thinking'],
                        tools_and_technologies: ['Git', 'AWS', 'Docker', 'Kubernetes', 'Jest', 'Cypress'],
                        certifications: ['AWS Certified Developer']
                    })
                }
            }]
        };
        
        try {
            const skills = openRouterService.parseSkillsResponse(mockResponse);
            console.log('✅ Mock parsing successful!');
            console.log('Extracted skills count:', skills.length);
            console.log('Sample skills:', skills.slice(0, 5).map(s => `${s.name} (${s.category})`).join(', '));
        } catch (error) {
            console.log('❌ Mock parsing failed:', error.message);
        }
    }
    
    console.log('\n🎉 Demo completed!');
    console.log('\nTo test with a real API key:');
    console.log('1. Sign up at https://openrouter.ai/');
    console.log('2. Get your API key');
    console.log('3. Set VUE_APP_OPENROUTER_API_KEY in your .env file');
    console.log('4. Run this demo again');
}

// Run the demo if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    runDemo().catch(console.error);
}

export { runDemo, sampleJobDescriptions };
