<svg width="60" height="60" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_117_9537)">
<rect x="5" y="3" width="50" height="50" rx="25" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M30 18C24.47 18 20 22.47 20 28C20 33.53 24.47 38 30 38C35.53 38 40 33.53 40 28C40 22.47 35.53 18 30 18ZM34.3 32.3C33.91 32.69 33.28 32.69 32.89 32.3L30 29.41L27.11 32.3C26.72 32.69 26.09 32.69 25.7 32.3C25.5127 32.1132 25.4075 31.8595 25.4075 31.595C25.4075 31.3305 25.5127 31.0768 25.7 30.89L28.59 28L25.7 25.11C25.5127 24.9232 25.4075 24.6695 25.4075 24.405C25.4075 24.1405 25.5127 23.8868 25.7 23.7C26.09 23.31 26.72 23.31 27.11 23.7L30 26.59L32.89 23.7C33.28 23.31 33.91 23.31 34.3 23.7C34.69 24.09 34.69 24.72 34.3 25.11L31.41 28L34.3 30.89C34.68 31.27 34.68 31.91 34.3 32.3Z" fill="#C3CAD9"/>
</g>
<defs>
<filter id="filter0_d_117_9537" x="0" y="0" width="60" height="60" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_117_9537"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_117_9537" result="shape"/>
</filter>
</defs>
</svg>
