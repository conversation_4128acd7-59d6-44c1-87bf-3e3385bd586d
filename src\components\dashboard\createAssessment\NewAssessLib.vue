<template>
    <AssessementPreview :assessment="preview" :showPreview="showPreview" :togglePreview="togglePreview" :addAssessment="addAssessment" />
    <div @scroll="handleScroll">
        <div class="library-section">
            <div class="myAssessments">
                <div class="library-grid bg-red-500">
                    <!-- Changed :key="index" to :key="assess._id" for stable rendering -->
                    <LibraryCard v-for="assess in yourAssessment" :key="assess._id" :assessement="assess" @click="OpenPreview(assess)" @delete="deleteAssessement(assess)" />
                </div>
            </div>

            <div class="libraryWrapper relative">
                <div v-if="isLoading" class="loader">
                    <LoadingComponent />
                </div>
                <div class="library" v-else>
                    <div class="bg-blue-50 mb-[2rem] rounded w-full h-[fit-content] p-[2%]">
                        <h2 v-if="selectedOption === 'essentials'" class="text-[24px]" style="font-weight: 600">{{ $t("Essential skills") }}</h2>
                        <h2 v-else class="text-2xl mb-6" style="font-weight: 600">{{ $t("Recommended Skills") }}</h2>
                        <div class="library-grid-top w-full">
                            <!-- Changed :key="index" to :key="assess._id" for stable rendering -->
                            <TopAssessement
                                :removeAssessment="removeAssessment"
                                :placeholders="placeholders"
                                v-for="assess in recommendedAssessments"
                                :key="assess._id"
                                :assessement="assess"
                                @openPreview="OpenPreview(assess)"
                                :addAssessment="addAssessment"
                            />
                        </div>
                    </div>
                    <upgradePlan v-if="!this.Store.premium" />

                    <!-- Main container for search and filters -->
                    <div class="w-full relative flex mt-[4rem] mb-2 items-center gap-4">
                        <!-- This overlay is now redundant, click outside logic handles it -->
                        <!-- <div class="fixed top-0 left-0 w-full h-full" v-if="isOpen" @click="toggleFilter"></div> -->

                        <!-- Search bar -->
                        <div class="relative w-[25%]">
                            <font-awesome-icon :icon="['fas', 'search']" class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 text-sm" />
                            <input
                                :placeholder="$t('Search Test')"
                                class="shadow-m text-sm py-1 pl-8 pr-3 border border-[1.5px] rounded-md h-[3rem] w-full border-[#e7e7e9] outline-none focus:border-2 focus:border-NeonBlue"
                                type="text"
                                id="searchInput"
                                name="searchInput"
                                v-model="searchText"
                                required
                            />
                        </div>

                        <!-- Filters section - now after search bar -->
                        <div class="flex flex-wrap gap-4 relative">
                            <!-- Category Filter -->
                            <div class="relative" ref="categoryFilter">
                                <button @click="toggleCategory" :class="{ 'filter-btn': true, 'bg-NeonBlue text-white': selectedCategories.length > 0 }">
                                    {{ $t("Category") }}
                                    <span class="ml-2"> <font-awesome-icon :icon="['fas', 'caret-down']" /> </span>
                                </button>
                                <div v-if="isOpenCategory" class="dropdown">
                                    <label v-for="(category, index) in categories" :key="index" class="checkbox-label">
                                        <input type="checkbox" :value="category" v-model="selectedCategories" @change="debouncedApplyAllFilters" class="checkbox-input" />
                                        <span class="checkbox-text">{{ $t(category) }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Library Filter -->
                            <div class="relative gap-1.5" ref="libraryFilter">
                                <button @click="toggleLibrary" :class="{ 'filter-btn': true, 'bg-NeonBlue text-white': selectedLibraries.length > 0 }">
                                    {{ $t("Library") }}<span class="ml-2"> <font-awesome-icon :icon="['fas', 'caret-down']" /></span>
                                </button>
                                <div v-if="isOpenLibrary" class="dropdown">
                                    <label v-for="(library, index) in libraries" :key="index" class="checkbox-label">
                                        <input type="checkbox" :value="library" v-model="selectedLibraries" @change="debouncedApplyAllFilters" class="checkbox-input" />
                                        <span class="checkbox-text">{{ $t(library) }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Duration Filter -->
                            <div class="relative gap-1.5" ref="durationFilter">
                                <button @click="toggleDuration" :class="{ 'filter-btn': true, 'bg-NeonBlue text-white': selectedDurations.length > 0 }">
                                    {{ $t("Duration") }} <span class="ml-2"> <font-awesome-icon :icon="['fas', 'caret-down']" /></span>
                                </button>
                                <div v-if="isOpenDuration" class="dropdown">
                                    <label v-for="(duration, index) in testDurations" :key="index" class="checkbox-label">
                                        <input type="checkbox" :value="duration" v-model="selectedDurations" @change="debouncedApplyAllFilters" class="checkbox-input" />
                                        <span class="checkbox-text">{{ $t(duration) }}</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Focus Filter -->
                            <div class="relative gap-1.5" ref="focusFilter">
                                <button @click="toggleFocus" :class="{ 'filter-btn': true, 'bg-NeonBlue text-white': selectedFocus.length > 0 }">
                                    {{ $t("Focus") }} <span class="ml-2"> <font-awesome-icon :icon="['fas', 'caret-down']" /></span>
                                </button>
                                <div v-if="isOpenFocus" class="dropdown">
                                    <label v-for="(focus, index) in testFucus" :key="index" class="checkbox-label">
                                        <input type="checkbox" :value="focus" v-model="selectedFocus" @change="debouncedApplyAllFilters" class="checkbox-input" />
                                        <span class="checkbox-text">{{ $t(focus) }}</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr class="border border-[#e7e7e9] my-6 w-full" />
                    <div class="library-grid-top z-10 w-full">
                        <!-- Changed :key="index" to :key="assess._id" for stable rendering -->
                        <TopAssessement
                            :removeAssessment="removeAssessment"
                            :placeholders="placeholders"
                            v-for="assess in filteredAssessments"
                            :key="assess._id"
                            :assessement="assess"
                            @openPreview="OpenPreview(assess)"
                            :addAssessment="addAssessment"
                        />
                    </div>
                    <div class="flex items-center justify-center h-[300px] w-full" v-if="filteredAssessments.length === 0 && !isLoading">
                        <span class="text-2xl text-[#2196f3]">{{ $t("No assessments found matching your criteria.") }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LibraryCard from "@/components/LibraryCard.vue";
import AssessementPreview from "@/components/dashboard/library/AssessementPreview.vue";
import { BASE_URL } from "@/constants";
import TopAssessement from "@/components/TopAssessement.vue";
import { useStore } from "@/store/index";
import axios from "axios";
import LoadingComponent from "@/components/LoadingComponent.vue";
import upgradePlan from "@/components/upgradePlan.vue";

// Debounce utility function
function debounce(func, delay) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}

export default {
    name: "NewAssessLib",
    components: {
        LibraryCard,
        AssessementPreview,
        TopAssessement,
        LoadingComponent,
        upgradePlan,
    },
    props: {
        addNewAssessment: Function,
        removeAssessment: Function,
        recommendations: Array,
        placeholders: Array,
        selectedOption: String,
        programmingSkill: String,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },

    data() {
        return {
            isLoading: true,
            preview: {},
            isOpen: false, // This general isOpen can be removed as specific dropdown states are used
            isOpenCategory: false,
            isOpenLibrary: false,
            isOpenDuration: false,
            isOpenFocus: false,
            showPreview: false,
            showPreviewWindow: false,
            isVisible: false,
            TopAssessement: [],
            personalityTests: [],
            programmingTests: [],
            yourAssessment: [],
            allAssessments: [], // This will hold all fetched assessments
            searchPayload: "", // No longer directly used for filtering, but kept if needed elsewhere
            hardSkills: [],
            softSkills: [],
            psychometrics: [],
            selected: [true, false],
            current: 0,
            score: 0,
            filteredAssessments: [], // Initialize as empty, will be populated after data fetch
            premium: false,
            imagePath: "",
            project_id: "",
            message: "",
            isFixed: false,
            scrollThreshold: 350,
            categories: ["Hard Skills", "Interpersonal Skills", "Behavioral Skills", "Personality", "Programming Skills"],
            selectedCategories: [],
            libraries: ["Go Platform", "My library"],
            selectedLibraries: [],
            searchText: "",
            testDurations: ["Up to 8min", "8min to 12min", "12min to 15min"],
            selectedDurations: [],
            testFucus: ["Verbal Reasoning", "Aptitude", "Numerical Reasoning"],
            selectedFocus: [],
            companies: ["KPMG", "PWC", "Workforce", "Adobe"], // This array is not used in filtering logic
            selectedCompanies: [], // This array is not used in filtering logic
            recommendedAssessments: [],
            filterContainerTop: 11,
            debouncedApplyAllFilters: null, // Initialize as null here
        };
    },
    watch: {
        // Watch for changes in searchText and debounce the filtering
        searchText() {
            // Ensure debouncedApplyAllFilters is not null before calling
            if (this.debouncedApplyAllFilters) {
                this.debouncedApplyAllFilters();
            }
        },
        // Watch for changes in selected filter arrays and debounce the filtering
        selectedCategories: {
            handler() {
                if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters();
            },
            deep: true, // Important for arrays
        },
        selectedDurations: {
            handler() {
                if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters();
            },
            deep: true,
        },
        selectedLibraries: {
            handler() {
                if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters();
            },
            deep: true,
        },
        selectedFocus: {
            handler() {
                if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters();
            },
            deep: true,
        },
        selectedCompanies: {
            // Still watched, but not used in applyAllFilters
            handler() {
                if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters();
            },
            deep: true,
        },

        // This watches the *source* of all assessments and triggers initial filtering
        allAssessments: {
            handler() {
                this.getRecommended(); // Recalculate recommended based on allAssessments
                // Call debouncedApplyAllFilters here as well, with null check
                if (this.debouncedApplyAllFilters) {
                    this.debouncedApplyAllFilters();
                }
                this.isLoading = false;
            },
            immediate: true,
            deep: true,
        },
        // Watch for changes in recommendations prop to update recommendedAssessments
        recommendations: {
            handler() {
                this.getRecommended();
            },
            immediate: true,
            deep: true,
        },
    },
    created() {
        // Initialize the debounced function once when the component is created
        this.debouncedApplyAllFilters = debounce(this.applyAllFilters, 300); // 300ms debounce delay
    },
    mounted() {
        this.id = this.$route.query.id;
        this.project_id = this.$route.query.id ? this.$route.query.id : "";
        this.fetchAllAssessments(); // No longer need await here, watchers handle reactivity
        // Add global click listener when component is mounted
        document.addEventListener("click", this.handleClickOutside);
        this.id = this.$route.query.id;
        this.project_id = this.$route.query.id ? this.$route.query.id : "";
        this.fetchAllAssessments(); // Wait for all assessments to be fetched
        // The watchers will handle getRecommended and initial applyAllFilters
    },
    beforeUnmount() {
        // Remove global click listener when component is unmounted to prevent memory leaks
        document.removeEventListener("click", this.handleClickOutside);
    },
    methods: {
        // New method to handle clicks outside dropdowns
        handleClickOutside(event) {
            // Define all filter dropdown elements and their corresponding state
            const filters = [
                { ref: "categoryFilter", isOpen: "isOpenCategory" },
                { ref: "libraryFilter", isOpen: "isOpenLibrary" },
                { ref: "durationFilter", isOpen: "isOpenDuration" },
                { ref: "focusFilter", isOpen: "isOpenFocus" },
            ];

            let clickedInsideAnyFilter = false;
            for (const filter of filters) {
                const element = this.$refs[filter.ref];
                if (element && element.contains(event.target)) {
                    clickedInsideAnyFilter = true;
                    break;
                }
            }

            // If the click was outside all filter elements, close all dropdowns
            if (!clickedInsideAnyFilter) {
                this.closeAllDropdowns();
            }
        },
        toggleFilter() {
            // This method is no longer needed as `isOpen` is not used for dropdowns
            // and handleClickOutside handles closing.
            // Kept for compatibility if other parts of code still use it.
            this.isOpen = !this.isOpen;
        },
        applyAllFilters() {
            this.filteredAssessments = this.allAssessments.filter((assessment) => {
                const searchMatch = assessment.name.toLowerCase().includes(this.searchText.toLowerCase());

                const categoryMatch =
                    this.selectedCategories.length === 0 ||
                    this.selectedCategories
                        .map((cat) => {
                            switch (cat) {
                                case "Hard Skills":
                                    return "Hard Skills";
                                case "Interpersonal Skills":
                                    return "Soft Skills";
                                case "Behavioral Skills":
                                    return "Psychometrics";
                                case "Personality":
                                    return "Personality";
                                case "Programming Skills":
                                    return "Programming Skills";
                                default:
                                    return cat;
                            }
                        })
                        .includes(assessment.category);

                const libraryMatch =
                    this.selectedLibraries.length === 0 ||
                    (this.selectedLibraries.includes("Go Platform") && !assessment.company) ||
                    (this.selectedLibraries.includes("My library") && !!assessment.company);

                const focusMatch = this.selectedFocus.length === 0 || this.selectedFocus.some((focus) => assessment.name.toLowerCase().includes(focus.toLowerCase())); // Case-insensitive focus match

                // Note: selectedCompanies is watched but not used in this filter logic.
                // If you intend to filter by company, you'll need to add logic here.
                const companyMatch =
                    this.selectedCompanies.length === 0 || this.selectedCompanies.some((company) => assessment.company && assessment.company.toLowerCase().includes(company.toLowerCase()));

                const durationMatch = this.checkDuration(assessment);

                return searchMatch && categoryMatch && libraryMatch && focusMatch && companyMatch && durationMatch;
            });
        },

        toggleCategory() {
            this.isOpenCategory = !this.isOpenCategory;
            // Close other dropdowns when one is opened
            this.isOpenLibrary = false;
            this.isOpenDuration = false;
            this.isOpenFocus = false;
        },
        toggleLibrary() {
            this.isOpenLibrary = !this.isOpenLibrary;
            // Close other dropdowns when one is opened
            this.isOpenCategory = false;
            this.isOpenDuration = false;
            this.isOpenFocus = false;
        },
        toggleDuration() {
            this.isOpenDuration = !this.isOpenDuration;
            // Close other dropdowns when one is opened
            this.isOpenCategory = false;
            this.isOpenLibrary = false;
            this.isOpenFocus = false;
        },
        toggleFocus() {
            this.isOpenFocus = !this.isOpenFocus;
            // Close other dropdowns when one is opened
            this.isOpenCategory = false;
            this.isOpenLibrary = false;
            this.isOpenDuration = false;
        },
        closeAllDropdowns() {
            this.isOpenCategory = false;
            this.isOpenLibrary = false;
            this.isOpenDuration = false;
            this.isOpenFocus = false;
        },
        handleScroll() {
            // Added null check for safety, though libraryWrapper should always exist
            if (this.$refs.libraryWrapper) {
                if (this.$refs.libraryWrapper.scrollTop < this.scrollThreshold) {
                    this.filterContainerTop = this.$refs.libraryWrapper.scrollTop + 40;
                } else {
                    this.filterContainerTop = this.$refs.libraryWrapper.scrollTop + 10;
                }
            }
        },
        getRecommended() {
            this.recommendedAssessments = this.allAssessments.filter((assessment) => {
                return this.recommendations.includes(assessment._id);
            });
        },

        checkDuration(assessment) {
            // Ensure assessment.time is used if available, otherwise fallback to questions_nbr logic
            const duration =
                assessment.time !== undefined && assessment.time !== null ? assessment.time : assessment.questions_nbr > 25 ? parseInt((20 * 35) / 60) : parseInt((assessment.questions_nbr * 35) / 60);

            return (
                this.selectedDurations.length === 0 ||
                this.selectedDurations.some((selectedDuration) => {
                    if (selectedDuration === "Up to 8min" && duration <= 8) {
                        return true;
                    } else if (selectedDuration === "8min to 12min" && duration > 8 && duration <= 12) {
                        return true;
                    } else if (selectedDuration === "12min to 15min" && duration > 12 && duration <= 15) {
                        return true;
                    }
                    return false;
                })
            );
        },

        OpenPreview(test) {
            this.preview = test;
            this.togglePreview();
        },
        togglePreview() {
            this.showPreview = !this.showPreview;
        },
        addAssessment(assessment) {
            this.showPreview = false;
            this.addNewAssessment(assessment);
        },

        async fetchAllAssessments() {
            this.isLoading = true; // Set loading to true before fetching
            try {
                const [hardSkillsRes, psychometricsRes, softSkillsRes, topAssessmentsRes, personalityRes, progSkillsRes] = await Promise.all([
                    axios.get(`${BASE_URL}/AssessmentTest/hardSkills`, { withCredentials: true }),
                    axios.get(`${BASE_URL}/AssessmentTest/psychometrics`, { withCredentials: true }),
                    axios.get(`${BASE_URL}/AssessmentTest/softSkills`, { withCredentials: true }),
                    axios.get(`${BASE_URL}/AssessmentTest/topAssessments`, { withCredentials: true }),
                    axios.get(`${BASE_URL}/AssessmentTest/personality`, { withCredentials: true }),
                    axios.get(`${BASE_URL}/AssessmentTest/progSkills`, { withCredentials: true }),
                ]);

                // Concatenate all results into a single array
                this.allAssessments = [...hardSkillsRes.data.hardSkills, ...psychometricsRes.data, ...softSkillsRes.data, ...topAssessmentsRes.data, ...personalityRes.data, ...progSkillsRes.data];

                // Assuming premium status is consistent across responses, or pick one
                if (hardSkillsRes.data.premium !== undefined) {
                    this.Store.setPremium(hardSkillsRes.data.premium);
                }

                // Initial filtering and recommended assessments will be handled by the watchers
            } catch (error) {
                console.error("Error fetching all assessments:", error);
                // Handle error state, e.g., show a message to the user
            } finally {
                this.isLoading = false; // Set loading to false after fetching (success or error)
            }
        },
    },
};
</script>

<style scoped lang="scss">
.library-grid-top {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    width: 100%;
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
}

.checkbox-input {
    margin-right: 5px;
    background-color: #2196f3;
}

.checkbox-text {
    margin-left: 5px;
    font-size: 15px;
    font-weight: 400;
}

.loader {
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-btn {
    background-color: white;
    border: 1.5px solid #e7e7e9;
    padding: 13px 10px;
    border-radius: 50px;
    cursor: pointer;
    font-size: 14px;
    color: #4a5568;
    transition:
        background-color 0.2s,
        color 0.2s;

    display: inline-flex;
    /* Inline-flex to respect content size */
    align-items: center;
    /* Vertically center items */
    gap: 8px;
    /* Space between text and icon */

    max-width: 100%;
    /* Prevent overflow */
    white-space: nowrap;
    /* Avoid multi-line */
}
.filter-btn:hover {
    background-color: #2196f3;
    color: white;
}
/* Tailwind's equivalent for bg-NeonBlue text-white */
.filter-btn.bg-NeonBlue {
    background-color: #2196f3;
    color: white;
}

.dropdown {
    position: absolute;
    top: 110%;
    z-index: 10;
    background: white;
    border: 1px solid #ccc;
    padding: 1rem;
    width: 220px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

input {
    cursor: pointer;
}
</style>
