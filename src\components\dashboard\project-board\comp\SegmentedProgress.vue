<template>
    <div class="segmented-progresscircle">
        <!-- Label above the circle -->
        <div class="segmented-progresscircle__label">{{ label }}</div>

        <!-- Circle with total/maxTotal centered -->
        <div class="segmented-progresscircle__wrapper">
            <svg>
                <defs>
                    <linearGradient id="circleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stop-color="#34d399" />
                        <stop offset="50%" stop-color="#3b82f6" />
                        <stop offset="100%" stop-color="#a78bfa" />
                    </linearGradient>
                </defs>

                <circle r="59" cy="80" cx="80" stroke-width="5" class="segmented-progresscircle__circles__background-dashes" />
                <circle r="59" cy="80" cx="80" stroke-width="6" class="segmented-progresscircle__circles__progress-dashes" :style="animatedStyle" />
            </svg>

            <!-- Centered value -->
            <div class="segmented-progresscircle__center-text">{{ total }}/{{ maxTotal }}</div>
        </div>
    </div>
</template>

<script>
export default {
    name: "SegmentedProgressCircle",
    props: {
        total: { type: Number, default: 3 },
        maxTotal: { type: Number, default: 10 },
        average: { type: Number, default: 5 },
        label: { type: String, default: "Decision making" },
    },
    data() {
        return {
            currentAverage: 0,
        };
    },
    computed: {
        animatedStyle() {
            const segmentLength = 27;
            const spacing = 10;
            const full = this.maxTotal;
            const active = Math.min(this.currentAverage, full);

            let dashArray = "";
            for (let i = 0; i < full; i++) {
                dashArray += i < active ? `${segmentLength} ${spacing} ` : `0 ${segmentLength + spacing} `;
            }

            return {
                strokeDasharray: dashArray.trim(),
                stroke: "url(#circleGradient)",
                strokeDashoffset: 0,
                transition: "stroke-dasharray 0.6s ease-out",
            };
        },
    },
    mounted() {
        this.animateProgress();
    },
    methods: {
        animateProgress() {
            const duration = 1000; // ms
            const frameRate = 1000 / 60;
            const totalFrames = Math.round(duration / frameRate);
            let currentFrame = 0;

            const animation = setInterval(() => {
                currentFrame++;
                const progressEase = this.easeOutCubic(currentFrame / totalFrames);
                this.currentAverage = Math.round(this.average * progressEase);

                if (currentFrame >= totalFrames) {
                    clearInterval(animation);
                }
            }, frameRate);
        },
        easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        },
    },
};
</script>
<style scoped lang="scss">
.segmented-progresscircle {
    display: flex;
    flex-direction: column;
    align-items: center;

    &__label {
        font-size: 14px;
        font-weight: 600;
        color: #3e3e3e;
        margin-bottom: 10px;
    }

    &__wrapper {
        position: relative;
        width: 160px;
        height: 160px;

        svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
        }

        circle {
            fill: transparent;
        }

        .segmented-progresscircle__circles__progress-dashes {
            stroke-linecap: round;
            filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.5));
            transition:
                stroke-dasharray 0.6s ease-in-out,
                stroke 0.3s ease;

            &:hover {
                animation: pulseGlow 1.5s infinite;
            }
        }

        .segmented-progresscircle__circles__background-dashes {
            stroke-linecap: round;
            stroke: #ddd;
            stroke-dasharray: 27 10 27 10 27 10 27 10 27 10 27 10 27 10 27 10 27 10 27 10 27 10;
        }

        .segmented-progresscircle__center-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 20px;
            font-weight: bold;
            color: #3e3e3e;
            text-align: center;
        }
    }
}

@keyframes pulseGlow {
    0% {
        filter: drop-shadow(0 0 4px rgba(99, 102, 241, 0.3));
    }
    50% {
        filter: drop-shadow(0 0 12px rgba(99, 102, 241, 0.7));
    }
    100% {
        filter: drop-shadow(0 0 4px rgba(99, 102, 241, 0.3));
    }
}
</style>
