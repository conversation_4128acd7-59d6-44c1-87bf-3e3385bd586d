/* eslint-disable */
<template>
    <ConfirmEmail :isVisible="showConfirm" :toggleConfirmEmail="toggleConfirmEmail" />
    <div class="absolute top-0 left-0 w-full h-full bg-white z-50">
        <ToastNotification :message="message" :isVisible="isVisible" bgColor="red" />
        <div class="flex h-screen w-full">
            <!-- Left Form Section -->
            <div class="w-full lg:w-1/2 p-8 lg:p-12 overflow-y-auto bg-white">
                <div class="max-w-[300px] lg:max-w-[400px] mx-auto">
                    <!-- Logo & Title -->
                    <div class="w-full flex flex-col items-center gap-4 mb-8">
                        <router-link to="/" class="hover:opacity-90 transition-opacity duration-200">
                            <!--<img loading="lazy" decoding="async" src="@/assets/Images/go_logo.svg" alt="Recruitable" class="max-w-[180px] mb-8 mx-auto" />-->
                            <img :src="logoSrc" class="max-w-[180px] mb-8 mx-auto" :alt="logoAlt" />
                        </router-link>
                        <h2 class="text-lg md:text-2xl font-bold text-gray-800 text-center">{{ $t("Create Your Free Account") }}</h2>
                        <p class="text-gray-500 text-base">{{ $t("Start your recruitment journey with us") }}</p>
                    </div>

                    <!-- Combined Email & Password Step -->
                    <div v-show="currentStep === 1" class="space-y-6">
                        <div class="space-y-4">
                            <div class="w-full">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <input
                                        type="email"
                                        name="registerEmail"
                                        autocomplete="email"
                                        v-model="email"
                                        :placeholder="$t('Work email')"
                                        class="w-full px-4 py-3 pl-10 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                        :class="{ 'border-red-500': errors.email }"
                                    />
                                </div>
                                <p v-if="errors.email" class="text-red-500 text-sm mt-1 text-left">{{ errors.email }}</p>
                            </div>

                            <div class="w-full">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                        />
                                    </svg>
                                    <input
                                        :type="showPassword ? 'text' : 'password'"
                                        name="newPassword"
                                        autocomplete="new-password"
                                        v-model="password"
                                        :placeholder="$t('Create password')"
                                        class="w-full px-4 py-3 pl-10 pr-10 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                        :class="{ 'border-red-500': errors.password }"
                                        @input="validatePassword"
                                    />
                                    <button type="button" @click="showPassword = !showPassword" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 focus:outline-none">
                                        <i :class="['far', showPassword ? 'fa-eye-slash' : 'fa-eye']"></i>
                                    </button>
                                </div>
                                <p v-if="errors.password" class="text-red-500 text-sm mt-1 text-left">{{ errors.password }}</p>
                                <p v-if="showPasswordRequirementMessage" class="text-red-500 text-xs mt-1 text-left">
                                    <font-awesome-icon :icon="['far', 'circle-xmark']" class="text-xs" />
                                    {{ $t("Password must contain at least 8 characters with uppercase letters (A...Z), lowercase letters (a...z), numbers (1...9), and special characters (!...$).") }}
                                </p>
                            </div>

                            <div class="w-full">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                        />
                                    </svg>
                                    <input
                                        :type="showConfirmPassword ? 'text' : 'password'"
                                        name="confirmPassword"
                                        autocomplete="new-password"
                                        v-model="confirmPassword"
                                        :placeholder="$t('Confirm password')"
                                        class="w-full px-4 py-3 pl-10 pr-10 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                        :class="{ 'border-red-500': errors.confirmPassword }"
                                    />
                                    <button type="button" @click="showConfirmPassword = !showConfirmPassword" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 focus:outline-none">
                                        <i :class="['far', showConfirmPassword ? 'fa-eye-slash' : 'fa-eye']"></i>
                                    </button>
                                </div>
                                <p v-if="errors.confirmPassword" class="text-red-500 text-sm mt-1 text-left">{{ errors.confirmPassword }}</p>
                            </div>
                        </div>

                        <!-- Terms & Privacy -->
                        <div class="space-y-2">
                            <p class="text-sm text-gray-500">
                                {{ $t("By clicking Create Account, you agree to our") }}
                                <a href="/Terms-of-use" class="text-blue-600 hover:underline font-medium">{{ $t("Terms of Service") }}</a> &
                                <a href="/privacy-policy" class="text-blue-600 hover:underline font-medium">{{ $t("Privacy Policy") }}</a>
                            </p>
                        </div>

                        <button @click="submitAccount" class="w-full bg-NeonBlue text-white text-lg py-3 rounded-md font-semibold hover:opacity-85">{{ $t("Create Account") }}</button>
                    </div>

                    <div class="mt-8 text-gray-600">
                        {{ $t("Already have an account?") }}
                        <router-link to="/login" class="text-NeonBlue font-semibold hover:opacity-85 hover:underline">
                            {{ $t("Sign in") }}
                        </router-link>
                    </div>
                </div>
            </div>

            <!-- Right Graphic Section -->
            <div class="hidden lg:block w-1/2 h-screen bg-NeonBlue overflow-hidden">
                <div class="h-full flex flex-col items-center justify-center p-12 text-white">
                    <h2 class="text-4xl font-bold mb-6 text-center leading-tight">{{ $t("Find Top Talent Faster") }}</h2>
                    <p class="text-lg text-center mb-8 opacity-90">{{ $t("Join companies worldwide hiring smarter with our AI-powered recruitment platform") }}</p>

                    <img src="@/assets/Images/Automated-Workflow.png" alt="Automated Workflow" class="w-full max-w-xs mx-auto" />
                </div>
            </div>
        </div>

        <!-- Details Popup -->
        <div v-if="showDetailsPopup" class="fixed inset-0 backdrop-blur-xl bg-white/95 flex items-center justify-center p-4 z-50">
            <div class="bg-white rounded-xl p-12 max-w-6xl w-full min-h-[450px] border shadow-2xl flex flex-col">
                <!-- Popup Header -->
                <div class="mb-8 text-left">
                    <h2 class="text-2xl font-bold text-gray-900">{{ $t("My Profile") }}</h2>
                    <h3 class="text-lg font-semibold text-gray-800 mt-4">{{ $t("Personal Information") }}</h3>
                    <p class="text-gray-500 text-sm mt-1">{{ $t("Update your personal details") }}</p>
                </div>
                <!-- Form Content -->
                <div class="flex-1 flex flex-col justify-center">
                    <div class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <input
                                    type="text"
                                    v-model="firstName"
                                    :placeholder="$t('First name')"
                                    class="w-full px-4 py-3 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                    :class="{ 'border-red-500': errors.firstName }"
                                />
                                <p v-if="errors.firstName" class="text-red-500 text-sm mt-1 text-left">{{ errors.firstName }}</p>
                            </div>
                            <div>
                                <input
                                    type="text"
                                    v-model="lastName"
                                    :placeholder="$t('Last name')"
                                    class="w-full px-4 py-3 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                    :class="{ 'border-red-500': errors.lastName }"
                                />
                                <p v-if="errors.lastName" class="text-red-500 text-sm mt-1 text-left">{{ errors.lastName }}</p>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <input
                                    type="text"
                                    v-model="companyName"
                                    :placeholder="$t('Company name')"
                                    class="w-full px-4 py-3 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                    :class="{ 'border-red-500': errors.companyName }"
                                />
                                <p v-if="errors.companyName" class="text-red-500 text-sm mt-1 text-left">{{ errors.companyName }}</p>
                            </div>
                            <div>
                                <div class="relative">
                                    <select
                                        v-model="companySize"
                                        class="w-full px-4 py-3 border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200 bg-white appearance-none"
                                        :class="{ 'border-red-500': errors.companySize }"
                                    >
                                        <option value="" disabled selected>{{ $t("Company size") }}</option>
                                        <option v-for="(size, index) in companySizes" :key="index" :value="size">
                                            {{ size }}
                                        </option>
                                    </select>
                                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                        <font-awesome-icon :icon="['fas', 'chevron-down']" class="text-gray-400 text-sm" />
                                    </div>
                                </div>
                                <p v-if="errors.companySize" class="text-red-500 text-sm mt-1 text-left">
                                    {{ errors.companySize }}
                                </p>
                            </div>
                        </div>

                        <div class="mt-auto pt-6 flex justify-end">
                            <button @click="submitProfile" class="w-1/6 py-3 bg-NeonBlue text-white rounded-md font-semibold hover:opacity-85">{{ $t("Complete") }}</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ConfirmEmail from "./ConfirmEmail.vue";
import ToastNotification from "./ToastNotification.vue";
import { BASE_URL } from "@/constants";
import axios from "axios";
import { computed } from "vue";
import { useGeolocationStore } from "@/store/geolocation";

export default {
    components: {
        ConfirmEmail,
        ToastNotification,
    },
    data() {
        return {
            currentStep: 1,
            showDetailsPopup: false,
            showPasswordRequirementMessage: false,
            email: "",
            password: "",
            confirmPassword: "",
            firstName: "",
            lastName: "",
            companyName: "",
            companySize: "",
            companySizes: ["1-10", "11-50", "51-200", "201-500", "501-1000", "1000+"],
            showPassword: false,
            showConfirmPassword: false,
            showConfirm: false,
            errors: {},
            message: "",
            isVisible: false,
            verificationToken: null,
        };
    },
    setup() {
        const geolocationStore = useGeolocationStore();

        const logoSrc = computed(() => {
            return geolocationStore.country === "DZ" ? require("@/assets/GoProfiling-logo.svg") : require("@/assets/Images/go_logo.svg");
        });

        const logoAlt = computed(() => {
            return geolocationStore.country === "DZ" ? "Go Profiling & Testing Logo" : "Go Platform Logo";
        });

        return { logoSrc, logoAlt };
    },
    mounted() {
        // Check for verification token in URL
        const urlParams = new URLSearchParams(window.location.search);
        this.verificationToken = urlParams.get("token");

        if (this.verificationToken) {
            this.verifyEmailToken();
        }
    },
    methods: {
        validatePassword() {
            const password = this.password;

            if (password.length > 0) {
                this.showPasswordRequirementMessage = true;
            }

            const passwordCriteria = {
                minLength: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /[0-9]/.test(password),
                specialCharacter: /[!@#$%^&*|/?]/.test(password),
            };

            const allCriteriaMet = Object.values(passwordCriteria).every(Boolean);

            if (allCriteriaMet) {
                this.showPasswordRequirementMessage = false;
            }

            // Clear any previous password error before re-validating.
            delete this.errors.password;

            // Check if password contains the user's email address.
            if (this.email && password.toLowerCase().includes(this.email.toLowerCase())) {
                this.errors.password = this.$t("Password should not contain your email address");
                return false; // Return false because there is a specific error.
            }

            // If not all criteria are met, we just return false.
            // This will block the submission without setting a generic error message.
            if (!allCriteriaMet) {
                return false;
            }

            // If all checks pass, return true.
            return true;
        },

        async submitAccount() {
            this.errors = {};
            if (!this.email) this.errors.email = this.$t("Email is required");
            if (!this.password) this.errors.password = this.$t("Password is required");
            if (!this.confirmPassword) this.errors.confirmPassword = this.$t("Confirm password is required");
            if (this.password !== this.confirmPassword) this.errors.confirmPassword = this.$t("Passwords do not match");

            if (Object.keys(this.errors).length > 0) return;

            try {
                this.showConfirm = true;

                await axios
                    .post(
                        `${BASE_URL}/company/registerAccount`,
                        {
                            email: this.email,
                            password: this.password,
                        },
                        { withCredentials: true },
                    )
                    .then((response) => {
                        console.log("response");
                        console.log(response);
                        console.log("response");
                        // show profile completion popup after successful verification
                        this.showDetailsPopup = true;

                        // slear the token from URL
                    });
            } catch (error) {
                this.message = error.response?.data?.message || "Registration failed";
                this.isVisible = true;
                setTimeout(() => (this.isVisible = false), 3000);
            }
        },

        async verifyEmailToken() {
            try {
                await axios.post("", {
                    token: this.verificationToken,
                });

                // show profile completion popup after successful verification
                this.showDetailsPopup = true;

                // slear the token from URL
                window.history.replaceState({}, document.title, window.location.pathname);
            } catch (error) {
                this.message = error.response?.data?.message || "Email verification failed";
                this.isVisible = true;
                setTimeout(() => (this.isVisible = false), 3000);
            }
        },

        async submitProfile() {
            this.errors = {};
            if (!this.firstName) this.errors.firstName = "First name is required";
            if (!this.lastName) this.errors.lastName = "Last name is required";
            if (!this.companyName) this.errors.companyName = "Company name is required";
            if (!this.companySize) this.errors.companySize = "Please select company size";

            if (Object.keys(this.errors).length > 0) return;

            try {
                await axios.post("", {
                    firstName: this.firstName,
                    lastName: this.lastName,
                    company: {
                        name: this.companyName,
                        size: this.companySize,
                    },
                });

                this.showDetailsPopup = false;
                this.$router.push("/login");
            } catch (error) {
                this.message = error.response?.data?.message || "Profile completion failed";
                this.isVisible = true;
                setTimeout(() => (this.isVisible = false), 3000);
            }
        },

        toggleConfirmEmail() {
            this.showConfirm = !this.showConfirm;
            this.$router.push("/login");
        },
    },
};
</script>

<style scoped>
input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
    display: none;
}
input[type="password"]::-webkit-credentials-auto-fill-button {
    display: none !important;
}
input[type="password"]::-webkit-input-decoration {
    display: none !important;
}
input[type="password"]::-webkit-input-password-toggle-button {
    display: none !important;
}
input[type="password"]::-webkit-input-suffix {
    display: none !important;
}
</style>
