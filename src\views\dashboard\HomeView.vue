<template>
    <div v-if="showContent" class="container mx-auto p-8">
        <!-- Header Section -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold mb-2">{{ $t("Welcome", { userName }) }}</h1>
                <p class="text-gray-600">{{ $t("Find your next hire with skill-based assessments.") }}</p>
            </div>
            <button
                class="btn-primary flex items-center gap-2"
                @click="
                    () => {
                        this.$router.push('/newAssessment');
                    }
                "
            >
                <font-awesome-icon :icon="['fas', 'plus']" />
                {{ $t("Create Assessment") }}
            </button>
        </div>

        <!-- Main Content -->
        <div class="flex gap-8">
            <!-- Left Container -->
            <div class="w-2/5 border-2 rounded-lg p-6 bg-white">
                <h2 class="text-lg font-semibold mb-4">{{ $t("Jumpstart hiring! Discover top talent fast") }}</h2>

                <!-- Progress Section -->
                <div class="mb-6">
                    <div class="flex justify-between mb-2">
                        <span class="text-sm">{{ $t("Progress") }}</span>
                        <span class="text-sm">{{ progress }}%</span>
                    </div>
                    <div class="h-2 bg-gray-200 rounded-full">
                        <div class="h-full bg-custom-gradient rounded-full transition-all duration-300" :style="{ width: progress + '%' }"></div>
                    </div>
                </div>

                <div class="border-b mb-6"></div>

                <!-- Steps List -->
                <div class="space-y-4">
                    <div v-for="(step, index) in steps" :key="index" class="flex items-center justify-between p-3 hover:bg-blue-50 rounded-lg cursor-pointer" @click="selectStep(index)">
                        <div class="flex items-center gap-3">
                            <font-awesome-icon v-if="step.completed" :icon="['fas', 'check-circle']" class="w-5 h-5 text-NeonBlue" />
                            <span class="text-gray-700">{{ $t(step.title) }}</span>
                        </div>
                        <font-awesome-icon :icon="['fas', 'chevron-right']" class="w-4 h-4 text-gray-400" />
                    </div>
                </div>
            </div>

            <!-- Right Container -->
            <div class="w-3/5 border-2 rounded-lg p-6 bg-white">
                <div v-if="selectedStep !== null">
                    <h3 class="text-lg font-semibold mb-4">{{ $t(steps[selectedStep].title) }}</h3>

                    <!-- Image Steps with Blue Container -->
                    <div v-if="steps[selectedStep].image" class="bg-blue-50 rounded-lg border border-blue-100 p-8 mb-6">
                        <img :src="steps[selectedStep].image" alt="Step illustration" class="w-full mb-8" />
                        <button class="btn-primary flex items-center gap-2 w-full justify-center" @click="navigateToStep">
                            <font-awesome-icon :icon="['fas', steps[selectedStep].buttonIcon]" />
                            {{ $t(steps[selectedStep].buttonText) }}
                        </button>
                    </div>

                    <!-- Video Steps (Full Space) -->
                    <div v-if="steps[selectedStep].video" class="h-full">
                        <div class="relative h-full rounded-md overflow-hidden">
                            <video :src="steps[selectedStep].video" class="w-full h-full object-cover" controls controlsList="nodownload" @contextmenu.prevent @ended="markVideoCompleted"></video>
                        </div>
                        <button v-if="steps[selectedStep].buttonText" class="btn-primary mt-4 flex items-center gap-2 w-full justify-center" @click="navigateToStep">
                            <font-awesome-icon :icon="['fas', steps[selectedStep].buttonIcon]" />
                            {{ $t(steps[selectedStep].buttonText) }}
                        </button>
                    </div>
                </div>
                <div v-else class="text-gray-500 h-full flex items-center justify-center">{{ $t("Select a step to view details") }}</div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { useStore } from "@/store/index";
import { BASE_URL } from "@/constants";

export default {
    name: "HomeView",
    data() {
        return {
            recruiter: {
                first_name: "",
            },
            newRecruiter: {},
            requiredFields: {},
            showPassword: false,
            showNewPassword: false,
            isVisible: false,
            message: "",
            bgc: "",
            userName: "John Doe",
            selectedStep: null,
            showContent: false,
            steps: [
                {
                    title: "Complete your setup",
                    completed: localStorage.getItem("step-0") === "true",
                    image: require(`@/assets/Images/CompleteSetup.png`),
                    buttonText: "Complete Setup",
                    buttonIcon: "user-cog",
                    routeName: "companyProfile",
                },
                {
                    title: "Watch the intro video",
                    completed: localStorage.getItem("step-1") === "true",
                    video: require(`@/assets/Videos/IntroVideo.mp4`),
                },
                {
                    title: "Create your first assessment",
                    completed: localStorage.getItem("step-2") === "true",
                    image: require(`@/assets/Images/CreateAssessment.png`),
                    buttonText: "Start Creating",
                    buttonIcon: "edit",
                    routeName: "NewAssessement",
                },
                {
                    title: "Invite candidates",
                    completed: localStorage.getItem("step-3") === "true",
                    image: require(`@/assets/Images/InviteCandidates.png`),
                    buttonText: "Invite Candidates",
                    buttonIcon: "user-plus",
                    routeName: "dashboard",
                },
                {
                    title: "Shortlist top candidates",
                    completed: localStorage.getItem("step-4") === "true",
                    video: require(`@/assets/Videos/HowToEvaluateAssessmentResults.mp4`),
                },
            ],
        };
    },
    created() {
        //this.redirectOnbording();
    },
    computed: {
        progress() {
            const completedSteps = this.steps.filter((step) => step.completed).length;
            return (completedSteps / this.steps.length) * 100;
        },
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    async mounted() {
        this.redirectOnbording();
        await this.getUser();
    },
    methods: {
        redirectOnbording() {
            const completedSteps = this.steps.filter((step) => step.completed).length;
            if ((completedSteps / this.steps.length) * 100 >= 50) {
                this.$router.push("/dashboard");
            } else {
                this.showContent = true;
            }
        },
        async getUser() {
            try {
                axios
                    .get(`${BASE_URL}/user/info`, {
                        withCredentials: true,
                    })
                    .then((res) => {
                        this.Store.setCompanyName(this.$route.query.company_name);
                        this.user = res.data.userInfo;
                        this.recruiter = res.data.userInfo;
                        this.newRecruiter.first_name = res.data.userInfo.first_name;
                        this.newRecruiter.last_name = res.data.userInfo.last_name;
                        this.userName = this.newRecruiter.first_name + " " + this.newRecruiter.last_name;
                        this.newRecruiter.phone_nbr = res.data.userInfo.phone_nbr;
                        this.Store.getCompanyCredit();
                    })
                    .catch((err) => {
                        console.log(err);
                        // Handle error if the token is invalid or expired
                        //this.$router.push('/');
                    });
            } catch (error) {
                console.error(error);
            }
        },

        selectStep(index) {
            this.selectedStep = index;
        },
        navigateToStep() {
            const step = this.steps[this.selectedStep];
            // Mark step as completed
            this.steps[this.selectedStep].completed = true;
            // Save to localStorage
            localStorage.setItem(`step-${this.selectedStep}`, "true");

            if (step.routeName) {
                this.$router.push({ name: step.routeName });
            }
        },
        markVideoCompleted() {
            this.steps[this.selectedStep].completed = true;
            localStorage.setItem(`step-${this.selectedStep}`, "true");
        },
    },
};
</script>

<style scoped>
.btn-primary {
    background-color: #2196f3;
    color: white;
    padding: 12px 16px;
    font-weight: 500;
    border-radius: 6px;
    transition: opacity 0.3s ease;
}

.btn-primary:hover {
    opacity: 0.85;
}

.border-b {
    border-bottom: 1px solid #e5e7eb;
}
</style>
