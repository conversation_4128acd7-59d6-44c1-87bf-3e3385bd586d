<template>
    <div class="main">
        <div class="w-full flex flex-col lg:grid grid-cols-2 justify-items-stretch gap-y-10 py-8 mt-8">
            <div class="box">
                <h5 class="text-left text-[#ffce39]">{{ $t("REQUEST A SERVICE") }}</h5>
                <h1 class="text-white text-4xl my-5">{{ $t("See go platform in action") }}</h1>
                <p class="text-white text-xl mb-5">{{ $t("Want to take your hiring to the next level? Let's chat.") }}</p>

                <ul>
                    <li class="list">
                        <span class="font-aws"><i class="fa-regular fa-circle-check"></i></span><span>{{ $t("Improved quality of hire") }}</span>
                    </li>

                    <li class="list">
                        <span class="font-aws"><i class="fa-regular fa-circle-check"></i></span><span>{{ $t("Faster candidate screening") }}</span>
                    </li>

                    <li class="list">
                        <span class="font-aws"><i class="fa-regular fa-circle-check"></i></span><span>{{ $t("Increased hiring diversity") }}</span>
                    </li>

                    <li class="list">
                        <span class="font-aws"><i class="fa-regular fa-circle-check"></i></span><span>{{ $t("Improved candidate experience") }}</span>
                    </li>

                    <li class="list">
                        <span class="font-aws"><i class="fa-regular fa-circle-check"></i></span><span>{{ $t("Lower employee attrition") }}</span>
                    </li>

                    <li class="list">
                        <span class="font-aws"><i class="fa-regular fa-circle-check"></i></span><span>{{ $t("Instant AI-powered grading and ranking") }}</span>
                    </li>
                </ul>

                <div clas="flex gap-8" style="display: inline-flex">
                    <div class="flex gap-2 items-center" style="display: inline-flex">
                        <img src="@/assets/google_white.svg" style="width: 55px; border-right: 1px solid white" />
                        <span>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                        </span>
                    </div>

                    <div class="flex gap-2 items-center">
                        <img src="https://img.icons8.com/?size=100&id=82471&format=png&color=FFFFFF" style="width: 55px; padding-right: 6px; border-right: 1px solid white" />

                        <span style="">
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                            <i style="color: yellow; font-size: 20px; padding-right: 2px" class="fa-solid fa-star"></i>
                        </span>
                    </div>
                </div>
            </div>

            <div class="box2">
                <span>{{ $t("Work Email") }}</span
                ><span class="required">*</span>
                <input name="email" id="email" :placeholder="$t('Business Email')" type="email" class="hs-input invalid error" inputmode="email" autocomplete="email" value="" />
                <span class="err-email err"></span>

                <div class="w-full flex flex-col lg:grid grid-cols-2 justify-items-stretch gap-10 gap-y-6 mt-2">
                    <div>
                        <span>{{ $t("First Name") }}</span
                        ><span class="required">*</span>
                        <input name="first" id="first" :placeholder="$t('Frist Name')" type="text" class="hs-input invalid error" inputmode="email" autocomplete="email" value="" />
                        <span class="err-fn err"></span>
                    </div>

                    <div>
                        <span>{{ $t("Last Name") }}</span
                        ><span class="required">*</span>
                        <input name="last" id="last" :placeholder="$t('Last Name')" type="text" class="hs-input invalid error" inputmode="email" autocomplete="email" value="" />
                        <span class="err-last err"></span>
                    </div>
                </div>

                <div class="w-full flex flex-col lg:grid grid-cols-2 justify-items-stretch gap-10 gap-y-6 mt-2">
                    <div>
                        <span>{{ $t("Job Title") }}</span
                        ><span class="required">*</span>
                        <input name="jbt" id="jbt" :placeholder="$t('Job Title')" type="text" class="hs-input invalid error" inputmode="email" autocomplete="email" value="" />
                        <span class="err-jbt err"></span>
                    </div>

                    <div>
                        <span>{{ $t("Company Name") }}</span
                        ><span class="required">*</span>
                        <input name="cmn" id="cmn" :placeholder="$t('Company Name')" type="text" class="hs-input invalid error" inputmode="email" autocomplete="email" value="" />
                        <span class="err-cmn err"></span>
                    </div>
                </div>

                <div class="mt-2">
                    <span>{{ $t("Direct Phone number") }}</span
                    ><span class="required">*</span>
                </div>
                <p class="text-gray-500 text-sm">{{ $t("Please include your country and area code") }}</p>
                <div>
                    <vue-tel-input v-model="tel" id="phone" mode="international"></vue-tel-input>
                    <span class="err-tel err"></span>

                    <div class="mt-2">
                        <span>{{ $t("Country") }}</span
                        ><span class="required">*</span>
                        <select v-model="selectedCountry" id="people-select">
                            <option class="option" value="" disabled selected>{{ $t("Please Select") }}</option>
                            <option v-for="(country, index) in countries" :key="index" :value="country">
                                {{ country }}
                            </option>
                        </select>
                        <span class="err-cn err"></span>
                    </div>

                    <div class="mt-2">
                        <span>{{ $t("How many people does your company hire per year?") }}</span>
                        <select v-model="selectedPeople" id="people-select">
                            <option class="option" value="" disabled selected>{{ $t("Please Select") }}</option>
                            <option value="1-10">1-10</option>
                            <option value="11-50">11-50</option>
                            <option value="51-100">51-100</option>
                            <option value="101-500">101-500</option>
                            <option value="500+">500+</option>
                        </select>
                        <span class="err-np err"></span>
                    </div>

                    <div class="mt-2">
                        <span>{{ $t("Number of employees in your company") }}</span
                        ><span class="required">*</span>
                        <select v-model="selectedEm" id="people-select">
                            <option class="option" value="" disabled selected>{{ $t("Please Select") }}</option>
                            <option value="1-10">1-10</option>
                            <option value="11-50">11-50</option>
                            <option value="51-100">51-100</option>
                            <option value="101-500">101-500</option>
                            <option value="500+">500+</option>
                        </select>
                        <span class="err-ne err"></span>
                    </div>
                    <p class="mt-8 text-gray-500 text-sm">
                        {{ $t("By submitting your information, you agree to ") }}<a href="#">{{ $t("our Terms of Use") }}</a
                        >{{ $t(" and ") }}<a href="#">{{ $t("Privacy Policy") }}</a
                        >{{ $t(". You can opt-out anytime.") }}
                    </p>

                    <div class="mt-12">
                        <button class="text-white px-6 py-4 text-lg font-normal leading-4 bg-NeonBlue rounded-md" @click="validateForm()">{{ $t("Request Service") }}</button>
                        <br />
                        <span id="sucess" style="color: green"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { VueTelInput } from "vue3-tel-input";
import "vue3-tel-input/dist/vue3-tel-input.css";
import { BASE_URL } from "@/constants";
import axios from "axios";
//import VueCountrySelect from 'vue-country-select';
//import countryList from 'country-list';
export default {
    data() {
        return {
            phone: "",
            country: "",

            selectedCountry: "",
            selectedPeople: "",
            selectedEm: "",
            countries: [
                "Afghanistan",
                "Albania",
                "Algeria",
                "Andorra",
                "Angola",
                "Antigua and Barbuda",
                "Argentina",
                "Armenia",
                "Australia",
                "Austria",
                "Azerbaijan",
                "Bahamas",
                "Bahrain",
                "Bangladesh",
                "Barbados",
                "Belarus",
                "Belgium",
                "Belize",
                "Benin",
                "Bhutan",
                "Bolivia",
                "Bosnia and Herzegovina",
                "Botswana",
                "Brazil",
                "Brunei",
                "Bulgaria",
                "Burkina Faso",
                "Burundi",
                "Cabo Verde",
                "Cambodia",
                "Cameroon",
                "Canada",
                "Central African Republic",
                "Chad",
                "Chile",
                "China",
                "Colombia",
                "Comoros",
                "Congo (Congo-Brazzaville)",
                "Costa Rica",
                "Croatia",
                "Cuba",
                "Cyprus",
                "Czechia (Czech Republic)",
                "Democratic Republic of the Congo",
                "Denmark",
                "Djibouti",
                "Dominica",
                "Dominican Republic",
                "Ecuador",
                "Egypt",
                "El Salvador",
                "Equatorial Guinea",
                "Eritrea",
                "Estonia",
                "Eswatini (fmr. Swaziland)",
                "Ethiopia",
                "Fiji",
                "Finland",
                "France",
                "Gabon",
                "Gambia",
                "Georgia",
                "Germany",
                "Ghana",
                "Greece",
                "Grenada",
                "Guatemala",
                "Guinea",
                "Guinea-Bissau",
                "Guyana",
                "Haiti",
                "Honduras",
                "Hungary",
                "Iceland",
                "India",
                "Indonesia",
                "Iran",
                "Iraq",
                "Ireland",
                "Italy",
                "Jamaica",
                "Japan",
                "Jordan",
                "Kazakhstan",
                "Kenya",
                "Kiribati",
                "Kuwait",
                "Kyrgyzstan",
                "Laos",
                "Latvia",
                "Lebanon",
                "Lesotho",
                "Liberia",
                "Libya",
                "Liechtenstein",
                "Lithuania",
                "Luxembourg",
                "Madagascar",
                "Malawi",
                "Malaysia",
                "Maldives",
                "Mali",
                "Malta",
                "Marshall Islands",
                "Mauritania",
                "Mauritius",
                "Mexico",
                "Micronesia",
                "Moldova",
                "Monaco",
                "Mongolia",
                "Montenegro",
                "Morocco",
                "Mozambique",
                "Myanmar (formerly Burma)",
                "Namibia",
                "Nauru",
                "Nepal",
                "Netherlands",
                "New Zealand",
                "Nicaragua",
                "Niger",
                "Nigeria",
                "North Korea",
                "North Macedonia",
                "Norway",
                "Oman",
                "Pakistan",
                "Palau",
                "Palestine State",
                "Panama",
                "Papua New Guinea",
                "Paraguay",
                "Peru",
                "Philippines",
                "Poland",
                "Portugal",
                "Qatar",
                "Romania",
                "Russia",
                "Rwanda",
                "Saint Kitts and Nevis",
                "Saint Lucia",
                "Saint Vincent and the Grenadines",
                "Samoa",
                "San Marino",
                "Sao Tome and Principe",
                "Saudi Arabia",
                "Senegal",
                "Serbia",
                "Seychelles",
                "Sierra Leone",
                "Singapore",
                "Slovakia",
                "Slovenia",
                "Solomon Islands",
                "Somalia",
                "South Africa",
                "South Korea",
                "South Sudan",
                "Spain",
                "Sri Lanka",
                "Sudan",
                "Suriname",
                "Sweden",
                "Switzerland",
                "Syria",
                "Tajikistan",
                "Tanzania",
                "Thailand",
                "Timor-Leste",
                "Togo",
                "Tonga",
                "Trinidad and Tobago",
                "Tunisia",
                "Turkey",
                "Turkmenistan",
                "Tuvalu",
                "Uganda",
                "Ukraine",
                "United Arab Emirates",
                "United Kingdom",
                "United States of America",
                "Uruguay",
                "Uzbekistan",
                "Vanuatu",
                "Vatican City",
                "Venezuela",
                "Vietnam",
                "Yemen",
                "Zambia",
                "Zimbabwe",
            ],
            err: false,
        };
    },
    components: {
        VueTelInput,
        //VueCountrySelect
    },
    methods: {
        validateForm() {
            //init
            this.err = false;
            document.querySelector(".err-email").textContent = "";
            document.querySelector(".err-fn").textContent = "";
            document.querySelector(".err-last").textContent = "";
            document.querySelector(".err-jbt").textContent = "";
            document.querySelector(".err-cmn").textContent = "";
            document.querySelector(".err-cn").textContent = "";
            document.querySelector(".err-np").textContent = "";
            document.querySelector(".err-ne").textContent = "";
            document.querySelector(".err-tel").textContent = "";
            document.querySelector("#sucess").textContent = "";
            //email
            const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
            let email = document.getElementById("email").value;
            if (email == "") {
                document.querySelector(".err-email").textContent = "Cannot be Empty";
                this.err = true;
            } else {
                if (!emailPattern.test(email)) {
                    document.querySelector(".err-email").textContent = "Please enter a valid email address.";
                    this.err = true;
                }
            }

            //first-name
            let first = document.getElementById("first").value;
            if (first == "") {
                document.querySelector(".err-fn").textContent = "Cannot be Empty";
                this.err = true;
            }

            //last-name
            let last = document.getElementById("last").value;
            if (last == "") {
                document.querySelector(".err-last").textContent = "Cannot be Empty";
                this.err = true;
            }

            //job-title
            let jbt = document.getElementById("jbt").value;
            if (jbt == "") {
                document.querySelector(".err-jbt").textContent = "Cannot be Empty";
                this.err = true;
            }

            //company-name
            let cmn = document.getElementById("cmn").value;
            if (cmn == "") {
                document.querySelector(".err-cmn").textContent = "Cannot be Empty";
                this.err = true;
            }

            //country
            if (this.selectedCountry == "") {
                document.querySelector(".err-cn").textContent = "Cannot be Empty";
                this.err = true;
            }

            //people
            if (this.selectedPeople == "") {
                document.querySelector(".err-np").textContent = "Cannot be Empty";
                this.err = true;
            }

            //employee
            if (this.selectedEm == "") {
                document.querySelector(".err-ne").textContent = "Cannot be Empty";
                this.err = true;
            }

            //tel
            var tel2 = document.querySelector(".vti__input").value.split(" ").join("");
            const phoneNumberRegex = /^\+(\d{1,4})[\s-]?(\d{7,15})$/;
            if (tel2 == "") {
                document.querySelector(".err-tel").textContent = "Cannot be empty";
                this.err = true;
            } else {
                if (!phoneNumberRegex.test(tel2)) {
                    document.querySelector(".err-tel").textContent = "Invalid Phone Number";
                    this.err = true;
                }
                //alert(parseInt(this.tel));
            }
            //alert(parseInt(this.tel));
            if (this.err == false) {
                let contactForm = {
                    email: email,
                    last: last,
                    first: first,
                    jbt: jbt,
                    cmn: cmn,
                    phone: tel2,
                    country: this.selectedCountry,
                    np: this.selectedPeople,
                    ne: this.selectedEm,
                };
                axios
                    .post(`${BASE_URL}/request-service/request`, contactForm, {
                        withCredentials: true,
                    })
                    .then((res) => {
                        console.log(res);
                        document.querySelector("#sucess").textContent = "Email send succesfully";
                    })
                    .catch((err) => {
                        console.log(err);
                    });
            } else {
                window.scrollTo({ top: 0, left: 0, behavior: "smooth" });
            }
        },
        /* getAllCountries() {
  return countryList.getData();
},*/
    },
    created() {
        // this.countries = countryList.getData();
    },
};
</script>

<style lang="scss" scoped>
.err {
    color: red;
    font-size: 14px;
}

.main {
    width: 100%;

    background: linear-gradient(90deg, rgb(123, 42, 255) 0%, rgb(0, 174, 240) 100%);

    margin-top: 122px;
    font-weight: bold;
    padding: 10px 50px;
    text-align: left;
}

.list {
    color: white;
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: 700;
}

.box2 {
    background-color: white;
    border-radius: 12px;
    width: 100%;
    height: fit-content;
    padding: 40px;
    width: 88%;
}

input {
    box-sizing: border-box;
    padding: 0 15px;
    min-height: 27px;
    width: 100%;
    max-width: 100%;
    display: inline-block;
    height: 40px;
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
    font-size: 16px;
    font-weight: normal;
    line-height: 22px;
    color: #33475b;
    border: 1px solid #cbd6e2;
    border-radius: 3px;
}

.hs-input:hover {
    border-color: #c87872;
}

.required {
    color: red;
    font-size: 20px;
    padding-left: 5px;
}

span {
    font-weight: normal;
}

.box {
    margin-bottom: 50px;
}

.font-aws {
    padding-right: 10px;
}

.vti__input {
    border: none;
    border-radius: 0 2px 2px 0;
    width: 100%;
    outline: none;
    padding-left: 7px;
    height: 40px;
}

#people-select {
    border: 1px solid #bbb;
    border-radius: 3px;
    height: 40px;
    width: 100%;
    display: inline-block;
    padding: 5px;
    box-sizing: border-box;
    color: black;
}

.option {
    color: #9ca3af;
}

a {
    color: #01253f;
    cursor: pointer;
    text-decoration: underline;
}

.fa-regular {
    color: white;
}
</style>
