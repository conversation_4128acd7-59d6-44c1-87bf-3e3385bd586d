<template>
   <!--<NavBar />--> 
    <HeroSection />
    <LogoCarousel />
    <FeaturesSection />
    <BenefitsSection />
    <SkillsComponent />
    <SocialProofSection />
    <PricingTable :currency="currency" />
    <CallToAction />
</template>

<script>
import { mapState } from "pinia";
import { useGeolocationStore } from "@/store/geolocation";

import HeroSection from "@/components/CurrentLandingPage/HeroSection.vue";
import LogoCarousel from "@/components/CurrentLandingPage/LogoCarousel.vue";
import BenefitsSection from "@/components/CurrentLandingPage/BenefitsSection.vue";
import FeaturesSection from "@/components/CurrentLandingPage/FeaturesSection.vue";
import SkillsComponent from "@/components/CurrentLandingPage/SkillsComponent.vue";
import PricingTable from "@/components/CurrentLandingPage/PricingTable.vue";
import CallToAction from "@/components/CurrentLandingPage/CallToAction.vue";
import SocialProofSection from "@/components/CurrentLandingPage/SocialProofSection.vue";

export default {
    name: "App",
    computed: {
        ...mapState(useGeolocationStore, ["currency"]),
    },
    components: {
        HeroSection,
        LogoCarousel,
        FeaturesSection,
        BenefitsSection,
        SkillsComponent,
        SocialProofSection,
        PricingTable,
        CallToAction,
    },
};
</script>
