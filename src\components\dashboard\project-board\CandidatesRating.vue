<template>
    <div ref="candidateCard" class="candidate-card group cursor-pointer w-full" @click="toggleFeedBack" :class="{ visible: isVisible }">
        <div class="flex items-center justify-between gap-4">
            <!-- Avatar -->
            <img :src="avatarSrc" alt="Avatar" class="w-12 h-12 rounded-full object-cover" />

            <!-- Email -->
            <div class="flex-1">
                <h3 class="text-base font-medium text-gray-800 truncate">
                    {{ candidateRating?.candidateEmail }}
                </h3>
            </div>

            <!-- Score -->
            <div class="score-badge text-sm font-semibold" :class="scoreAccepted ? 'bg-green-500/90' : 'bg-red-400/90'">{{ displayScore }}/5</div>
        </div>

        <!-- Feedback -->
        <transition name="fade">
            <div v-if="showFeedback" class="feedback-box mt-3 text-sm text-gray-600 leading-relaxed">
                <p>{{ candidateRating?.feedback }}</p>
            </div>
        </transition>
    </div>
</template>
<script>
export default {
    name: "CandidatesRating",
    props: {
        candidateRating: Object,
        useEmoji: { type: Boolean, default: true },
    },
    data() {
        return {
            showFeedback: false,
            displayScore: 0,
            isVisible: false,
        };
    },
    computed: {
        scoreAccepted() {
            return this.candidateRating.rating >= 3;
        },
        avatarSrc() {
            return require("../../../assets/usercheat.jpeg");
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.animateCountUp();
            this.setupIntersectionObserver();
        });
    },
    methods: {
        toggleFeedBack() {
            if (this.candidateRating?.feedback) {
                this.showFeedback = !this.showFeedback;
            }
        },
        animateCountUp() {
            const target = this.candidateRating?.rating || 0;
            let current = 0;
            const step = target / 30;
            const interval = setInterval(() => {
                current += step;
                if (current >= target) {
                    this.displayScore = target.toFixed(1);
                    clearInterval(interval);
                } else {
                    this.displayScore = current.toFixed(1);
                }
            }, 20);
        },
        setupIntersectionObserver() {
            const observer = new IntersectionObserver(
                (entries) => {
                    entries.forEach((entry) => {
                        if (entry.isIntersecting) {
                            this.isVisible = true;
                        }
                    });
                },
                { threshold: 0.5 },
            );
            observer.observe(this.$refs.candidateCard);
        },
    },
};
</script>
<style scoped>
.candidate-card {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1.25rem;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
}

.candidate-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.score-badge {
    color: white;
    padding: 6px 12px;
    border-radius: 6px;
    min-width: 48px;
    text-align: center;
}

.feedback-box {
    background-color: #f3f4f6;
    border-radius: 8px;
    padding: 0.75rem;
}

.fade-enter-active,
.fade-leave-active {
    transition:
        opacity 0.3s ease,
        transform 0.3s ease;
}
.fade-enter-from,
.fade-leave-to {
    opacity: 0;
    transform: translateY(-4px);
}
</style>
