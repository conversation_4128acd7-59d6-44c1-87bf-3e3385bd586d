<template>
    <footer class="footer py-10 bg-gray-50 sm:pt-16 lg:pt-24 w-full mt-auto">
        <div class="px-4 mx-auto sm:px-2 lg:px-8 max-w-7xl">
            <div class="grid grid-cols-2 md:col-span-3 lg:grid-cols-5 gap-y-16 gap-x-12">
                <div class="col-span-2 md:col-span-3 lg:col-span-2 lg:pr-8">
                    <!--<img class="w-auto h-12" :src="logo" alt="Go Platform logo" />-->
                    <img :src="logoSrc" class="w-auto" :class="geolocationStore.country === 'DZ' ? 'h-18' : 'h-12'" :alt="logoAlt" />

                    <p class="text-base leading-relaxed text-gray-600 mt-7 text-left">{{ $t("Unlock the full potential of your workforce") }}</p>

                    <ul class="flex items-center space-x-3 mt-9">
                        <li>
                            <a
                                href="https://www.linkedin.com/company/go-platform/posts/?feedView=all"
                                target="_blank"
                                class="flex items-center justify-center text-white transition-all duration-200 bg-white border rounded-full w-9 h-9 hover:bg-blue-300"
                            >
                                <font-awesome-icon :icon="['fab', 'linkedin-in']" class="text-[#0c1c2c]" />
                            </a>
                        </li>

                        <li>
                            <a
                                href="https://www.facebook.com/GO.Platform"
                                target="_blank"
                                class="flex items-center justify-center text-white transition-all duration-200 bg-white border rounded-full w-9 h-9 hover:bg-blue-300"
                            >
                                <font-awesome-icon :icon="['fab', 'facebook-f']" class="text-[#0c1c2c]" />
                            </a>
                        </li>

                        <li>
                            <a
                                href="https://www.instagram.com/go.platform/"
                                target="_blank"
                                class="flex items-center justify-center text-white transition-all duration-200 bg-white border rounded-full w-9 h-9 hover:bg-blue-300"
                            >
                                <font-awesome-icon :icon="['fab', 'instagram']" class="text-[#0c1c2c]" />
                            </a>
                        </li>
                    </ul>
                </div>

                <div>
                    <h2 class="text-sm font-bold tracking-widest text-gray-900 uppercase text-left">{{ $t("Company") }}</h2>

                    <ul class="mt-6 space-y-4">
                        <li>
                            <a href="#" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Company") }} </a>
                        </li>

                        <li>
                            <a href="/contact" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Contact us") }} </a>
                        </li>

                        <li>
                            <a href="/Talents" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Talents") }} </a>
                        </li>
                    </ul>
                </div>

                <div>
                    <h2 class="text-sm font-bold tracking-widest text-gray-900 uppercase text-left">{{ $t("Services") }}</h2>

                    <ul class="mt-6 space-y-4">
                        <li>
                            <a href="#" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Services") }} </a>
                        </li>

                        <li>
                            <a href="/planning" title="" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Pricing") }} </a>
                        </li>

                        <li>
                            <a href="/blogs?category=All" title="" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Blogs") }} </a>
                        </li>
                    </ul>
                </div>

                <div>
                    <h2 class="text-sm font-bold tracking-widest text-gray-900 uppercase text-left">{{ $t("Resources") }}</h2>

                    <ul class="mt-6 space-y-4">
                        <li>
                            <a href="/blogs?category=All" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Blogs") }} </a>
                        </li>

                        <li>
                            <a href="/Terms-of-use" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Terms") }} </a>
                        </li>

                        <li>
                            <a href="/privacy-policy" class="flex text-base text-gray-600 transition-all duration-200 hover:text-NeonBlue"> {{ $t("Privicy policy") }} </a>
                        </li>
                    </ul>
                </div>
            </div>

            <hr class="mt-16 mb-10 border-gray-200" />

            <p class="text-sm text-center text-gray-500">{{ $t("© 2025 GO PLATFORM. All rights reserved.") }}</p>
        </div>
    </footer>
</template>

<script setup>
import { computed } from "vue";
import { useGeolocationStore } from "@/store/geolocation";

const geolocationStore = useGeolocationStore();

const logoSrc = computed(() => {
    return geolocationStore.country === "DZ" ? require("@/assets/GoProfiling-logo.svg") : require("@/assets/logo.svg");
});

const logoAlt = computed(() => {
    return geolocationStore.country === "DZ" ? "Go Profiling & Testing Logo" : "Go Platform Logo";
});
</script>
