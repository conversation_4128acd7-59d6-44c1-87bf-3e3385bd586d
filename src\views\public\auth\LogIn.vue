<template>
    <div class="fixed inset-0 z-[100] bg-white overflow-y-auto flex">
        <div class="flex-1 min-h-screen flex items-center justify-center p-4 lg:w-auto">
            <Transition name="slide-fade">
                <div class="w-full max-w-[350px] lg:max-w-[430px] flex flex-col items-center gap-8 px-4" v-if="!psswrd_forgot">
                    <!-- Logo & Title -->
                    <div class="w-full flex flex-col items-center gap-4">
                        <router-link to="/" class="hover:opacity-90 transition-opacity duration-200">
                            <img :src="logoSrc" class="max-w-[180px] mb-8 mx-auto" :alt="logoAlt" />
                        </router-link>
                        <h2 class="text-lg md:text-2xl font-bold text-gray-800 text-center mb-6">{{ $t("Welcome Back to Go Platform") }}</h2>
                    </div>

                    <!-- Form Container -->
                    <form @submit.prevent="login" autocomplete="on" class="w-full flex flex-col gap-6">
                        <!-- Email Input -->
                        <div class="w-full">
                            <div class="relative">
                                <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                    />
                                </svg>
                                <input
                                    type="email"
                                    name="email"
                                    autocomplete="email"
                                    v-model="email"
                                    :placeholder="$t('Work email')"
                                    class="w-full h-12 px-4 py-3 pl-10 text-base border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                />
                            </div>
                            <span v-if="requiredFields.email" class="block mt-2 text-sm text-[#ff6969] text-left">
                                {{ requiredFields.email }}
                            </span>
                        </div>

                        <!-- Password Input -->
                        <div class="w-full">
                            <div class="relative">
                                <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                    />
                                </svg>
                                <input
                                    :type="showPassword ? 'text' : 'password'"
                                    name="password"
                                    :autocomplete="showPassword ? 'off' : 'current-password'"
                                    v-model="password"
                                    :placeholder="$t('Password')"
                                    class="w-full h-12 px-4 py-3 pl-10 pr-10 text-base border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                                />
                                <button type="button" @click="showPassword = !showPassword" class="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 focus:outline-none">
                                    <i :class="['far', showPassword ? 'fa-eye-slash' : 'fa-eye']"></i>
                                </button>
                            </div>
                            <span v-if="requiredFields.password" class="block mt-2 text-sm text-[#ff6969]">
                                {{ requiredFields.password }}
                            </span>
                        </div>

                        <!-- Action Buttons -->
                        <div class="w-full flex flex-col gap-4">
                            <button type="submit" :disabled="loading" class="loginBtn w-full h-12">
                                <span v-if="!loading">{{ $t("Log in") }}</span>
                                <LoaderComponent v-else />
                            </button>

                            <button @click.prevent="psswrd_forgot = true" class="text-NeonBlue hover:opacity-85 text-base font-medium mt-2">
                                {{ $t("Forgot Password?") }}
                            </button>
                        </div>

                        <!-- Error Message -->
                        <p v-if="wrongCredentials" class="text-center text-red-500 text-sm">
                            {{ errMsg }}
                        </p>

                        <!-- Sign Up Link -->
                        <p class="text-center text-gray-600">
                            {{ $t("You don't have an account?") }}
                            <router-link to="/register" class="text-NeonBlue font-semibold hover:opacity-85 hover:underline">
                                {{ $t("Sign up") }}
                            </router-link>
                        </p>
                    </form>
                </div>
            </Transition>
            <Transition name="slide-fade">
                <div class="w-full max-w-[350px] lg:max-w-[430px] flex flex-col items-center gap-8 px-4" v-if="psswrd_forgot">
                    <!-- Add Logo & Title -->
                    <div class="mb-4 md:mb-8">
                        <router-link to="/" class="hover:opacity-90 transition-opacity duration-200">
                            <img :src="logoSrc" class="max-w-[180px] mb-8 mx-auto" :alt="logoAlt" />
                        </router-link>
                        <h2 class="text-lg md:text-2xl font-bold text-gray-800 mt-8 text-center">{{ $t("Forgot your password?") }}</h2>
                        <p class="text-xs md:text-base text-gray-600 mt-4">{{ $t("Enter your GO Platform email below and we'll send you a link to reset it.") }}</p>
                    </div>

                    <form @submit.prevent="resetPassword" class="flex flex-col w-full gap-6">
                        <!-- Email Input -->
                        <div class="relative">
                            <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                />
                            </svg>
                            <input
                                type="email"
                                id="email_forgot"
                                v-model="resetPassEmail"
                                :placeholder="$t('Enter your email')"
                                class="w-full h-12 px-4 py-3 pl-10 text-base border border-[#e5e5ef] rounded-md shadow-md shadow-[rgba(21,60,245,0.04)] outline-none focus:border-NeonBlue focus:border-[1.5px] transition-colors duration-200"
                            />
                            <span v-if="resetEmailRequired" class="text-[#ff6969] text-sm font-light absolute top-full left-0 mt-2">
                                {{ resetEmailRequired }}
                            </span>
                        </div>

                        <!-- Captcha -->
                        <!--
                        <div class="w-full flex justify-center">
                            <VueRecaptcha :sitekey="sitekey" size="normal" badge="bottomright" :loadRecaptchaScript="true" ref="recaptcha" @verify="onVerify" />
                        </div>
                        -->

                        <!-- Buttons -->
                        <div class="flex flex-col gap-4">
                            <button type="submit" class="loginBtn" :disabled="loading">
                                <span v-if="!loading">{{ $t("Reset password") }}</span>
                                <LoaderComponent v-else />
                            </button>

                            <button @click.prevent="psswrd_forgot = false" class="text-NeonBlue bg-transparent border-none font-normal text-base hover:opacity-85">{{ $t("Go back to login") }}</button>
                        </div>

                        <!-- Success Message -->
                        <p v-if="resetPasswordMessage" class="text-center text-green-500 text-sm">
                            {{ this.resetPasswordMessage }}
                        </p>
                    </form>
                </div>
            </Transition>
        </div>
        <div class="hidden lg:flex flex-1 bg-NeonBlue items-center justify-center p-4">
            <!--[#f8faff]-->
            <div class="w-full max-w-[600px] flex flex-col items-center gap-6">
                <!-- Title Group -->
                <div class="flex flex-col items-center gap-4 text-center text-white">
                    <h2 class="text-4xl font-bold">{{ $t("Simplify Your Hiring") }}</h2>
                    <p class="text-lg opacity-90 mb-14">{{ $t("Discover tools that make recruitment faster and more effective") }}</p>

                    <img src="@/assets/Images/Competencies.png" alt="Competencies" class="w-full max-w-md mx-auto" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { BASE_URL } from "@/constants";
// import { VueRecaptcha } from "vue-recaptcha";
import { useStore } from "@/store/index";
import LoaderComponent from "@/components/LoaderComponent.vue";
import { decodeCredential, googleTokenLogin } from "vue3-google-login";
import VueCookies from "vue-cookies";
import { computed } from "vue";
import { useGeolocationStore } from "@/store/geolocation";

export default {
    name: "LogIn",
    components: {
        // VueRecaptcha,
        LoaderComponent,
    },
    props: {
        show: Boolean,
        // toggleModal: Function,
        userLoggedIn: Function,
    },
    data() {
        return {
            showPassword: false,
            screenWidth: window.innerWidth,
            psswrd_forgot: false,
            // sitekey: "6LeAEmcnAAAAAERPmvDElaSYYb9Zyw7YP-W_MOAu",
            email: "",
            password: "",
            loading: false,
            wrongCredentials: false,
            errMsg: "",
            resetPassEmail: "",
            resetEmailRequired: "",
            resetPasswordMessage: "",
            requiredFields: {
                email: "",
                password: "",
            },
            imgLoaded: false,
        };
    },
    setup() {
        const Store = useStore();

        const geolocationStore = useGeolocationStore();

        const logoSrc = computed(() => {
            return geolocationStore.country === "DZ" ? require("@/assets/GoProfiling-logo.svg") : require("@/assets/Images/go_logo.svg");
        });

        const logoAlt = computed(() => {
            return geolocationStore.country === "DZ" ? "Go Profiling & Testing Logo" : "Go Platform Logo";
        });

        return { Store, logoSrc, logoAlt };
    },
    methods: {
        closepop() {},
        callback(response) {
            googleTokenLogin().then((response) => {
                console.log("Handle the response", response);
            });
            const userData = decodeCredential(response.code);
            console.log("Handle the response", userData);
        },
        toggleModal() {
            this.$router.push("/login");
        },
        resetPassword() {
            this.resetEmailRequired = "";
            if (this.resetPassEmail) {
                const emailRegex = /^\S+@\S+\.\S+$/; // Regular expression for basic email format

                if (!emailRegex.test(this.resetPassEmail)) {
                    this.resetEmailRequired = "Please enter a valid email address.";
                } else {
                    this.loading = true;
                    this.resetEmailRequired = "";

                    axios
                        .post(
                            `${BASE_URL}/user/changePassword`,
                            { email: this.resetPassEmail },
                            {
                                withCredentials: true,
                            },
                        )
                        .then((response) => {
                            this.resetPasswordMessage = response.data.message;
                            this.loading = false;
                        })
                        .catch((error) => {
                            console.log(error);
                            this.loading = false;
                            this.resetPasswordMessage = error.response.data.message;
                        });
                }
            }
        },
        checkInputs() {
            this.requiredFields = {
                email: "",
                password: "",
            };
            let isValid = true;
            // const passwordRegex = /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/;

            // if (!passwordRegex.test(this.password)) {
            //     this.requiredFields.password = "Password must contain at least one uppercase letter, one special character, and be at least 8 characters long.";
            //     isValid = false; // Update the formValid flag
            // }
            const emailRegex = /^\S+@\S+\.\S+$/; // Regular expression for basic email format

            if (!emailRegex.test(this.email)) {
                this.requiredFields.email = "Please enter a valid email address.";
                isValid = false; // Update the formValid flag
            }
            return isValid;
        },

        login() {
            if (!this.checkInputs()) {
                return;
            }
            this.loading = true;
            let user = {
                email: this.email,
                password: this.password,
            };
            axios
                .post(`${BASE_URL}/user/signin`, user, { withCredentials: true })
                .then((res) => {
                    if (res.status === 200) {
                        this.Store.isLoading = true;
                        VueCookies.set("userLogged", true, "24h");
                        this.$router.push(`/home?company_name=${res.data.company_name}`);
                        // this.userLoggedIn();
                        this.Store.userLogged();
                        this.Store.getCompanyCredit();
                        if (res.data.role) {
                            this.$router.push(`/adminBlogs?company_name=${res.data.admin_name}`);
                        } else {
                            this.$router.push(`/home?company_name=${res.data.company_name}`);
                        }

                        // this.toggleModal();
                        // this.$emit("loggedIn");
                    }
                })
                .then(() => {
                    axios
                        .get(`${BASE_URL}/user/isLoggedIn`, {
                            withCredentials: true,
                        })
                        .then(() => {
                            this.$gtag.event("login", { method: "Google" });
                        })
                        .catch((error) => {
                            console.error("Error:", error);
                        });
                })
                .catch((err) => {
                    this.wrongCredentials = true;
                    this.errMsg = err.response.data.message;
                    console.log("THE Error is here ---------------------", err);
                })
                .finally(() => (this.loading = false));
        },
        /*
        async forgotPassword(response) {
            const recaptchaToken = response;

            // Add reCAPTCHA token to your API request payload
            const forgotPasswordData = {
                email: this.email,
                recaptchaToken, // Add the reCAPTCHA token here
            };

            // Now you can make an API request to your backend to verify the reCAPTCHA token and handle the forgot password process.
            // For example, using Axios:
            try {
                await axios.post(`${BASE_URL}/verify-recaptcha`, forgotPasswordData);
            } catch (error) {
                console.error("Error:", error);
            }
        },
        onVerify(response) {
            // The reCAPTCHA response is passed as a parameter to the event handler
            this.forgotPassword(response);
        },
        */
    },
    watch: {
        show() {
            this.psswrd_forgot = false;
        },
    },
    mounted() {},
};
</script>

<style lang="scss" scoped>
input[type="submit"] {
    color: #fff;
    background: #2196f3;
    border-radius: 7px;
    border: none;
    width: 100%;
    padding: 1rem;
    font-weight: 600;
    font-size: 19.96px;
    line-height: 100%;
}

.slide-fade-enter-active {
    transition: all 0.6s ease-in;
}

.slide-fade-leave-active {
    display: none;
    transition: all 0.4s ease cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter {
    transform: translateX(0);
    opacity: 0;
}

.slide-fade-leave-to {
    transform: translateX(0);
    opacity: 0;
}

a:hover {
    text-decoration: underline;
}

.loginBtn {
    cursor: pointer;
    transition: 0.2s ease-in;
    background-color: #2196f3;
    color: white;
    width: 100%;
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    font-size: 18px;
    height: 50px;
    font-weight: 600;

    &:hover {
        opacity: 0.85;
    }
}

input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear {
    display: none;
}
input[type="password"]::-webkit-credentials-auto-fill-button {
    display: none !important;
}
input[type="password"]::-webkit-input-decoration {
    display: none !important;
}
input[type="password"]::-webkit-input-password-toggle-button {
    display: none !important;
}
input[type="password"]::-webkit-input-suffix {
    display: none !important;
}
</style>
