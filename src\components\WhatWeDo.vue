<template>
    <!-- Fourth Section -->
    <div class="w-full py-16 overflow-hidden">
        <div class="px-6 max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                    {{ $t("What can we") }}
                    <span class="relative inline-block">
                        <div class="absolute inset-0 -rotate-3"></div>
                        <span class="relative z-10">{{ $t("Do?") }}</span>
                    </span>
                </h1>
                <p class="text-xl text-gray-600">{{ $t("GO PLATFORM delivers tailored solutions across the tech spectrum") }}</p>
            </div>

            <div class="grid md:grid-cols-3 gap-8">
                <div v-for="(data, index) in whatWeDo" :key="index" class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <div class="p-6 flex flex-col items-center">
                        <div class="w-16 h-16 bg-[#cbe1ef] rounded-xl flex items-center justify-center mb-6">
                            <font-awesome-icon v-if="data.title === 'MVP'" :icon="['fas', 'rocket']" class="text-white text-2xl flex-shrink-0" />
                            <font-awesome-icon v-else-if="data.title === 'Startups'" :icon="['fas', 'chart-line']" class="text-white text-2xl" />
                            <font-awesome-icon v-else :icon="['fas', 'building']" class="text-white text-2xl" />
                        </div>
                        <h3 class="text-2xl font-bold text-[#2196f3] mb-4">{{ $t(data.title) }}</h3>

                        <div class="w-full bg-blue-50 p-6 text-center mb-4">
                            <p class="text-gray-700 font-semibold">{{ $t(data.question) }}</p>
                        </div>

                        <p class="text-gray-600 flex-grow mb-6">{{ $t(data.text) }}</p>

                        <router-link
                            to="/request-service"
                            class="group relative px-8 py-4 bg-[#2196f3] from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                        >
                            {{ $t("Get Started") }}
                        </router-link>
                    </div>
                </div>
            </div>

            <p class="text-center text-xl text-gray-800 mt-12">{{ $t("Build your dream team with GO PLATFORM in 72 hours!") }}</p>
        </div>
    </div>
</template>

<script>
export default {
    name: "WhatWeDo",
    data() {
        return {
            whatWeDo: [
                {
                    title: "MVP",
                    question: "Looking to develop a new product?",
                    text: "Find high-caliber AI professionals and agile software developers to convert ideas from concept to reality - faster and cheaper than your competition.",
                },
                {
                    title: "Startups",
                    question: "Looking to save costs while scaling swiftly?",
                    text: "Employ remote-savvy tech talent at low costs to maximize budget efficiency and supercharge growth trajectory.",
                },
                {
                    title: "Enterprise",
                    question: "Looking to expand or elevate your tech division?",
                    text: "Find seasoned software and AI experts ready to tackle tech challenges of any magnitude from complex integrations to large-scale transformations.",
                },
            ],
        };
    },
};
</script>
