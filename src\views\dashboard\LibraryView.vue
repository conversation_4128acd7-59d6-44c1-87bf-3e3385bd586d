<template>
    <div class="library-container relative bg-white rounded-md">
        <AssessementPreview :assessment="preview" :showPreview="showPreview" :togglePreview="togglePreview" :addAssessment="addAssessment" />
        <ConfirmAssessments
            :score="score"
            :yourAssessment="yourAssessment"
            :toggleshowPreviewWindow="toggleshowPreviewWindow"
            :updateAssessments="updateProjectAssessments"
            :showPreviewWindow="showPreviewWindow"
            :deleteAssessement="deleteAssessement"
            :project_id="project_id"
        />
        <ToastNotification :message="this.message ? this.message : `Assessment updated successfully`" :isVisible="isVisible" :bgColor="'success'" />
        <div class="w-full flex justify-between items-center my-2 mb-[2%]">
            <div class="flex items-center space-x-3">
                <!-- <img src="@/assets/Images/go_logo-copy.svg" alt="Logo" class="h-10" /> -->
                <h1 class="text-3xl font-bold tracking-widest">{{ $t("Assessment Library") }}</h1>
                <font-awesome-icon :icon="['far', 'bookmark']" class="text-2xl text-NeonBlue" />
            </div>
            <Popper :content="$t('Create a new project for assessment')" placement="top" :hover="true">
                <div
                    @click="
                        () => {
                            this.$router.push('/newAssessment');
                        }
                    "
                    class="bg-[#2196f3] text-white inline-flex items-center justify-center text-base rounded-md min-h-[30px] px-4 py-3 font-medium cursor-pointer hover:opacity-85 transition-all duration-300 ease-in-out"
                >
                    <font-awesome-icon :icon="['fas', 'plus']" class="mr-2" />
                    {{ $t("Create Assessment") }}
                </div>
            </Popper>
        </div>
        <hr class="h-[5px] bg-[#2196f333] w-full mt-2 mb-2" />

        <div class="libraryWrapper flex flex-col mt-4 justify-start lg:grid relative">
            <div class="w-full h-fit md:w-72 bg-blue-400 text-white p-6 space-y-6 shadow-lg transition-all duration-300 sticky top-20 max-h-[calc(100vh-5rem)] overflow-y-auto hide-scrollbar">
                <!-- Header -->

                <!-- Search Input -->
                <div class="relative group">
                    <input
                        v-model="searchText"
                        type="text"
                        :placeholder="$t('Search...')"
                        aria-label="Search input"
                        class="w-full py-2 pl-10 pr-4 bg-white text-gray-900 rounded-lg shadow-md outline-none placeholder:!text-gray-400 placeholder:!text-base placeholder:!font-medium focus:ring-2 focus:ring-CustomBlue transition-all duration-300 text-lg"
                    />
                    <font-awesome-icon :icon="['fas', 'search']" class="absolute left-3 top-1/2 -translate-y-1/2 text-blue-500 text-base" />
                </div>

                <!-- Filter by Category -->
                <div>
                    <h3 class="font-semibold text-lg">{{ $t("Category") }}</h3>
                    <div class="mt-3 space-y-2">
                        <label v-for="(category, index) in categories" :key="index" class="flex items-center space-x-3 cursor-pointer hover:opacity-90">
                            <input
                                type="checkbox"
                                :value="category"
                                v-model="selectedCategories"
                                @change="filterAssessments"
                                class="checkBox h-5 w-5 text-blue-500 rounded focus:ring-2 focus:ring-white/70"
                            />
                            <span>{{ $t(category) }}</span>
                        </label>
                    </div>
                </div>

                <!-- Filter by Duration -->
                <div>
                    <h3 class="font-semibold text-lg">{{ $t("Test Duration") }}</h3>
                    <div class="mt-3 space-y-2">
                        <label v-for="(duration, index) in testDurations" :key="index" class="flex items-center space-x-3 cursor-pointer hover:opacity-90">
                            <input type="checkbox" :value="duration" v-model="selectedDurations" @change="SearchByDuration" class="h-5 w-5 text-blue-500 rounded focus:ring-2 focus:ring-white/70" />
                            <span>{{ $t(duration) }}</span>
                        </label>
                    </div>
                </div>

                <!-- Filter by Focus -->
                <div>
                    <h3 class="font-semibold text-lg">{{ $t("Test Focus") }}</h3>
                    <div class="mt-3 space-y-2">
                        <label v-for="(focus, index) in testFucus" :key="index" class="flex items-center space-x-3 cursor-pointer hover:opacity-90">
                            <input type="checkbox" :value="focus" v-model="selectedFocus" @change="filterAssessments" class="checkBox h-5 w-5 text-blue-500 rounded focus:ring-2 focus:ring-white/70" />
                            <span>{{ $t(focus) }}</span>
                        </label>
                    </div>
                </div>

                <!-- Clear Filters -->
                <div class="pt-4 border-t border-white/20">
                    <button @click="clearFilters" class="w-full py-2 text-sm font-medium bg-white/20 hover:bg-white/30 text-white rounded-lg shadow-md">{{ $t("Clear Filters") }}</button>
                </div>
            </div>
            <CardsLoader v-if="allAssessments.length === 0" />
            <Transition name="slide" v-else>
                <div class="library" v-if="psychometrics?.length > 0">
                    <div class="library-grid-top flex flex-col lg:grid w-full">
                        <LibraryAssessCard v-for="(assess, index) in paginatedAssessments" :key="index" :assessement="assess" @openPreview="OpenPreview(assess)" />
                    </div>

                    <!-- Simple Pagination Controls -->
                    <div class="mt-8 flex justify-center items-center gap-2">
                        <!-- Previous page -->
                        <button
                            @click="goToPage(currentPage - 1)"
                            :disabled="currentPage === 1"
                            class="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            <font-awesome-icon :icon="['fas', 'angle-left']" />
                            {{ $t("Previous") }}
                        </button>

                        <!-- Page numbers -->
                        <template v-for="page in visiblePages" :key="page">
                            <button
                                v-if="page !== '...'"
                                @click="goToPage(page)"
                                class="px-3 py-2 text-sm hover:bg-gray-50 rounded"
                                :class="{
                                    'bg-blue-500 text-white hover:bg-blue-600': page === currentPage,
                                    'text-gray-700': page !== currentPage,
                                }"
                            >
                                {{ page }}
                            </button>
                            <span v-else class="px-3 py-2 text-sm text-gray-500">...</span>
                        </template>

                        <!-- Next page -->
                        <button
                            @click="goToPage(currentPage + 1)"
                            :disabled="currentPage === totalPages"
                            class="flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:text-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {{ $t("Next") }}
                            <font-awesome-icon :icon="['fas', 'angle-right']" />
                        </button>
                    </div>

                    <div v-if="psychometrics?.length < 9" class="library-grid-top w-full relative mt-8">
                        <div class="absolute w-full h-full bg-red-500 top-0 left-0 fadedCards" @click="this.Store.toggleSubscriptions()"></div>
                        <LibraryAssessCard v-for="(assess, index) in 3" :key="index" :assessement="assess" @openPreview="OpenPreview(assess)" />
                    </div>
                </div>
            </Transition>
        </div>
        <upgradePlan v-if="!this.Store.premium" />
    </div>
</template>

<script>
import AssessementPreview from "@/components/dashboard/library/AssessementPreview.vue";
import LibraryAssessCard from "@/components/dashboard/library/LibraryAssessCard.vue";
import { BASE_URL } from "@/constants";
import ConfirmAssessments from "@/components/unsued/ConfirmAssessments.vue";
import ToastNotification from "@/components/ToastNotification";
import axios from "axios";
import upgradePlan from "@/components/upgradePlan.vue";
import { useStore } from "@/store/index";
import CardsLoader from "@/components/cardsLoader.vue";

export default {
    name: "LibraryView",
    components: {
        // LibraryCard,
        AssessementPreview,
        LibraryAssessCard,
        // AssessementTab,
        // AssessementTabs,
        ConfirmAssessments,
        upgradePlan,
        ToastNotification,
        CardsLoader,
        // SearchCard,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    computed: {
        projectList() {
            return this.Store.getProjectList;
        },
        totalItems() {
            return this.filteredAssessments.length;
        },
        totalPages() {
            return Math.ceil(this.totalItems / this.itemsPerPage);
        },
        startIndex() {
            return (this.currentPage - 1) * this.itemsPerPage;
        },
        endIndex() {
            return this.startIndex + this.itemsPerPage;
        },
        paginatedAssessments() {
            return this.filteredAssessments.slice(this.startIndex, this.endIndex);
        },
        visiblePages() {
            const pages = [];
            const maxVisiblePages = 7;

            if (this.totalPages <= maxVisiblePages) {
                // Show all pages if total pages is small
                for (let i = 1; i <= this.totalPages; i++) {
                    pages.push(i);
                }
            } else {
                // Always show first page
                pages.push(1);

                // Calculate range around current page
                let start = Math.max(2, this.currentPage - 2);
                let end = Math.min(this.totalPages - 1, this.currentPage + 2);

                // Add ellipsis after first page if needed
                if (start > 2) {
                    pages.push("...");
                }

                // Add pages around current page
                for (let i = start; i <= end; i++) {
                    pages.push(i);
                }

                // Add ellipsis before last page if needed
                if (end < this.totalPages - 1) {
                    pages.push("...");
                }

                // Always show last page
                if (this.totalPages > 1) {
                    pages.push(this.totalPages);
                }
            }

            return pages;
        },
    },
    data() {
        return {
            // showTab: { hard: false, soft: false, psy: false, top: false },
            preview: {},
            showPreview: false,
            showPreviewWindow: false,
            isVisible: false,
            LibraryAssessCard: [],
            yourAssessment: [],
            allAssessments: [],
            searchPayload: "",
            hardSkills: [],
            personalityTests: [],
            softSkills: [],
            psychometrics: [],
            selected: [true, false],
            current: 0,
            score: 0,
            filteredAssessments: [],
            premium: false,
            imagePath: "",
            project_id: "",
            message: "",
            categories: ["Hard Skills", "Soft Skills", "Behavioral Skills", "Personality"],
            selectedCategories: [],
            searchText: "",
            testDurations: ["Up to 8min", "8min to 12min", "12min to 15min"],
            selectedDurations: [],
            testFucus: ["Verbal Reasoning", "Aptitude", "Numerical Reasoning"],
            selectedFocus: [],
            companies: ["KPMG", "PWC", "Workforce", "Adobe"],
            selectedCompanies: [],
            filterSM: false,
            // Pagination data
            currentPage: 1,
            itemsPerPage: 12,
            jumpToPageInput: 1,
        };
    },

    methods: {
        // Pagination methods
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.jumpToPageInput = page;
                // Scroll to top of library page
                this.$nextTick(() => {
                    const libraryContainer = document.querySelector(".library-container");
                    if (libraryContainer) {
                        libraryContainer.scrollIntoView({ behavior: "smooth", block: "start" });
                    } else {
                        // Fallback: scroll to top of page
                        window.scrollTo({ top: 0, behavior: "smooth" });
                    }
                });
            }
        },

        resetPagination() {
            this.currentPage = 1;
            this.jumpToPageInput = 1;
        },

        // Filter methods (updated to reset pagination)
        filterAssessments() {
            this.filteredAssessments = this.allAssessments.filter((assessment) => {
                const matchesSearch = assessment.name.toLowerCase().includes(this.searchText.toLowerCase());
                const mappedCategories = this.selectedCategories.map((category) => {
                    switch (category) {
                        case "Hard Skills":
                            return "Hard Skills";
                        case "Interpersonal Skills":
                            return "Soft Skills";
                        case "Behavioral Skills":
                            return "Psychometrics";
                        case "Personality":
                            return "Personality";

                        default:
                            return category;
                    }
                });
                const matchesCategory = mappedCategories.length === 0 || mappedCategories.includes(assessment.category);
                const matchesDuration = this.checkDuration(assessment);
                const matchesFocus = this.selectedFocus.length === 0 || this.selectedFocus.some((focus) => assessment.name.includes(focus));
                const matchesCompany = this.selectedCompanies.length === 0 || this.selectedCompanies.some((company) => assessment.name.includes(company));

                return matchesSearch && matchesCategory && matchesDuration && matchesFocus && matchesCompany;
            });
            this.resetPagination();
        },

        searchAssessment() {
            this.filteredAssessments = this.allAssessments.filter((assessment) => {
                const matchesSearch = assessment.name.toLowerCase().includes(this.searchText.toLowerCase());
                const matchesCategory = this.selectedCategories.length === 0 || this.selectedCategories.includes(assessment.category);
                const matchesDuration = this.checkDuration(assessment);
                const matchesFocus = this.selectedFocus.length === 0 || this.selectedFocus.some((focus) => assessment.name.includes(focus));
                return matchesSearch && matchesCategory && matchesDuration && matchesFocus;
            });
            this.resetPagination();
        },
        SearchByDuration() {
            this.filteredAssessments = this.allAssessments.filter((assessment) => {
                const matchesSearch = assessment.name.toLowerCase().includes(this.searchText.toLowerCase());
                const matchesCategory = this.selectedCategories.length === 0 || this.selectedCategories.includes(assessment.category);
                const matchesDuration = this.checkDuration(assessment);
                const matchesFocus = this.selectedFocus.length === 0 || this.selectedFocus.some((focus) => assessment.name.includes(focus));
                return matchesSearch && matchesCategory && matchesDuration && matchesFocus;
            });
            this.resetPagination();
        },
        checkDuration(assessment) {
            const duration = assessment.questions_nbr > 25 ? parseInt((20 * 35) / 60) : parseInt((assessment.questions_nbr * 35) / 60);
            return (
                this.selectedDurations.length === 0 ||
                this.selectedDurations.some((selectedDuration) => {
                    if (selectedDuration === "Up to 8min" && duration <= 8) {
                        return true;
                    } else if (selectedDuration === "8min to 12min" && duration > 8 && duration <= 12) {
                        return true;
                    } else if (selectedDuration === "12min to 15min" && duration > 12 && duration <= 15) {
                        return true;
                    }
                    return false;
                })
            );
        },
        transformedName() {
            // Use the same transformation logic as before
            const nameArray = this.filteredAssessments?.split(/-/);

            // Capitalize the first letter of each word and remove "-"
            const transformedArray = nameArray.map((part) => {
                const words = part.split(/\s+/);
                return words
                    .map((word) => {
                        const capitalWords = ["KPMG", "DRAGNET", "GTCO", "NNPC", "PWC", "ZENITHBANK", "xml", "aws", "vba"];

                        // Capitalize the entire word if it's in the list
                        if (capitalWords.includes(word.toUpperCase())) {
                            return word.toUpperCase();
                        }
                        // Capitalize the first letter of each word
                        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    })
                    .join(" ");
            });

            // Join the parts into a sentence
            return transformedArray.join(" ");
        },
        selectTab(num) {
            this.selected[this.current] = false;
            this.selected[num] = true;
            this.current = num;
        },
        toggleshowPreviewWindow() {
            if (this.project_id) {
                this.showPreviewWindow = !this.showPreviewWindow;
            } else {
                this.message = "Please select a project first";
                this.isVisible = true;
                setTimeout(() => {
                    this.isVisible = false;
                }, 5000);
            }
        },
        deleteAssessement(assess) {
            const newArr = this.yourAssessment.filter((el) => el !== assess);
            this.yourAssessment = newArr;
        },
        addAssessment(test) {
            let testName = test.name;
            if (this.yourAssessment.length < 5) {
                let assessmentFound = this.yourAssessment.find((el) => el.name === testName);

                if (assessmentFound) {
                    alert("You already added this assessment");
                } else {
                    // console.log({ test });
                    let clonedTest = { ...test };
                    clonedTest.delete = true;
                    this.yourAssessment.push(clonedTest);
                }
                this.showPreview = false;
            } else {
                this.message = "You can add up to 5 assessments only";
                this.isVisible = true;
                setTimeout(() => {
                    this.isVisible = false;
                }, 5000);
            }
        },
        OpenPreview(test) {
            this.preview = test;
            this.togglePreview();
        },
        togglePreview() {
            this.showPreview = !this.showPreview;
        },
        clearAllAssessments() {
            this.yourAssessment = [];
        },

        updateProjectAssessments(projectid, sliderValue) {
            const ID = typeof this.id === "undefined" ? projectid : this.project_id;
            const project = this.Store.getProjectList.filter((pro) => pro.id === projectid)[0];
            if (typeof ID !== "undefined") {
                let data = JSON.stringify({
                    assessments: project ? [...this.yourAssessment] : this.yourAssessment,
                    id: ID,
                    score: parseInt(sliderValue),
                });

                let config = {
                    method: "put",
                    maxBodyLength: Infinity,
                    url: `${BASE_URL}/projects/update`,
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: data,
                    withCredentials: true,
                };

                axios
                    .request(config)
                    .then(() => {
                        this.toggleshowPreviewWindow();
                        this.isVisible = true;
                        setTimeout(() => {
                            this.isVisible = false;
                        }, 5000);
                        this.$router.push("/library-partial");
                        this.Store.fetchProjects();
                    })
                    .catch((error) => {
                        console.log(error);
                        alert("there was an arror");
                    });
            } else {
                alert("you need to select a project");
            }
        },
        clearFilters() {
            this.searchText = "";
            this.selectedCategories = [];
            this.selectedDurations = [];
            this.selectedFocus = [];
            this.resetPagination();
        },
        async fetchProjectAssessments(id) {
            if (id) {
                let config = {
                    method: "get",
                    maxBodyLength: Infinity,
                    url: `${BASE_URL}/projects/projectAssessments`,
                    headers: {
                        "Content-Type": "application/json",
                    },
                    params: {
                        id: id,
                    },
                    withCredentials: true,
                };

                await axios
                    .request(config)
                    .then((response) => {
                        this.yourAssessment = response.data.assessments;
                        this.score = response.data.score;
                    })
                    .catch((error) => {
                        console.log(error);
                    });
            }
        },
    },
    watch: {
        async project_id(newVal) {
            await this.fetchProjectAssessments(newVal);
        },
        allAssessments: {
            handler(newAssessments) {
                this.filteredAssessments = newAssessments.filter((assessment) => {
                    return assessment.name.toLowerCase().includes(this.searchPayload.toLowerCase());
                });
                this.isLoading = false;
                this.resetPagination();
            },
            immediate: true,
        },
        selectedCategories: "filterAssessments",
        searchText: "searchAssessment",
        selectedDurations: "SearchByDuration",
    },
    async mounted() {
        this.Store.fetchProjects();
        this.id = this.$route.query.id;
        this.project_id = this.$route.query.id ? this.$route.query.id : "";

        axios
            .get(`${BASE_URL}/AssessmentTest/hardSkills`, {
                withCredentials: true,
            })
            .then((res) => {
                this.hardSkills = res.data.hardSkills;
                this.Store.setPremium(res.data.premium);
                this.allAssessments = [...this.allAssessments, ...this.hardSkills];
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/psychometrics`, {
                withCredentials: true,
            })
            .then((res) => {
                this.psychometrics = res.data;
                this.allAssessments = [...this.allAssessments, ...this.psychometrics];
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/softSkills`, {
                withCredentials: true,
            })
            .then((res) => {
                this.softSkills = res.data;
                this.allAssessments = [...this.allAssessments, ...this.softSkills];
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/topAssessments`, {
                withCredentials: true,
            })
            .then((res) => {
                this.LibraryAssessCard = res.data;
                this.allAssessments = [...this.allAssessments, ...this.LibraryAssessCard];
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/personality`, {
                withCredentials: true,
            })
            .then((res) => {
                this.personalityTests = res.data;
                this.allAssessments = [...this.allAssessments, ...this.personalityTests];
            });
    },
};
</script>

<style lang="scss" scoped>
.library-container {
    display: flex;
    flex-direction: column;
    padding: 1rem;
    margin: 2% 0;
}

.library-grid-top {
    grid-template-columns: 1fr 1fr;
    padding: 1rem;
    gap: 3rem;
    width: 100%;
}

.libraryWrapper {
    grid-template-columns: 1fr 3fr;
    .library {
        padding: 1%;
    }
}

.hide-scrollbar {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
}

.hide-scrollbar::-webkit-scrollbar {
    display: none; /* Chrome/Safari/Opera */
}

.library::-webkit-scrollbar {
    width: 10px;
    /* Width of the scrollbar */
}

.library::-webkit-scrollbar-track {
    background-color: #bbdffc;
    border: none;
}

.library::-webkit-scrollbar-thumb {
    background-color: #2196f3; /* Color of the scrollbar thumb */
    border-radius: 5px;
    height: 40px;
}

.library::-webkit-scrollbar-thumb:hover {
    background-color: #2195f328; /* Color of the scrollbar thumb on hover */
}

h1 {
    font-weight: 500;
    font-size: 24px;
    position: relative;
}

.library-grid {
    padding: 24px;
    display: grid;
    gap: 16px;
    row-gap: 40px;
    width: 100%;
}

::placeholder {
    font-weight: 700;
    font-size: 11px;
    color: #adb8cc;
}

input {
    border: none;
}

input:focus {
    outline: none;
}

.fadedCards {
    background: rgb(244, 247, 254);
    background: linear-gradient(0deg, rgba(244, 247, 254, 1) 37%, rgba(255, 255, 255, 0) 100%);
    z-index: 5;
}

.categories {
    gap: 8px;
}

@media (max-width: 768px) {
    ::-webkit-scrollbar {
        display: none;
    }
}

.checkBox:checked {
    background: #1677ff;
    border-color: transparent;
}

/* Slow Spin */
@keyframes spin-slow {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
.animate-spin-slow {
    animation: spin-slow 5s linear infinite;
}
</style>
