sonar.projectKey=GO-Platform-Frontend
sonar.projectName=GO Platform Frontend
sonar.projectVersion=1.0

sonar.sources=src
# sonar.tests=tests

# Language
sonar.language=js

# Encoding of source files
sonar.sourceEncoding=UTF-8

# Coverage and test results
sonar.javascript.lcov.reportPaths=coverage/lcov.info
# sonar.testExecutionReportPaths=test-report.xml

# Vue.js specific
sonar.sources=src
# sonar.tests=tests
# sonar.test.inclusions=**/*.spec.js,**/*.spec.jsx,**/*.test.js,**/*.test.jsx

sonar.javascript.globals=Vue
sonar.javascript.exclusions=**/node_modules/**,**/*.spec.js
sonar.coverage.exclusions=tests/**/*,src/assets/**/*
