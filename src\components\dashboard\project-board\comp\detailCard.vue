<template>
    <div class="bg-white shadow-md rounded-lg p-6" :class="width">
        <div v-if="$slots.title" class="mb-4 flex flex-row justify-between">
            <slot name="title"></slot>
            <button @click="handleClick" class="flex items-center justify-center w-6 h-6 hover:scale-110 transition-transform duration-300">
                <!-- Icon Image -->
                <img src="@/assets/search1.png" alt="Zoom Icon" class="w-6 h-6" />
            </button>
        </div>
        <div v-else class="flex flex-row pl-[95%]">
            <button @click="handleClick" class="flex items-center justify-center w-6 h-6 hover:scale-110 transition-transform duration-300">
                <!-- Icon Image -->
                <img src="@/assets/search1.png" alt="Zoom Icon" class="w-6 h-6" />
            </button>
        </div>
        <div v-if="$slots.header" class="mb-7">
            <slot name="header"></slot>
        </div>
        <div>
            <slot name="body"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "detailCard",
    props: {
        width: {
            type: String,
            default: "w-full",
        },
    },
    methods: {
        handleClick() {
            this.$emit("zoom"); // Emits a "zoom" event when the button is clicked
        },
    },
};
</script>
