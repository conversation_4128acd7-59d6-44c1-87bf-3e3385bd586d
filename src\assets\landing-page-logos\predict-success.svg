<svg width="158" height="199" viewBox="0 0 158 199" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_3_446)">
<rect y="0.602051" width="158" height="158" rx="50" fill="white" fill-opacity="0.05"/>
</g>
<rect x="1" y="1.60205" width="156" height="156" rx="49" stroke="url(#paint0_linear_3_446)" stroke-opacity="0.2" stroke-width="2"/>
<g filter="url(#filter1_d_3_446)">
<path d="M71.1077 42.0484C72.5392 42.9235 72.9902 44.7934 72.1151 46.2248C71.4801 47.2636 71.6392 48.6021 72.5001 49.463L72.8966 49.8595C75.2809 52.2438 76.1601 55.7465 75.1845 58.9742C74.699 60.5802 73.0036 61.4886 71.3976 61.0032C69.7916 60.5177 68.8832 58.8223 69.3687 57.2163C69.6963 56.1324 69.4011 54.9563 68.6004 54.1556L68.204 53.7592C65.3584 50.9136 64.8324 46.4893 66.9313 43.0558C67.8064 41.6244 69.6763 41.1734 71.1077 42.0484Z" fill="#FFCF22"/>
<path d="M107.758 61.0314C107.197 61.2474 106.7 61.7443 105.706 62.7383C104.712 63.7322 104.215 64.2292 103.999 64.7899C103.746 65.4459 103.746 66.1726 103.999 66.8286C104.215 67.3893 104.712 67.8863 105.706 68.8802C106.7 69.8742 107.197 70.3711 107.758 70.5871C108.414 70.8399 109.14 70.8399 109.796 70.5871C110.357 70.3711 110.854 69.8742 111.848 68.8802C112.842 67.8863 113.339 67.3893 113.555 66.8286C113.808 66.1726 113.808 65.4459 113.555 64.7899C113.339 64.2292 112.842 63.7322 111.848 62.7383C110.854 61.7443 110.357 61.2474 109.796 61.0314C109.14 60.7786 108.414 60.7786 107.758 61.0314Z" fill="#FFCF22"/>
<path d="M113.564 83.3608C112.099 82.7202 110.395 82.9893 109.198 84.0502C105.92 86.9568 101.15 87.4399 97.3564 85.2494L96.4943 84.7516C95.0413 83.9128 94.5435 82.0548 95.3824 80.6019C96.2212 79.1489 98.0792 78.6511 99.5321 79.4899L100.394 79.9877C101.924 80.8706 103.846 80.6759 105.167 79.5043C108.136 76.8722 112.363 76.2046 115.998 77.794L117.178 78.3102C118.716 78.9823 119.417 80.7734 118.745 82.3106C118.073 83.8478 116.282 84.5491 114.744 83.877L113.564 83.3608Z" fill="#FFCF22"/>
<path opacity="0.7" d="M81.7771 50.2963C82.5915 49.4819 82.9987 49.0747 83.4695 48.9255C83.8677 48.7992 84.2953 48.7992 84.6935 48.9255C85.1643 49.0747 85.5715 49.4819 86.3859 50.2963C87.2002 51.1106 87.6074 51.5178 87.7567 51.9886C87.8829 52.3869 87.8829 52.8144 87.7567 53.2126C87.6074 53.6835 87.2003 54.0906 86.3859 54.905C85.5715 55.7194 85.1643 56.1266 84.6935 56.2758C84.2953 56.4021 83.8677 56.4021 83.4695 56.2758C82.9987 56.1266 82.5915 55.7194 81.7771 54.905C80.9628 54.0906 80.5556 53.6835 80.4063 53.2126C80.2801 52.8144 80.2801 52.3869 80.4063 51.9886C80.5556 51.5178 80.9628 51.1106 81.7771 50.2963Z" fill="#1C274C"/>
<path opacity="0.7" d="M104.04 94.5147C104.889 93.6663 106.264 93.6663 107.113 94.5147C107.961 95.3632 107.961 96.7388 107.113 97.5872C106.264 98.4357 104.889 98.4357 104.04 97.5872C103.192 96.7388 103.192 95.3632 104.04 94.5147Z" fill="#1C274C"/>
<path d="M54.9046 48.4505C55.7531 47.6021 57.1287 47.6021 57.9771 48.4505C58.8256 49.299 58.8256 50.6746 57.9771 51.523C57.1287 52.3715 55.7531 52.3715 54.9046 51.523C54.0562 50.6746 54.0562 49.299 54.9046 48.4505Z" fill="#FF841F"/>
<path d="M98.4961 51.6133C100.141 51.9423 101.208 53.5428 100.879 55.1879L100.296 58.104C99.4933 62.1173 96.6018 65.3928 92.7191 66.687C90.9048 67.2918 89.5536 68.8223 89.1786 70.6976L88.5954 73.6137C88.2663 75.2589 86.6659 76.3258 85.0207 75.9968C83.3755 75.6677 82.3086 74.0673 82.6376 72.4221L83.2209 69.5061C84.0235 65.4928 86.915 62.2173 90.7978 60.9231C92.6121 60.3183 93.9632 58.7878 94.3383 56.9125L94.9215 53.9964C95.2505 52.3512 96.8509 51.2843 98.4961 51.6133Z" fill="#FF841F"/>
<path opacity="0.2" d="M97.7352 71.9464C98.5837 71.098 99.9593 71.098 100.808 71.9464C101.656 72.7949 101.656 74.1705 100.808 75.0189C99.9593 75.8674 98.5837 75.8674 97.7352 75.0189C96.8868 74.1705 96.8868 72.7949 97.7352 71.9464Z" fill="#1C274C"/>
<path d="M43.1033 96.331L49.9466 75.8013L49.9466 75.8013C53.0493 66.4932 54.6006 61.8391 58.2714 60.9725C61.9422 60.106 65.4111 63.5749 72.349 70.5128L86.0355 84.1993C92.9734 91.1372 96.4423 94.6061 95.5757 98.2769C94.7092 101.948 90.0551 103.499 80.747 106.602L60.2172 113.445L60.2172 113.445C49.016 117.179 43.4154 119.046 40.4591 116.089C37.5027 113.133 39.3696 107.532 43.1033 96.331Z" fill="url(#paint1_linear_3_446)"/>
<path d="M62.4931 62.8842L62.6982 61.8917C61.1059 60.9575 59.702 60.6338 58.2672 60.9725C57.6822 61.1107 57.151 61.345 56.6587 61.679L59.4796 62.2618C57.5413 61.8613 56.8828 61.7251 56.6587 61.679C56.6116 61.711 56.5644 61.7442 56.518 61.778L56.4966 61.8842C56.4673 62.0297 56.4251 62.2422 56.3719 62.5156C56.2654 63.0624 56.1151 63.8531 55.9376 64.838C55.5829 66.8066 55.1183 69.5577 54.6785 72.6923C53.8071 78.9026 53 86.8374 53.4158 93.1781C53.6674 97.0148 54.4656 101.774 55.178 105.493C55.5375 107.37 55.8817 109.015 56.1362 110.192C56.2635 110.78 56.3685 111.253 56.4421 111.579L56.5277 111.957L56.5507 112.056L56.5585 112.09C56.5586 112.091 56.5593 112.094 59.5181 111.405L56.5585 112.09L57.1133 114.475C58.0958 114.151 59.1281 113.807 60.213 113.445L62.9031 112.548L62.4705 110.689L62.4499 110.6L62.3693 110.244C62.2991 109.933 62.1978 109.478 62.0745 108.907C61.8278 107.767 61.4937 106.17 61.1452 104.35C60.4414 100.676 59.7042 96.2231 59.4784 92.7805C59.1056 87.0944 59.834 79.6744 60.6952 73.5366C61.1217 70.4971 61.5728 67.8258 61.917 65.9156C62.089 64.9611 62.2341 64.1982 62.3357 63.6763C62.3864 63.4154 62.4263 63.2148 62.4533 63.0807L62.4837 62.9303L62.4911 62.894L62.4931 62.8842Z" fill="white"/>
<path d="M79.6637 106.961L73.8998 108.883L73.5255 107.757L76.4083 106.799C73.5255 107.757 73.5257 107.757 73.5255 107.757L73.5218 107.746L73.5137 107.721L73.4849 107.633C73.4603 107.557 73.4251 107.448 73.3808 107.309C73.2924 107.03 73.1674 106.629 73.0182 106.131C72.7203 105.135 72.3232 103.743 71.9255 102.15C71.1475 99.0349 70.2995 94.894 70.2995 91.4438C70.2995 87.9936 71.1475 83.8528 71.9255 80.7372C72.3232 79.1447 72.7203 77.7524 73.0182 76.7571C73.1674 76.2587 73.2924 75.8579 73.3808 75.579C73.4251 75.4396 73.4603 75.3305 73.4849 75.2548L73.5137 75.1667L73.5218 75.1421L73.5242 75.1348C73.5244 75.1345 73.5255 75.1309 76.4083 76.089L73.5255 75.1309L74.383 72.551L79.1905 77.3585C79.1845 77.3772 79.1783 77.3967 79.1719 77.4169C79.0925 77.6673 78.9775 78.0361 78.8387 78.4994C78.5609 79.4276 78.1902 80.7275 77.8202 82.2093C77.0628 85.2423 76.3752 88.7789 76.3752 91.4438C76.3752 94.1088 77.0628 97.6453 77.8202 100.678C78.1902 102.16 78.5609 103.46 78.8387 104.388C78.9775 104.852 79.0925 105.22 79.1719 105.471C79.2116 105.596 79.2424 105.691 79.2628 105.754L79.2853 105.823L79.2904 105.838L79.6637 106.961Z" fill="white"/>
</g>
<g filter="url(#filter2_f_3_446)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M111.98 158.903H49C49.637 142.061 63.4919 128.602 80.4902 128.602C97.4886 128.602 111.344 142.061 111.98 158.903Z" fill="#7F76FF" fill-opacity="0.31"/>
</g>
<defs>
<filter id="filter0_i_3_446" x="0" y="0.602051" width="158" height="163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_446"/>
</filter>
<filter id="filter1_d_3_446" x="35" y="41.6021" width="88" height="83.9462" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_446"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_446" result="shape"/>
</filter>
<filter id="filter2_f_3_446" x="9" y="88.6021" width="142.98" height="110.301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_3_446"/>
</filter>
<linearGradient id="paint0_linear_3_446" x1="79" y1="0.602051" x2="79" y2="158.602" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint1_linear_3_446" x1="67" y1="67.102" x2="49.5" y2="115.102" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF7D1E"/>
<stop offset="0.653714" stop-color="#FFCE1E"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
