<template>
    <section class="skills-overview">
        <div class="text-content">
            <h1>{{ $t("All skills, no frills") }}</h1>
            <p>
                {{ $t("allSkillsNoFrills.description") }}
            </p>
            <div class="button-container">
                <a href="/register" class="cta-button">{{ $t("Start for free") }}</a>
                <a href="https://calendly.com/aouf-abdellah/20min" target="_blank" rel="noopener noreferrer" class="cta-button bordered-button">{{ $t("Book a demo") }}</a>
            </div>
        </div>
        <span> <img :src="skills" class="skills" /> </span>
    </section>
</template>

<script>
import skills from "@/assets/Images/skills.png";

export default {
    name: "SkillsOverview",
    data() {
        return {
            skills: skills,
        };
    },
};
</script>

<style scoped>
.skills-overview {
    background: linear-gradient(to bottom, #3b6ff7 34%, #5652fb 62%, #7036fe 100%);
    height: auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

.text-content {
    width: 50%;
    text-align: left;
}

.text-content h1 {
    color: #ffffff;
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 20px;
}

.text-content p {
    color: #ffffff;
    font-size: 16px;
}

.skills {
    width: 450px;
    height: auto;
    /* Maintains aspect ratio */
    border-radius: 5px;
}

.button-container {
    margin-top: 30px;
    display: inline-flex;
    gap: 20px;
}

.cta-button {
    background-color: #fef5ff;
    color: #334155;
    padding: 14px 24px; /* Increased vertical padding from default */
    border-radius: 8px;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    transition: all 0.2s ease;
}

.bordered-button {
    background-color: transparent;
    color: #f5f8ff;
    border: 1.5px solid #f5f8ff;
    padding: 12px 24px; /* Increased vertical padding from default */
}

/* Responsive Styles */
@media (max-width: 1023px) {
    .skills-overview {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .text-content {
        width: 100%;
        margin-bottom: 20px;
    }

    .text-content h1 {
        font-size: 24px;
    }

    .text-content p {
        font-size: 12px;
    }

    .talent-score {
        max-width: 100%;
    }

    .cta-button {
        padding: 8px 20px; /* Adjusted for mobile but still increased */
        font-size: 12px;
    }
}
</style>
