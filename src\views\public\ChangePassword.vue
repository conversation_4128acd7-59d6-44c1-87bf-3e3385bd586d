<template>
    <div class="w-full h-[85vh] mt-[12vh] flex justify-center items-center overflow-hidden">
        <div class="forgotPass" :class="{ expandWindow: requiredFields.password || requiredFields.confirmPassword || reponseMessage }">
            <h1>{{ $t("Reset your password") }}</h1>
            <p class="subtitle">{{ $t("Choose a strong password to secure your account") }}</p>
            <div class="input_group">
                <div class="input-wrapper">
                    <input :type="showPassword ? 'text' : 'password'" @input="checkValidity" v-model="password" name="password_forgot" id="password_forgot" :placeholder="$t('New password')" />
                    <button class="showpassword" @click.prevent="showPassword = !showPassword">
                        <img loading="lazy" decoding="async" class="eye-icon" :src="require(`@/assets/${showPassword ? 'icon_eye_closed.svg' : 'icon_eye.svg'}`)" width="25" height="25" alt="" />
                    </button>
                </div>
                <span v-if="requiredFields.password" class="err_msg">{{ requiredFields.password }}</span>
            </div>
            <div class="input_group">
                <div class="input-wrapper">
                    <input
                        :type="showConfirmPassword ? 'text' : 'password'"
                        @input="checkValidity"
                        name="confirm_password"
                        id="confirm_password"
                        :placeholder="$t('Confirm password')"
                        v-model="confirmPassword"
                    />
                    <button class="showpassword" @click.prevent="showConfirmPassword = !showConfirmPassword">
                        <img
                            loading="lazy"
                            decoding="async"
                            class="eye-icon"
                            :src="require(`@/assets/${showConfirmPassword ? 'icon_eye_closed.svg' : 'icon_eye.svg'}`)"
                            width="25"
                            height="25"
                            alt=""
                        />
                    </button>
                </div>
                <span v-if="requiredFields.confirmPassword" class="err_msg">{{ requiredFields.confirmPassword }}</span>
            </div>
            <div class="btnWrapper">
                <button v-if="!loading" @click="changePassword()">{{ $t("Change password") }}</button>
                <button v-else class="loading-btn">
                    <LoaderComponent />
                </button>
            </div>
            <div class="response-message">
                <span v-if="reponseMessage">{{ reponseMessage }}</span>
            </div>
        </div>
    </div>
</template>

<script>
import LoaderComponent from "@/components/LoaderComponent.vue";
import axios from "axios";
import Swal from "sweetalert2";
import { BASE_URL } from "@/constants";

export default {
    name: "ChangePassword",
    components: {
        LoaderComponent,
    },
    data() {
        return {
            password: "",
            confirmPassword: "",
            requiredFields: {
                password: "",
                confirmPassword: "",
            },
            showPassword: false,
            showConfirmPassword: false,
            token: "",
            reponseMessage: "",
            loading: false,
        };
    },
    methods: {
        changePassword() {
            if (this.checkValidity()) {
                this.loading = true;
                axios
                    .post(
                        `${BASE_URL}/user/changeUserPassword`,
                        {
                            password: this.password,
                            confirmPassword: this.confirmPassword,
                        },
                        {
                            headers: {
                                Authorization: `Bearer ${this.token}`,
                            },
                            withCredentials: true,
                        },
                    )
                    .then((response) => {
                        this.loading = false;
                        this.reponseMessage = response.data.message;
                        //popup with redirecting to lofin page
                        if (response.data.message == "Password changed successfully!") {
                            Swal.fire({
                                title: "Success!",
                                text: "Your password has been changed successfully.",
                                icon: "success",
                                confirmButtonText: "Go to Login",
                                confirmButtonColor: "#2196f3",
                                showClass: {
                                    popup: "animate__animated animate__fadeInDown",
                                },
                                hideClass: {
                                    popup: "animate__animated animate__fadeOutUp",
                                },
                            }).then(() => {
                                // Redirect after confirmation
                                this.$router.push("/login");
                            });
                        }
                    })
                    .catch((error) => {
                        console.log(error);
                        this.loading = false;
                        this.reponseMessage = error.response.data.message;
                    });
            }
        },
        checkValidity() {
            let valid = true;
            this.requiredFields = {
                password: "",
                confirmPassword: "",
            };
            const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/;
            if (!passwordRegex.test(this.password)) {
                this.requiredFields["password"] =
                    "Password must contain at least 8 characters with uppercase letters (A...Z), lowercase letters (a...z), numbers (1...9), and special characters (!...$).";
                valid = false;
            }
            if (this.password !== this.confirmPassword) {
                this.requiredFields["confirmPassword"] = "Password does not match.";
                valid = false;
            }

            return valid;
        },
    },
    mounted() {
        this.token = this.$route.query.token;
    },
};
</script>

<style scoped lang="scss">
.forgotPass {
    background-color: #fff;
    border-radius: 10px;
    width: 45%;
    max-width: 600px;
    min-width: 300px;
    height: auto;
    min-height: 400px;
    max-height: calc(85vh - 16vh);
    padding: 2% 6%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    gap: 1.5rem;
    box-shadow:
        rgba(0, 0, 0, 0.1) 0px 0px 5px 0px,
        rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
    transition: all 0.3s ease;
    //overflow-y: auto;
    box-sizing: border-box;

    &.expandWindow {
        min-height: 450px;
        max-height: calc(85vh - 8vh);
    }

    h1 {
        color: #0f172a;
        font-size: 28px;
        font-style: normal;
        font-weight: 500;
        line-height: 42px;
        letter-spacing: -0.48px;
        margin: 0;
        word-wrap: break-word;
        overflow-wrap: break-word;
        text-align: center;
    }

    .subtitle {
        color: #64748b;
        font-size: 0.95rem;
        font-weight: 400;
        margin: 0;
        text-align: center;
    }
}

.input_group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    width: 100%;
    position: relative;
    box-sizing: border-box;
    margin-top: 12px;

    .input-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        width: 100%;
    }

    input {
        border: 1px solid #e5e5ef;
        box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);
        border-radius: 6px;
        width: 100%;
        height: 2.8rem;
        padding: 0 3rem 0 0.75rem;
        font-size: 16px;
        box-sizing: border-box;
        outline: none;
        transition: border-color 0.2s ease;

        &:focus {
            border-color: #2196f3;
        }

        &::placeholder {
            color: #9ca3af;
        }
    }

    .showpassword {
        position: absolute;
        right: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        padding: 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 2;

        &:hover {
            opacity: 0.7;
        }

        .eye-icon {
            width: 20px;
            height: 20px;
        }
    }
}

.btnWrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 1rem;

    button {
        cursor: pointer;
        transition: all 0.2s ease;
        background-color: #2196f3;
        color: white;
        width: 100%;
        max-width: 300px;
        padding: 12px 24px;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        height: 50px;
        font-weight: 600;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover:not(.loading-btn) {
            background-color: #1976d2;
            transform: translateY(-1px);
        }

        &:active:not(.loading-btn) {
            transform: translateY(0);
        }

        &.loading-btn {
            opacity: 0.8;
            cursor: not-allowed;
        }
    }
}

.response-message {
    text-align: center;
    margin-top: 0.5rem;

    span {
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        word-wrap: break-word;
        overflow-wrap: break-word;
    }
}

.err_msg {
    color: #ef4444;
    font-size: 13px;
    font-weight: 400;
    margin-top: 0.25rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.4;
    text-align: left;
}

/* Responsive Design */
@media (max-width: 768px) {
    .forgotPass {
        width: 90%;
        min-width: 280px;
        padding: 1.5rem;
        gap: 1.25rem;
    }

    .forgotPass h1 {
        font-size: 24px;
        line-height: 1.3;
    }

    .btnWrapper button {
        font-size: 15px;
        height: 48px;
    }
}

@media (max-width: 480px) {
    .forgotPass {
        width: 95%;
        padding: 1rem;
        gap: 1rem;
    }

    .forgotPass h1 {
        font-size: 22px;
    }

    .subtitle {
        font-size: 0.9rem;
    }
}

@media (max-height: 600px) {
    .forgotPass {
        height: auto;
        max-height: calc(85vh - 16vh);
        gap: 1rem;
        padding: 1rem 6%;
    }

    .forgotPass h1 {
        font-size: 24px;
        line-height: 1.2;
    }

    .input_group {
        gap: 0.25rem;
    }
}
</style>
