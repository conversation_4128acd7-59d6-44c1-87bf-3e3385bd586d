<svg width="158" height="200" viewBox="0 0 158 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_3_590)">
<rect y="0.903076" width="158" height="158" rx="50" fill="white" fill-opacity="0.05"/>
</g>
<rect x="1" y="1.90308" width="156" height="156" rx="49" stroke="url(#paint0_linear_3_590)" stroke-opacity="0.2" stroke-width="2"/>
<g filter="url(#filter1_f_3_590)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M111.98 159.204H49C49.637 142.362 63.4919 128.903 80.4902 128.903C97.4886 128.903 111.344 142.362 111.98 159.204Z" fill="#7F76FF" fill-opacity="0.31"/>
</g>
<g filter="url(#filter2_d_3_590)">
<path d="M82.162 39.9031H75.8315C69.6785 39.9031 66.602 39.9031 64.4083 41.6627C62.2147 43.4224 61.5473 46.4257 60.2125 52.4322L59.9535 53.5977C63.8702 51.9031 69.3724 51.9031 78.9964 51.9031C88.6211 51.9031 94.1234 51.9031 98.0401 53.598L97.781 52.4322C96.4462 46.4257 95.7789 43.4224 93.5852 41.6627C91.3916 39.9031 88.3151 39.9031 82.162 39.9031Z" fill="url(#paint1_linear_3_590)"/>
<path d="M59.9531 106.208C63.8698 107.903 69.3721 107.903 78.9964 107.903C88.6207 107.903 94.123 107.903 98.0397 106.208L97.7807 107.374C96.4459 113.38 95.7785 116.384 93.5849 118.143C91.3912 119.903 88.3147 119.903 82.1617 119.903H75.8312C69.6781 119.903 66.6016 119.903 64.408 118.143C62.2143 116.384 61.5469 113.38 60.2122 107.374L59.9531 106.208Z" fill="url(#paint2_linear_3_590)"/>
<path d="M58.1109 105.207C62.1465 107.903 67.7643 107.903 79 107.903C90.2357 107.903 95.8536 107.903 99.8891 105.207C101.636 104.039 103.136 102.539 104.304 100.792C107 96.7566 107 91.1388 107 79.9031C107 68.6674 107 63.0495 104.304 59.014C103.136 57.2669 101.636 55.7669 99.8891 54.5996C95.8536 51.9031 90.2357 51.9031 79 51.9031C67.7643 51.9031 62.1465 51.9031 58.1109 54.5996C56.3638 55.7669 54.8638 57.2669 53.6965 59.014C51 63.0495 51 68.6674 51 79.9031C51 91.1388 51 96.7566 53.6965 100.792C54.8638 102.539 56.3638 104.039 58.1109 105.207Z" fill="url(#paint3_linear_3_590)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M79 64.9031C80.6569 64.9031 82 66.2462 82 67.9031V78.9335L89.0793 85.7406C90.2736 86.889 90.3109 88.7881 89.1625 89.9824C88.0141 91.1767 86.115 91.214 84.9207 90.0656L76.9207 82.3733C76.3324 81.8077 76 81.0268 76 80.2108V67.9031C76 66.2462 77.3431 64.9031 79 64.9031Z" fill="#26243B"/>
</g>
<defs>
<filter id="filter0_i_3_590" x="0" y="0.903076" width="158" height="163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_590"/>
</filter>
<filter id="filter1_f_3_590" x="9" y="88.9031" width="142.98" height="110.301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_3_590"/>
</filter>
<filter id="filter2_d_3_590" x="47" y="39.9031" width="64" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_590"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_590" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3_590" x1="79" y1="0.903076" x2="79" y2="158.903" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint1_linear_3_590" x1="78.9966" y1="39.9031" x2="78.9966" y2="119.903" gradientUnits="userSpaceOnUse">
<stop stop-color="#28D8FF"/>
<stop offset="1" stop-color="#025CFE"/>
</linearGradient>
<linearGradient id="paint2_linear_3_590" x1="78.9966" y1="39.9031" x2="78.9966" y2="119.903" gradientUnits="userSpaceOnUse">
<stop stop-color="#28D8FF"/>
<stop offset="1" stop-color="#025CFE"/>
</linearGradient>
<linearGradient id="paint3_linear_3_590" x1="57.5" y1="55.9031" x2="93.5" y2="103.903" gradientUnits="userSpaceOnUse">
<stop stop-color="#024EFD"/>
<stop offset="0.666667" stop-color="#02EEFD"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
