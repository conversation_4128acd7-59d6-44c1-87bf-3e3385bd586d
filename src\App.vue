<template>
    <PageLoader v-if="this.Store.isLoading" />
    <div v-else>
        <div v-if="this.Store.getisLogged == 'none' || (this.$route.path == '/' && this.Store.getisLogged != 'none1')" id="app">
            <div v-if="this.screenWidth > 992">
                <!-- <NavBar :toggleSearch="this.toggleSearch" ref="navbar" /> -->
                <NavBar2 :toggleSearch="this.toggleSearch" ref="navbar" />
            </div>
            <div v-else>
                <PhoneNavBar :toggleSearch="this.toggleSearch" />
            </div>
            <!-- <SwitchLocale /> -->
            <!-- <TopBar :open="togglebtn" @Opennav="(navOpen = true), (togglebtn = false)" /> -->
            <SideBar :open="navOpen" @Closenav="((navOpen = false), (togglebtn = true))" />

            <searchBar :search="this.search" ref="searchBarContainer" />
            <router-view />
            <FooterComponent />
        </div>
        <div v-if="this.Store.getisLogged == 'user'" class="logged">
            <SubscriptionComponent :show="showPlans" />
            <!--<SideBarOnBarding />-->
            <!-- <WelcomeWindow
            :startTour="startTour"  
            :showWelcome="showWelcome"
            :toggleWelcome="toggleWelcome"
        /> -->

            <!-- <GuidesWindow /> -->
            <NavbarView :SidebarViewOpen="this.SidebarViewOpen" />
            <div class="layout w-[90%] mx-[5%] h-full bg-[#f4f7fe] p-2 rounded-md">
                <!-- <SidebarView :toggleSideBarView="this.toggleSideBarView" :SidebarViewOpen="this.SidebarViewOpen" :togglePlans="togglePlans" v-if="this.$route.path !== '/newAssessment'" /> -->
                <!-- <NavigationView /> -->
                <div class="router-view h-full" :class="{ expend: !SidebarViewOpen }">
                    <router-view />
                </div>
                <!--<FeedBack />-->
                <ChatBot />
            </div>
            <!-- <ChatRoom /> -->
            <!-- <v-tour name="myTour" :steps="steps"></v-tour> -->
            <!-- :class="{ tourSteps: takeTour }" -->
        </div>
    </div>
    <div v-if="this.Store.getisLogged == 'admin'" class="logged">
        <NavbarView :toggleSideBarView="this.toggleSideBarView" />
        <div class="layout w-full">
            <SidebarView :SidebarViewOpen="this.SidebarViewOpen" />
            <!-- <NavigationView /> -->
            <div class="ml-[18%] w-full" :class="{ expend: !SidebarViewOpen }">
                <router-view />
            </div>
        </div>
    </div>
</template>

<script>
import NavBar2 from "@/components/navbar/MenuNav.vue";
import PhoneNavBar from "./components/PhoneNavBar.vue";
import FooterComponent from "./components/FooterComponent.vue";
import searchBar from "./components/searchBar.vue";
import SideBar from "./components/sideBar.vue";
import NavbarView from "./components/NavbarView.vue";
import SidebarView from "./components/SidebarView.vue";
import SubscriptionComponent from "./components/Subscription.vue";
import { useStore } from "./store/index";
import { mapState, mapActions } from "pinia";
import { useGeolocationStore } from "@/store/geolocation";
import PageLoader from "./components/PageLoader.vue";
import ChatBot from "./components/chatbot/ChatBot.vue";

export default {
    name: "App",
    components: {
        NavBar2,
        PhoneNavBar,
        FooterComponent,
        searchBar,
        SideBar,
        NavbarView,
        SidebarView,
        SubscriptionComponent,
        ChatBot,
        PageLoader,
    },
    data() {
        return {
            isLoading: true,
            togglebtn: true,
            SidebarViewOpen: true,
            navOpen: false,
            search: false,
            screenWidth: window.innerWidth,
            showPlans: false,
            visibleSlide: 0,
            showWelcome: true,
            takeTour: false,

            steps: [
                {
                    target: "#library", // We're using document.querySelector() under the hood
                    header: {
                        title: "Get Started",
                    },
                    content: `<div>
            <h1>Welcome to My Tour!</h1>
            <img loading="lazy"  decoding="async" src="./assets/Images/Landing_page.png" alt="Tour Image" />
            <p>This is a description of the tour step.</p>
          </div>`,
                    next: () => {
                        this.$router.push("/library");
                    },
                },
                {
                    target: "#library", // We're using document.querySelector() under the hood
                    header: {
                        title: "Get Started",
                    },
                    content: `<div>
            <h1>Welcome to My Tour!</h1>
            <img loading="lazy"  decoding="async" src="./assets/Images/Landing_page.png" alt="Tour Image" />
            <p>This is a description of the tour step.</p>
          </div>`,
                    next: () => {
                        this.$router.push("/library");
                    },
                },
                {
                    target: "#library", // We're using document.querySelector() under the hood
                    header: {
                        title: "Get Started",
                    },
                    content: `<div>
            <h1>Welcome to My Tour!</h1>
            <img loading="lazy"  decoding="async" src="./assets/Images/Landing_page.png" alt="Tour Image" />
            <p>This is a description of the tour step.</p>
          </div>`,
                    next: () => {
                        this.$router.push("/library");
                    },
                },
                {
                    target: ".v-step-1",
                    content: "An awesome plugin made with Vue.js!",
                },
                {
                    target: '[data-v-step="2"]',
                    content: "Try it, you'll love it!<br>You can put HTML in the steps and completely customize the DOM to suit your needs.",
                    params: {
                        placement: "top", // Any valid Popper.js placement. See https://popper.js.org/popper-documentation.html#Popper.placements
                    },
                },
            ],
        };
    },
    setup() {
        const Store = useStore();
        const geoStore = useGeolocationStore();

        // Initialize geolocation store
        geoStore.initialize();

        return { Store };
    },
    computed: {
        ...mapState(useGeolocationStore, ["country"]),
    },
    watch: {
        country(newCountry) {
            const savedLocale = localStorage.getItem("locale");
            if (!savedLocale) {
                if (newCountry === "DZ" || newCountry === "FR") {
                    this.$i18n.locale = "fr";
                } else {
                    this.$i18n.locale = "en";
                }
            }
        },
    },
    methods: {
        ...mapActions(useGeolocationStore, ["initialize"]),
        toggleSearch() {
            this.search = !this.search;

            if (this.search) {
                document.addEventListener("click", this.clickOutside);
            } else {
                document.removeEventListener("click", this.clickOutside);
            }
        },
        toggleSideBarView() {
            this.SidebarViewOpen = !this.SidebarViewOpen;
        },
        startTour() {
            this.takeTour = true;
            this.showWelcome = false;
            this.$tours["myTour"]?.start();
        },
        toggleWelcome() {
            this.showWelcome = !this.showWelcome;
        },
        navigateToNextStep() {
            this.takeTour = false;
            this.$router.push("/library");
        },
        clickOutside(event) {
            if (!this.$refs.navbar.$refs.searchIcon.contains(event.target) && !this.$refs.searchBarContainer.$refs.searchInput.contains(event.target)) {
                this.search = false;
                document.removeEventListener("click", this.clickOutside);
            }
        },
    },

    async created() {
        const geoStore = useGeolocationStore();
        await geoStore.initialize();

        // Apply text replacement for Algerian users across all languages
        if (geoStore.country === "DZ") {
            if (this.$i18n?.messages?.fr) {
                this.$i18n.setLocaleMessage("fr", deepWrapMessages(this.$i18n.messages.fr));
            }
            if (this.$i18n?.messages?.en) {
                this.$i18n.setLocaleMessage("en", deepWrapMessages(this.$i18n.messages.en));
            }
            if (this.$i18n?.messages?.ar) {
                this.$i18n.setLocaleMessage("ar", deepWrapMessages(this.$i18n.messages.ar));
            }
        }

        const savedLocale = localStorage.getItem("locale");
        if (savedLocale) {
            this.$i18n.locale = savedLocale;
        } else {
            // Set initial locale based on geolocation if no preference is saved
            if (geoStore.country === "DZ" || geoStore.country === "FR") {
                this.$i18n.locale = "fr";
            } else {
                this.$i18n.locale = "en";
            }
        }
    },

    mounted() {
        //console.log(sessionStorage.getItem('user'))
        // Check if there is a valid login token in local storage
        this.Store.userLogged();
        // console.log(this.loggedIn);
        const loginToken = localStorage.getItem("LoginToken");

        if (loginToken) {
            // Retrieve user information from session storage
            const userString = sessionStorage.getItem("user");
            if (userString) {
                const user = JSON.parse(userString);
                const { firstName, lastName } = user;
                this.$root.userInitials = (firstName.charAt(0) + lastName.charAt(0)).toUpperCase();
            }
        }
        this.Store.getCompanyCredit();
    },
    // provide() {
    // return {
    // userInitials: this.userInitials,
    // };
    // },
};

// Company name replacement functions
function wrapMessageFunc(func) {
    return function (...args) {
        const result = func(...args);
        if (typeof result === "string") {
            return result.replace(/GO PLATFORM|Go Platform|GO Platform|go platform|Go platform/g, "Go Profiling & Testing");
        }
        return result;
    };
}

function deepWrapMessages(messages) {
    const newMessages = {};
    for (const key in messages) {
        if (typeof messages[key] === "function") {
            newMessages[key] = wrapMessageFunc(messages[key]);
        } else if (typeof messages[key] === "object" && messages[key] !== null) {
            newMessages[key] = deepWrapMessages(messages[key]);
        } else {
            newMessages[key] = messages[key];
        }
    }
    return newMessages;
}
</script>

<style scoped lang="scss">
#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    justify-items: center;
}

.layout {
    display: flex;
    align-items: stretch;
    margin-top: 4rem;
}

.router-view {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    flex-grow: 1;
    width: 80%;
    transition: all 0.3 ease-in-out;

    .container {
        min-height: 75vh;
        min-width: 100%;
    }
}

.tourSteps {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    height: 100%;

    > :first-child {
        background-color: red;
    }

    > :nth-child(2) {
        background-color: #fff;
    }

    #header {
        background-color: red;
    }
}

body::after {
    content: "";
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    opacity: 0.7;
    filter: grayscale(0%) brightness(100%) blur(2px) hue-rotate(30deg);
    position: absolute;
    top: 10%;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

#app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    justify-items: center;
}

.expend {
    margin-left: 5%;
}
</style>
