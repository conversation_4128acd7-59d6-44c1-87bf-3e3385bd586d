<template>
    <section class="bg-gray-50 w-full">
        <!-- Hero Content Section -->
        <div class="min-h-screen flex items-center pt-[40px] lg:pt-[40px]">
            <div class="px-4 mx-auto max-w-7xl w-full sm:px-2">
                <RippleComponent :main-circle-size="150" :num-circles="4" class="pointer-events-none hidden lg:block" />
                <div class="max-w-4xl mx-auto text-center">
                    <div class="gradient-border p-0.5 rounded-full hover:transform hover:-translate-y-0.5 transition-transform duration-200 w-fit mx-auto mb-4 md:mb-6">
                        <div class="bg-white rounded-full px-5 py-2 flex items-center gap-2">
                            <span class="gradient-text text-sm font-semibold">{{ $t("Backed by Meta") }}</span>
                            <font-awesome-icon :icon="['fab', 'meta']" class="text-[#0081FB]" />
                        </div>
                    </div>
                    <h1 class="text-2xl text-gray-900 px-2 sm:px-0 sm:text-3xl md:text-4xl lg:text-6xl mb-4 md:mb-6 grid gap-y-3 md:gap-y-4">
                        <span class="block font-bold">{{ $t("Skills of Tomorrow,") }}</span>
                        <span class="block font-bold">{{ $t("Delivered Today") }}</span>
                    </h1>

                    <p class="text-sm text-gray-600 px-3 sm:px-0 sm:text-base md:text-lg font-inter mb-9">
                        {{ $t("HeroSectionDescription") }}
                    </p>

                    <div class="flex flex-col gap-3 px-2 sm:px-0 md:flex-row md:gap-4 md:justify-center">
                        <router-link
                            to="/register"
                            class="px-5 py-2.5 text-white bg-[#2196f3] rounded-md md:px-8 md:py-3 text-base font-bold hover:opacity-85 transition-colors flex items-center justify-center sm:text-lg"
                        >
                            {{ $t("Start for free") }}
                        </router-link>

                        <button
                            @click="showVideo = true"
                            class="flex items-center justify-center gap-2 px-5 py-2.5 text-gray-900 transition-colors bg-white border-2 rounded-md md:px-8 md:py-3 text-base font-bold border-gray-200 hover:border-[#2196f3] hover:text-[#2196f3] sm:text-lg"
                        >
                            <svg class="w-5 h-5" viewBox="0 0 18 18" fill="none" stroke="currentColor">
                                <path
                                    d="M8.18 13.426C6.86 14.392 5 13.448 5 11.81V5.44C5 3.8 6.86 2.858 8.18 3.824L12.54 7.01C13.634 7.81 13.634 9.44 12.54 10.24L8.18 13.426Z"
                                    stroke-width="2"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                            {{ $t("Watch demo") }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Video Demo Section -->
        <div class="max-w-4xl mx-auto px-4 sm:px-6 pb-24 hidden lg:block">
            <div class="p-1 bg-white rounded-lg md:p-2">
                <video autoplay muted loop playsinline class="w-full rounded-lg aspect-video">
                    <source src="https://d25xxplko1zqco.cloudfront.net/Videos/GO-PLATFORM.mp4" type="video/mp4" />
                </video>
            </div>
        </div>

        <!-- Video Modal -->
        <div v-if="showVideo" class="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm">
            <div class="relative grid w-full h-full place-items-center p-4">
                <div class="w-full max-w-4xl aspect-video">
                    <iframe class="w-full h-full" :src="videoUrl" allow="autoplay; encrypted-media" allowfullscreen></iframe>
                </div>
                <button @click="showVideo = false" class="absolute text-white top-4 right-4 hover:text-gray-200 text-2xl">&times;</button>
            </div>
        </div>
    </section>
</template>

<script>
import RippleComponent from "./RippleComponent.vue";

export default {
    components: { RippleComponent },
    data() {
        return {
            showVideo: false,
            videoId: "P9pIfxgiPCk",
        };
    },
    computed: {
        videoUrl() {
            return `https://www.youtube.com/embed/${this.videoId}?autoplay=1&controls=0&modestbranding=1&rel=0`;
        },
    },
};
</script>

<style>
.gradient-border {
    background: linear-gradient(45deg, #0081fb, #00a2ff, #c084fc, #0081fb);
    background-size: 300% 300%;
    animation: gradientAnimation 3s ease infinite;
}

.gradient-text {
    background: linear-gradient(45deg, #0081fb, #00a2ff, #c084fc, #0081fb);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientAnimation 3s ease infinite;
}

@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
</style>
