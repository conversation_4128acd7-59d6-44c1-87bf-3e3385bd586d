<template>
    <InviteCandidate :showEmail="showEmail" :assessmentsLength="project.assessments" :toggleEmail="toggleEmail" :projectId="project._id" />
    <AllRatings :viewAllRatings="viewAllRatings" :toggleRatings="toggleRatings" :candidatesRating="candidatesRating" />
    <div v-if="isLoading" class="loader">
        <LoadingComponent type="talents" />
    </div>
    <div v-else class="mb-40">
        <!-- Header Stats -->
        <div class="grid grid-cols-4 gap-4 mb-4">
            <StatsCard title="Success Rate" :value="getSuccessRate() + '%'" :iconIndex="2" />

            <StatsCard title="Completion" :value="isNaN(compitionRate) ? 0 : compitionRate + '%'" :iconIndex="3" />

            <StatsCard title="Applicants" :value="this.candidates.length" :iconIndex="0" />

            <StatsCard title="Assessements" :value="this.project.assessments?.length" :iconIndex="1" />
        </div>
        <div>
            <div class="board-container">
                <div class="w-full flex flex-col-reverse lg:flex-row justify-between p-3 bg-white border-b rounded mb-5 shadow-card">
                    <div class="flex">
                        <Popper :content="$t('Back')" placement="top" :hover="true">
                            <button
                                class="menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 hidden lg:block"
                                @click="
                                    () => {
                                        this.$router.push('/dashboard');
                                    }
                                "
                            >
                                <font-awesome-icon :icon="['fas', 'angle-left']" class="pl-6" />
                            </button>
                        </Popper>
                        <div class="mx-4 flex flex-col">
                            <h2 class="projData bg-custom-gradient bg-clip-text text-transparent">
                                {{ project.name ? project.name : "Untitled" }}
                                {{ project.seniority && " - " + project.seniority }}
                                {{ project.jobTitle && " - " + project.jobTitle }}
                            </h2>
                            <div class="flex" style="color: #2196f3">
                                <div class="flex items-center">
                                    <font-awesome-icon :icon="['far', 'file-lines']" class="mx-2" />
                                    <p>{{ filteredAssessmentsLength }} tests</p>
                                </div>
                                <div class="flex mx-2 items-center">
                                    <font-awesome-icon :icon="['far', 'clock']" class="mx-2" />
                                    <p>{{ totalDuration }} {{ $t(" minutes") }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-4 items-center justify-start relative">
                        <button
                            class="menuBtn bg-[#E0E4EC] text-gray-700 hover:bg-black/10 lg:hidden block"
                            @click="
                                () => {
                                    this.$router.go(-1);
                                }
                            "
                        >
                            <font-awesome-icon :icon="['fas', 'angle-left']" class="pl-8" />
                        </button>
                        <Popper :content="$t('Edit, Duplicate and Delete')" placement="top" :hover="true">
                            <button @click="toggleProjectMenu" style="cursor: pointer" class="menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 mt-1">
                                <font-awesome-icon :icon="['fas', 'ellipsis-vertical']" class="pl-7" />
                            </button>
                        </Popper>
                        <ProjectMenu
                            :showDetails="showMenu"
                            :toggleMenu="toggleProjectMenu"
                            :editable="invitations.length > 0 ? false : true"
                            :thisProject="project"
                            :hideSample="true"
                            class="absolute top-0 right-0"
                            style="position: absolute; right: 20%"
                        />
                        <Popper :content="$t('Preview')" placement="top" :hover="true">
                            <div
                                class="text-slate-700 px-4 font-light text-sm py-3 my-1 menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 flex items-center justify-center cursor-pointer"
                                @click.stop
                                @click="navigateToPreview"
                            >
                                <!-- <p>{{ $t("Preview") }}</p> -->
                                <font-awesome-icon :icon="['fas', 'eye']" class="w-5 h-5" />
                            </div>
                        </Popper>
                        <Popper :content="$t('Invite candidates')" placement="top" :hover="true">
                            <button ref="targetElement3" @click="toggleEmail" class="nextStep ml-auto">{{ $t("Invite") }}</button>
                        </Popper>
                    </div>
                </div>

                <div class="navigation-tabs bg-white h-[62px] rounded shadow-card">
                    <div class="nav-links font-inter text-md pt-5 font-bold text-left decoration-skip-ink-none flex flex-row justify-between items-center gap-4 text-[#343637] h-full">
                        <router-link to="#" @click.prevent="navigateToDashBoard(project)" :class="`${this.$route.path == '/boards' ? 'active' : ''}`">
                            <span>{{ $t("Summary") }}</span>
                        </router-link>

                        <router-link to="#" @click.prevent="navigateToDetailBoard(project)" :class="`${this.$route.path == `/${locale}/Details` ? 'active' : ''}`">
                            <span>{{ $t("Details") }}</span>
                        </router-link>

                        <router-link to="#" @click.prevent="navigateToCheatBoard(project)" :class="`${this.$route.path == '/CheatTab' ? 'active' : ''}`">
                            <span>{{ $t("Anti cheat") }}</span>
                        </router-link>

                        <router-link to="#" class="disabled" @click.prevent="navigateToVideoBoard(project)" :class="`${this.$route.path == '/DashVideo' ? 'active' : ''}`">
                            <div class="absolute top-[-15px] right-[-25px] rounded-[15px] text-[12px] px-2 text-[#fff] bg-[#2371b6]">{{ $t("Soon") }}</div>
                            <span class="hidden lg:block">{{ $t("Video Interview") }}</span>
                        </router-link>

                        <!-- <router-link to="/InvitedCoworkerTab" :class="`${this.$route.path == '/InvitedCoworkerTab' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'chart-gantt']" class="nav-icon" />
                    <span>Invited Coworkers</span>
                </router-link> -->
                    </div>
                </div>

                <div v-if="allCheaters && allCheaters.length > 0" class="p-4 bg-white w-full h-600">
                    <!-- Table -->
                    <div class="overflow-x-auto h-600">
                        <table class="w-full table-auto border-collapse text-left">
                            <thead>
                                <tr class="text-black font-semibold text-xl">
                                    <th class="px-6 py-4">{{ $t("Talents") }}</th>
                                    <th class="px-6 py-4"></th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr v-for="cheater in allCheaters" :key="cheater._id" class="border-t hover:bg-gray-50 transition">
                                    <td class="px-6 py-4 flex items-center gap-4">
                                        <!--<img src="../assets/usercheat.jpeg" alt="Avatar" class="w-10 h-10 rounded-full object-cover" />-->
                                        <div class="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                                            <font-awesome-icon :icon="['fas', 'user']" class="text-NeonBlue text-xl" />
                                        </div>
                                        <span>
                                            {{ cheater.candidate_id?.First_name + " " + cheater.candidate_id?.Last_name }}
                                        </span>
                                    </td>

                                    <td class="px-6 py-4 text-right">
                                        <button
                                            v-if="cheater.status === 'potential-cheater'"
                                            @click="
                                                openCheatingCard();
                                                goToCheatingCard(cheater._id);
                                            "
                                            class="px-4 py-2 text-white bg-[#2196f3] rounded hover:bg-white hover:text-[#2196f3] border border-[#2196f3] transition font-roboto font-normal tracking-[0.6018px]"
                                        >
                                            {{ $t("Verify") }}
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination >
                    <div-- class="flex items-center justify-between mt-4">
                        <button class="px-3 py-2 bg-white text-black border rounded hover:text-[#2196f3]" :disabled="currentPage === 1" @click="changePage(currentPage - 1)">Prev</button>
                        <div class="flex items-center space-x-2">
                            <button
                                v-for="page in totalPages"
                                :key="page"
                                :class="['px-3 py-2 rounded', currentPage === page ? 'bg-[#2196f3] text-white' : 'text-black bg-white border border-black hover:text-[#2196f3]']"
                                @click="changePage(page)"
                            >
                                {{ page }}
                            </button>
                        </div>
                        <button class="px-3 py-2 bg-white text-black border rounded hover:text-[#2196f3]" :disabled="currentPage === totalPages" @click="changePage(currentPage + 1)">Next</button>
                    </div-->
                </div>
                <div v-else class="flex flex-col content-center items-center shadow-md rounded-lg p-4 bg-white w-full justify-between gap-3">
                    <div class="flex justify-center items-center">
                        <img src="../assets/No-Cheating-Attempt.svg" alt="No Behavioral Skills" class="w-[40%] h-auto" />
                    </div>
                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Cheating Attempt") }}</h2>
                    <div class="flex flex-col justify-center items-center">
                        <span class="text-center w-full">{{ $t("Looks like you have no cheating attempts yet.") }}</span>
                        <span class="text-center w-full">{{ $t("Keep an eye on this, there is always cheating attempts per hire.") }}</span>
                    </div>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useStore } from "@/store/index";
import ProjectMenu from "@/components/dashboard/project/ProjectMenu.vue";
import { BASE_URL } from "@/constants";
import InviteCandidate from "@/components/dashboard/project-board/InviteCandidate.vue";
import axios from "axios";

import LoadingComponent from "@/components/LoadingComponent.vue";
import StatsCard from "@/components/dashboard/project-board/comp/StatsCard.vue";

export default {
    name: "DashboardView",
    components: {
        StatsCard,
        ProjectMenu,
        LoadingComponent,
        InviteCandidate,
    },
    data() {
        return {
            currentPage: 1,
            totalPages: 2,
            selectedRank: "Best Ranked",
            selectedStatus: "Passed",
            imagePath: require(`@/assets/onBoardingGIFs/inviteCandidate.gif`),
            showEmail: false,
            windowWidth: 500,
            graphData: [],
            selected: [true, false, false],
            current: 0,
            invitations: [],
            allCheaters: [],
            candidates: [],
            compitionRate: 0,
            project: {},
            candidatesScores: [],
            threeCandidates: [],
            candidatesRating: [],
            diskData: [],
            histogramData: [],
            histogramData2: [],
            ThreeRatings: [],
            behindSchedule: null,
            id: "",
            isCheatingCardOpen: false,
            candidateCard: {},
            showMenu: false,
            isLoading: true,
            viewAllRatings: false,
            viewAllCheaters: false,
            users: [
                { name: "User 1", email: "<EMAIL>" },
                { name: "User 2", email: "<EMAIL>" },
                { name: "User 3", email: "<EMAIL>" },
            ],
        };
    },
    methods: {
        navigateToPreview() {
            const locale = this.$route.params.locale || "en";

            const url = this.$router.resolve({
                path: `/${locale}/preview-project`,
                query: { id: this.project._id },
            }).href;

            // Open the route in a new tab
            window.open(url, "_blank");
        },
        navigateToDashBoard(project) {
            const locale = this.$route.params.locale || "en";

            this.$router.push({
                path: `/${locale}/boards`,
                query: { id: project._id },
            });
        },
        navigateToDetailBoard(project) {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/Details`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToCheatBoard(project) {
            const locale = this.$route.params.locale || "en";

            this.$router.push({
                path: `/${locale}/CheatTab`,
                query: { id: project._id },
            });
        },
        navigateToVideoBoard(project) {
            const locale = this.$route.params.locale || "en";

            this.$router.push({
                path: `/${locale}/DashVideo`,
                query: { id: project._id },
            });
        },

        async getCandidateInv() {
            // if (!this.candidateInfo || !this.candidate || !this.projectId) {
            //     console.error("Candidate information is incomplete.");
            //     return;
            // }

            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/candidates/candidateInfo`,
                headers: {
                    "Content-Type": "application/json",
                },
                params: {
                    email: this.candidateEmail,
                    projectId: this.projectId,
                },
                withCredentials: true,
            };

            axios
                .request(config)
                .then((response) => {
                    this.candidateInv = response.data.CandidateInvitation;
                    this.Store.candidateInfoAct = response.data.candidateInfo;
                    this.Store.candidateActivity = response.data.candidateScore;
                    this.candidateInfo = response.data.candidateInfo;
                    this.candidate = response.data.candidateScore;
                    this.candidateInterpretations = response.data.interpretations;
                    this.candidateRating = response.data.candidateRating;
                })
                .catch((error) => {
                    console.log(error);
                });
        },

        toggleProjectMenu() {
            this.showMenu = !this.showMenu;
        },

        toggleRatings() {
            this.viewAllRatings = !this.viewAllRatings;
        },
        goToCheatingCard(id) {
            const locale = this.$route.params.locale || "en";

            this.$router.push({
                path: `/${locale}/anti-cheat/${id}`,
                // query: { id: this.project._id },
            });
        },
        toggleEmail() {
            this.showEmail = !this.showEmail;
        },

        openCheatingCard() {
            this.isCheatingCardOpen = true;
        },

        getAllCheaters(id) {
            axios.get(`${BASE_URL}/anticheat/potential-cheaters/${id}`).then((response) => {
                const potentialCheaters = response.data;
                const cheaters = response.data;
                this.allCheaters = [...potentialCheaters, ...cheaters];
                console.log("THESES AER ALL THE CHEATERS");
                console.log(this.allCheaters);
                console.log("THESES AER ALL THE CHEATERS");
            });
        },

        getSuccessRate() {
            if (this.candidates.length > 0) {
                const candidatesAbove80Percent = this.candidates.filter((candidate) => {
                    let score = 0;
                    candidate.results.forEach((element) => {
                        score += (element.totalPoints * 100) / element.quesionsNbr;
                    });
                    const averageScore = score / candidate.results.length;
                    // const roundedScore = (averageScore * 100).toFixed(2); // Convert to percentage

                    return averageScore > this.project.min_score; // Filter candidates with scores over 80%
                });

                const numberOfCandidatesAbove80Percent = candidatesAbove80Percent.length;
                const totalCandidates = this.candidates.length;

                const percentageAbove80Percent = (numberOfCandidatesAbove80Percent / totalCandidates) * 100;
                return percentageAbove80Percent.toFixed(2);
            } else return 0;
        },
    },
    computed: {
        filteredAssessmentsLength() {
            if (!this.project || !this.project.assessments) {
                return 0;
            }

            const hasCustomAssessment = this.project.assessments.some((assessment) => assessment.category === "Custom");

            return hasCustomAssessment ? this.project?.assessments?.length - 1 : this.project?.assessments?.length;
        },
        filteredAssessments() {
            return this.project.assessments.filter((assessment) => assessment.category !== "Custom");
        },
        customAssessments() {
            return this.project.assessments.find((assessment) => assessment.category == "Custom");
        },
        totalDuration: {
            get() {
                if (this.project?.assessments?.length > 0) {
                    const totalSeconds = this.filteredAssessments.reduce((acc, assessment) => {
                        if (assessment?.questions_nbr > 25) {
                            return acc + 20 * 35;
                        } else {
                            return acc + assessment?.questions_nbr * 35;
                        }
                    }, 0);

                    const minutes = Math.floor(totalSeconds / 60);
                    const seconds = totalSeconds % 60;
                    return `${minutes}:${seconds}`;
                } else {
                    return { minutes: 0, seconds: 0 };
                }
            },
        },
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    async mounted() {
        this.getCandidateInv();
        this.id = this.$route.query.id;
        this.getAllCheaters(this.id);

        await this.Store.fetchProjects();

        this.project = this.Store.projects[0]?._id;

        let config = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/projects/projectData`,
            headers: {
                "Content-Type": "application/json",
            },
            params: {
                id: this.id,
            },
            withCredentials: true,
        };

        await axios
            .request(config)
            .then((response) => {
                this.invitations = response.data.invited;
                this.candidates = response.data.candidates;
                this.behindSchedule = this.invitations.length - this.candidates.length;
                this.threeCandidates = this.candidates.slice(0, 3);
                this.project = response.data.project;
                this.compitionRate = (this.candidates.length / this.invitations.length).toFixed(2);
                this.graphData = response.data.chartData;

                this.isLoading = false;
                // this.yourAssessment = response.data.assessments;
                // this.score = response.data.score;
            })
            .catch((error) => {
                console.log(error);
            });
        this.diskData = this.Store.createDistributionData(this.id);
    },
};
</script>

<style scoped lang="scss">
@import "tailwindcss/tailwind.css";

.board-container {
    display: flex;
    flex-direction: column;
    padding-top: 20px;
}

.menuBtn {
    width: 60px;
    height: 50px;

    border-radius: 6px;
}

.projData {
    font-size: 20px;
    font-weight: 700;
}

/*----------------------------------------------------------------*/

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-top: 48px; */
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 2px solid #edeff2;
}

.nav-links span {
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: black;
}

.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-family: "Roboto";
    font-weight: 700;
    font-size: 14px;
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links .nav-icon {
    font-size: 20px;
}

.nav-links img {
    margin-right: 12px;
}

.nextStep {
    width: 100px;
    height: 50px;
    color: white;
    font-weight: 500;
    background: #2196f3;
    border-radius: 6px;

    &:hover {
        opacity: 0.85;
    }
}

.loader {
    width: 100%;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 2px solid #edeff2;
    margin-bottom: 25px;
}
.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-family: "Roboto";
    font-weight: 700;
    font-size: 14px;
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links .nav-icon {
    font-size: 20px;
}

.nav-links img {
    margin-right: 12px;
}
.nav-links a::after {
    content: "";
    background: #2196f3;
    position: absolute;
    bottom: 2.4px;
    left: 0;
    width: 0;
    height: 2px;
    transition: width 0.4s ease-in-out;
    border-radius: 25px;
}

.nav-links > a:hover::after {
    width: 100%;
    color: #2196f3;
}

.nav-links a.active::after {
    background: #2196f3;
    width: 100%;
}

.nav-links a.active span {
    color: #2196f3; /* This will change the span color when the link is active */
}
.disabled {
    pointer-events: none;

    span {
        opacity: 0.5;
    }
}
.nav-links span:hover {
    color: #2196f3;
}
</style>
