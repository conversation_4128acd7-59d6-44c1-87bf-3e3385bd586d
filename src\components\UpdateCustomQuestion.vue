<template>
    <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />
    <div v-if="isShown" class="w-screen h-screen bottom-0 top-0 left-0 right-0 fixed z-20 inset-0 flex justify-center items-center bg-black bg-opacity-25">
        <div class="fixed right-0 top-0 left-0 bottom-0 -z-10 w-screen h-screen"></div>
        <div class="w-1/2 h-fit max-h-5/6 rounded-md bg-white flex flex-col justify-start items-center overflow-hidden">
            <div class="bg-NeonBlue w-full flex flex-row justify-between items-center p-8 py-4">
                <h1 class="text-xl font-medium w-full text-left text-white">{{ $t("Update custom question") }}</h1>
                <button class="text-white text-lg font-semibold" @click="closePanel"><font-awesome-icon :icon="['fas', 'times']" /></button>
            </div>

            <Transition name="slide">
                <div v-if="type == 'Essay'" class="flex bg-white rounded flex-col items-center w-11/12 mx-auto overflow-hidden justify-center">
                    <form @submit.prevent="editCustomQuestion()" class="flex flex-col gap-5 pb-4 pt-4 items-center justify-center h-full w-full">
                        <div class="flex flex-row justify-between gap-4 items-center w-full">
                            <div class="flex flex-col items-start gap-0 justify-between w-4/5">
                                <label for="qstTitle">{{ $t("Title") }}</label>
                                <input
                                    id="qstTitle"
                                    required
                                    v-model="essay.name"
                                    class="w-full p-2 border border-solid border-gray-300 rounded shadow-md"
                                    type="text"
                                    placeholder="{{ $t('Enter question title...') }}"
                                />
                            </div>
                            <div class="flex flex-col items-start gap-0 justify-between w-1/5">
                                <label for="qstTime">{{ $t("Time (minutes)") }}</label>
                                <input
                                    id="qstTime"
                                    required
                                    v-model="essay.time"
                                    class="w-full p-2 border border-solid border-gray-300 rounded shadow-md"
                                    value="1"
                                    min="1"
                                    max="5"
                                    type="number"
                                    placeholder="{{ $t('Enter essay time...') }}"
                                />
                            </div>
                        </div>

                        <textarea
                            required
                            v-model="essay.question"
                            class="w-full h-36 resize-none p-2 border border-solid border-gray-300 rounded shadow-md"
                            placeholder="{{ $t('Enter essay question...') }}"
                            name=""
                            id=""
                        ></textarea>

                        <div class="flex flex-row items-center justify-end mt-4 gap-4 w-full">
                            <button type="submit" class="w-32 h-10 px-2 rounded bg-NeonBlue font-semibold text-white shadow-md hover:opacity-95">{{ $t("Confirm") }}</button>
                            <button class="w-32 h-10 px-2 rounded bg-red-500 font-semibold text-white shadow-md hover:opacity-95" @click="cancelPanel">{{ $t("Cancel") }}</button>
                        </div>
                    </form>
                </div>
            </Transition>
            <Transition name="slide">
                <div v-if="type == 'Multiple-choice'" class="flex bg-white pt-4 rounded flex-col items-center w-11/12 gap-5 overflow-hidden justify-center">
                    <form @submit.prevent="editCustomQuestion()" class="flex flex-col gap-5 pb-4 items-center justify-center h-full w-full">
                        <div class="flex flex-row justify-between gap-4 items-center w-full">
                            <div class="flex flex-col items-start gap-0 justify-between w-4/5">
                                <label for="qstTitle_">{{ $t("Title") }}</label>
                                <input
                                    id="qstTitle_"
                                    required
                                    v-model="multiple_choice.name"
                                    class="w-full p-2 border border-solid border-gray-300 rounded shadow-md"
                                    type="text"
                                    placeholder="{{ $t('Enter question title...') }}"
                                />
                            </div>
                            <div class="flex flex-col items-start gap-0 justify-between w-1/5">
                                <label for="qstTime_">{{ $t("Time (minutes)") }}</label>
                                <input
                                    id="qstTime_"
                                    required
                                    v-model="multiple_choice.time"
                                    value="1"
                                    min="1"
                                    max="5"
                                    class="w-full p-2 border border-solid border-gray-300 rounded shadow-md"
                                    type="number"
                                    placeholder="{{ $t('Enter Multiple-choice time...') }}"
                                />
                            </div>
                        </div>
                        <textarea
                            required
                            v-model="multiple_choice.question"
                            class="w-full h-36 resize-none p-2 border border-solid border-gray-300 rounded shadow-md"
                            placeholder="{{ $t('Enter Multiple-choice question...') }}"
                            name=""
                            id=""
                        ></textarea>
                        <div class="flex max-h-52 relative overflow-y-auto items-center flex-col gap-2 justify-center w-full">
                            <div v-for="(option, key) in multiple_choice.options" class="flex items-center gap-2 w-full" :key="key">
                                <input
                                    :id="'option_' + key"
                                    :placeholder="`${$t('Enter option n°')}${key}`"
                                    v-model="multiple_choice.options[key]"
                                    class="w-11/12 p-2 border border-solid border-gray-300 rounded shadow-md"
                                    type="text"
                                    required
                                />
                                <button
                                    v-if="shouldRenderDeleteButton(key)"
                                    class="flex justify-center items-center absolute right-7 bottom-0 -translate-y-1/2 border border-gray-300 rounded-full w-6 h-6 cursor-pointer hover:opacity-95"
                                    @click="deleteOption(key)"
                                >
                                    <font-awesome-icon class="bg-white font-normal w-3 h-3" :icon="['fas', 'minus']" />
                                </button>
                            </div>
                            <button class="absolute w-6 h-6 flex justify-center items-center rounded-full bottom-0 border border-gray-300 -translate-y-1/2 right-0" @click="addOption">
                                <font-awesome-icon class="w-3 h-3 font-light" :icon="['fas', 'plus']" />
                            </button>
                        </div>
                        <!-- <div class="w-full flex justify-center">
                            <button class="w-full bg-[#53b9ab] h-10 px-2 rounded font-semibold text-white shadow-md hover:opacity-95" @click="addOption">Add Option</button>
                        </div> -->
                        <div class="flex flex-row mt-4 items-center justify-end gap-4 w-full">
                            <button type="submit" class="w-32 h-10 px-2 rounded bg-NeonBlue font-semibold text-white shadow-md hover:opacity-95">{{ $t("Confirm") }}</button>
                            <button class="w-32 h-10 px-2 rounded bg-red-500 font-semibold text-white shadow-md hover:opacity-95" @click="cancelPanel">{{ $t("Cancel") }}</button>
                        </div>
                    </form>
                </div>
            </Transition>
        </div>
    </div>
</template>
<script>
import axios from "axios";
import { BASE_URL } from "@/constants";
//import LoaderComponent from './LoaderComponent';
import ToastNotification from "./ToastNotification.vue";
import { useStore } from "../store/index";

export default {
    name: "UpdateCustomQuestion",
    components: { ToastNotification },
    props: {
        isShown: String,
        closePanel: Function,
        addQst: Function,
        type: String,
        question_id: String,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    data() {
        return {
            qstType: "",
            essay: {
                name: "",
                question: "",
                category: "Essay",
                time: 1,
            },
            multiple_choice: {
                name: "",
                question: "",
                category: "Multiple-choice",
                time: 1,
                options: {
                    1: "", // Initially empty
                    2: "", // Initially empty
                },
            },
        };
    },
    mounted() {
        if (this.isShown) {
            this.fetchQuestion();
        }
    },
    watch: {
        isShown(newValue) {
            if (newValue) {
                this.fetchQuestion();
            }
        },
    },
    methods: {
        cancelPanel() {
            this.qstType = "";
            this.essay = {
                name: "",
                question: "",
                category: "Essay",
                time: 1,
            };
            this.multiple_choice = {
                name: "",
                question: "",
                category: "Multiple-choice",
                time: 1,
                options: {
                    1: "",
                    2: "",
                },
            };
            this.closePanel();
        },
        editCustomQuestion() {
            let data = {};
            if (this.type == "Essay") {
                data = this.essay;
            } else if (this.type == "Multiple-choice") {
                data = this.multiple_choice;
            }
            axios
                .put(`${BASE_URL}/CustomQuestion/update/${this.question_id}`, data, { withCredentials: true })
                .then(() => {
                    this.message = "Question updated successfully!";
                    this.bgc = "success";
                    this.isVisible = true;
                    this.closePanel();
                    this.Store.allCustomQuestions = [];
                    this.Store.fetchCustomCompany();
                    this.Store.fetchCustomGoPlatform();
                })
                .catch((error) => {
                    console.log("Error while updating question", error);
                    this.message = "Updating a question has failed";
                    this.bgc = "error";
                    this.isVisible = true;
                    this.closePanel();
                });
        },
        fetchQuestion() {
            axios
                .get(`${BASE_URL}/CustomQuestion/get/${this.question_id}`, {
                    withCredentials: true,
                })
                .then((res) => {
                    if (this.type == "Essay") {
                        this.essay.name = res.data.name;
                        this.essay.time = res.data.time;
                        this.essay.question = res.data.question;
                    } else {
                        this.multiple_choice.name = res.data.name;
                        this.multiple_choice.time = res.data.time;
                        this.multiple_choice.question = res.data.question;
                        this.multiple_choice.options = res.data.options;
                    }
                });
        },
        addOption() {
            const newKey = Object.keys(this.multiple_choice.options).length + 1;
            // Using normal assignment to add a new property
            this.multiple_choice.options[newKey.toString()] = "";
        },
        deleteOption(key) {
            // Using delete to remove the property
            delete this.multiple_choice.options[key];
        },
        shouldRenderDeleteButton(key) {
            const keys = Object.keys(this.multiple_choice.options);
            const lastKey = keys[keys.length - 1];
            return key !== "1" && key !== "2" && key == lastKey;
        },
    },
};
</script>
<style scoped lang="scss">
.slide-enter-active {
    transition: all 1s ease;
    transition-delay: 0.2s;
}

.slide-leave-active {
    transition: all 0.2s ease;
}

.slide-enter-from,
.slide-leave-to {
    // transform: translateX(-500px);
    opacity: 0;
}

.slide-enter-to,
.slife-leave-from {
    opacity: 1;
    // transform: translateX(0);
}
</style>
