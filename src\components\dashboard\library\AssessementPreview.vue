<template>
    <div class="assessment-preview" v-if="showPreview">
        <div class="preview-close" @click="togglePreview"></div>

        <div class="assessmentPopup">
            <div class="closeBtnWrapper">
                <div class="w-[30%] flex gap-5">
                    <button class="previewBtn" @click="navigateToPreview">
                        <font-awesome-icon :icon="['far', 'eye']" />
                        {{ $t("Preview") }}
                    </button>

                    <button @click="addAssessment(assessment)" class="selectBtn" v-if="this.$route.path !== '/library'">
                        <font-awesome-icon :icon="['far', 'circle-check']" />
                        {{ $t("Select") }}
                    </button>
                </div>
                <button @click="togglePreview" class="nav-btn trash-btn">
                    <!-- <img loading="lazy"  decoding="async" src="@/assets/Images/icons/close-icon.svg" alt="close-icon" /> -->
                    <font-awesome-icon :icon="['fas', 'xmark']" class="nav-icon" />
                </button>
            </div>
            <div class="assessmentDesc">
                <div>
                    <div class="preview-header">
                        {{ $t(this.assessment.name) }}
                    </div>
                    <div class="details">
                        <div>
                            <font-awesome-icon class="text-NeonBlue text-lg mb-2" :icon="['fas', 'layer-group']" />
                            <h2>{{ $t("Type") }}</h2>
                            <p>{{ $t(assessment.category) }}</p>
                        </div>
                        <div>
                            <font-awesome-icon class="text-NeonBlue text-lg mb-2" :icon="['fas', 'clock']" />
                            <h2>{{ $t("Time") }}</h2>
                            <p>{{ assessment.questions_nbr > 25 ? parseInt((20 * 35) / 60) : parseInt((assessment.questions_nbr * 35) / 60) }} mins</p>
                        </div>
                        <div>
                            <font-awesome-icon class="text-NeonBlue text-lg mb-2" :icon="['fas', 'globe']" />
                            <h2>{{ $t("Language") }}</h2>
                            <p>{{ $t("English") }}, {{ !assessment?.category?.toLowerCase().includes("hard") ? $t("French") : "" }}</p>
                        </div>
                        <div>
                            <!-- <img loading="lazy"  decoding="async" src="@/assets/levelIcon.svg" alt="" width="20" height="20" /> -->
                            <font-awesome-icon class="text-NeonBlue text-lg mb-2" :icon="['fas', 'star']" />
                            <h2>{{ $t("Recommended Score") }}</h2>
                            <p>80%</p>
                        </div>
                    </div>
                </div>
                <div>
                    <div class="preview-description">
                        <h2 class="text-xl font-bold mb-3">{{ $t("Description") }}</h2>
                        <span v-html="formattedText"></span>
                    </div>
                    <div class="aboutCreator">
                        <h2 class="text-xl font-semibold text-gray-800 mb-6">{{ $t("About the subject-matter expert") }}</h2>
                        <div class="flex items-start gap-6 bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors">
                            <img loading="lazy" decoding="async" src="@/assets/Images/go_logo-copy.svg" alt="GO Platform Logo" class="w-16 h-auto object-contain flex-shrink-0" />
                            <div class="space-y-3">
                                <h3 class="text-base font-semibold text-gray-900">{{ $t("GO platform team") }}</h3>
                                <p class="text-gray-600 leading-relaxed text-[15px]">
                                    {{ $t(assessment.description_test) || $t(assessment.description) }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useStore } from "@/store";
import axios from "axios";
import { BASE_URL } from "@/constants";

export default {
    name: "AssessmentPreview",
    props: ["assessment", "showPreview", "togglePreview", "addAssessment"],
    data() {
        return {
            /*assessment: {
                      name:"Javascript Assessment",
                      description:"lorem ipsum dollor Now that you have some icons on the page, add some pieces of flair! Check out\
                                   all the styling options you can use with Font Awesome and React.lorem ipsum dollor Now that you \
                                   have some icons on the page, add some pieces of flair! Check out all the styling options you can \
                                   use with Font Awesome and React. lorem ipsum dollor Now that you have some icons on the page, add \
                                   some pieces of flair! Check out all the styling options you can use with Font Awesome and React.",
                      qst_num: 12,
                      duration: 12,
                      score: 70,
                  }*/
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        addParagraphBreaks(longText) {
            // Split the text into paragraphs using double line breaks
            var paragraphs = longText?.split(". ");

            // Join paragraphs with <br/> tags between them
            var formattedText = paragraphs?.join(".<br/><br/>");

            return formattedText;
        },
        addAssessments(assessment) {
            let data = JSON.stringify({
                assessmentId: assessment.id,
                companyId: "431",
                recommanded_score: assessment.score,
                qst_num: assessment.qst_num,
                name: assessment.name,
                duration: assessment.duration,
            });

            let config = {
                method: "post",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/AssessmentTest/companyAssessment`,
                headers: {
                    "Content-Type": "application/json",
                },
                data: data,
            };

            axios
                .request(config)
                .then((response) => {
                    console.log(JSON.stringify(response.data));
                })
                .catch((error) => {
                    console.log(error);
                });
            // console.log({ assessmentId: assessment.id });
        },
        navigateToPreview() {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"

            const url = this.$router.resolve({
                path: `/${locale}/previewAssessment`,
                query: { id: this.assessment._id },
            }).href;

            // Open the route in a new tab
            window.open(url, "_blank");
        },
    },
    computed: {
        formattedHeader() {
            // Use the same transformation logic as before
            const nameArray = this.assessment.name.split(/-/);

            // Capitalize the first letter of each word and remove "-"
            const transformedArray = nameArray.map((part) => {
                const words = part.split(/\s+/);
                return words
                    .map((word) => {
                        // Check if the word is in the list of words to be capitalized entirely
                        const capitalWords = ["kpmg", "dragnet", "gtco", "nnpc", "pwc", "zenithbank", "xml", "aws", "vba"];

                        // Capitalize the entire word if the lowercase version is in the list
                        if (capitalWords.includes(word?.toLowerCase())) {
                            return word.toUpperCase();
                        }

                        // Capitalize the first letter of each word
                        return word.charAt(0).toUpperCase() + word?.slice(1)?.toLowerCase();
                    })
                    .join(" ");
            });

            // Join the parts into a sentence
            return transformedArray.join(" ");
        },
        formattedText() {
            // Translate the raw text before formatting
            const rawText = this.assessment.description_test !== "" ? this.$t(this.assessment.description_test) : this.$t(this.assessment.description);
            return this.addParagraphBreaks(rawText);
        },
    },
};
</script>

<style lang="scss" scoped>
.assessment-preview {
    position: fixed;
    z-index: 1000;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);

    .assessmentPopup {
        width: 90%;
        max-width: 800px;
        background: white;
        border-radius: 6px;
        padding: 32px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        max-height: 90vh; /* Fallback for browsers that don't support dvh */
        max-height: 90dvh;
        overflow-y: auto;

        .closeBtnWrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            position: relative;

            .trash-btn {
                position: absolute;
                top: -16px;
                right: -16px;
                padding: 10px;
                background: white;
                border-radius: 50%;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                width: 40px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                transition: all 0.2s ease;

                &:hover {
                    transform: rotate(90deg);
                    background: #f8f9fa;
                }

                .nav-icon {
                    font-size: 1.25rem;
                    color: #666;
                }
            }

            .selectBtn {
                padding: 12px 24px;
                border-radius: 6px;
                background: #2196f3;
                color: #fff;
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                transition: all 0.2s ease;
                border: none;
                cursor: pointer;

                &:hover {
                    opacity: 85%;
                }
            }

            .previewBtn {
                padding: 12px 24px;
                border-radius: 6px;
                // background: #dbeafe;
                color: #111827;
                border: 1px solid #d1d5db;
                display: flex;
                align-items: center;
                gap: 8px;
                font-weight: 600;
                transition: all 0.2s ease;
                cursor: pointer;

                &:hover {
                    color: #2196f3;
                    border-color: #2196f3;
                }
            }
        }

        .assessmentDesc {
            display: flex;
            flex-direction: column;
            gap: 32px;
        }

        .preview-header {
            font-size: 28px;
            font-weight: 700;
            color: #2196f3;
            margin-bottom: 24px;
        }

        .details {
            display: grid;
            grid-template-columns: repeat(4, 1fr); /* Changed to 4 columns */
            gap: 16px;
            margin-bottom: 24px;

            /* Responsive adjustments */
            @media (max-width: 768px) {
                grid-template-columns: repeat(2, 1fr);
            }

            @media (max-width: 480px) {
                grid-template-columns: 1fr;
            }

            div {
                padding: 16px;
                //background: #eff6ff;
                border: 1px solid #d1d5db;
                border-radius: 12px;
                transition: transform 0.2s ease;
                min-width: 150px; /* Ensure minimum width for smaller screens */
            }
        }

        .preview-description {
            flex: 1;
            overflow: hidden;
            display: flex;
            flex-direction: column;

            span {
                overflow-y: auto;
                padding-right: 8px;
                margin-bottom: 16px;
                font-size: 16px;
            }
        }

        .aboutCreator {
            margin-top: auto;
            padding-top: 24px;
        }
    }
}
</style>
