<template>
    <div v-if="isLoading" class="loader bg-[#fff] rounded-[8px] p-2">
        <LoadingComponent />
    </div>
    <div v-else class="company-container bg-[#fff] rounded-[8px] p-2">
        <div class="navigation-tabs">
            <div class="nav-links">
                <router-link to="/companyprofile" :class="`${this.$route.path == '/companyprofile' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'building']" class="w-4 h-4" />
                    <span class="text-base font-semibold">{{ $t("Company profile") }}</span>
                </router-link>

                <router-link to="/settings" :class="`${this.$route.path == '/userrofile' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'user-tie']" class="w-4 h-4" />
                    <span class="text-base font-semibold">{{ $t("Recruiter profile") }}</span>
                </router-link>
                <router-link to="/InvitedCoworkerTab" :class="`${this.$route.path == '/InvitedCoworkerTab' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'users']" class="w-4 h-4" />
                    <span class="text-base font-semibold">{{ $t("Invited Coworkers") }}</span>
                </router-link>
            </div>
        </div>

        <div class="profile_header">
            <div class="profile_cover">
                <div class="cover-placeholder flex flex-col items-center justify-center h-full w-full text-center">
                    <span v-if="!company.cover" class="text-gray-500 mb-2">{{ $t("No cover image uploaded") }}</span>
                    <label
                        for="cover_image"
                        style="position: relative; z-index: 99999; background-color: #878b94; padding: 10px"
                        class="text-blue-400 cursor-pointer font-semibold transition-colors duration-200 hover:text-blue-500"
                        >{{ $t("Upload image") }}</label
                    >
                    <input type="file" id="cover_image" @change="change_cover($event)" style="display: none" />
                    <img v-if="company.cover" loading="lazy" decoding="async" class="cover_img mt-2" :src="getImgUrl(this.company.cover)" alt="" />
                </div>
            </div>
            <div class="profile_infos">
                <div
                    class="profile_image relative flex items-center justify-center w-36 h-36 rounded-full border-2 border-blue-300 bg-white overflow-hidden shadow-md"
                    :style="company.logo ? { backgroundImage: `url('data:image/PNG;base64,${company.logo}')`, backgroundSize: 'cover', backgroundPosition: 'center' } : {}"
                >
                    <label for="profile_image" class="edit_photo flex items-center justify-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
                        <img loading="lazy" decoding="async" src="@/assets/Images/icons/icon_cam.svg" width="28" height="28" />
                    </label>
                    <input type="file" id="profile_image" @change="change_profileImg($event)" class="hidden" />
                </div>
                <div class="infos">
                    <span>
                        {{ company.name }}
                        <button @click="toggleModal">
                            <img loading="lazy" decoding="async" src="@/assets/Images/icons/icon_edit.svg" width="25" height="25" />
                        </button>
                    </span>
                    <span>{{ company.slogan }}</span>
                    <div>
                        <span><img loading="lazy" decoding="async" src="@/assets/Images/icons/icon_category.svg" width="17" height="15" alt="" /> {{ company.industry }}</span>
                        <span><img loading="lazy" decoding="async" src="@/assets/Images/icons/icon_location.svg" width="17" height="15" alt="" /> {{ company.location }}</span>
                        <span><img loading="lazy" decoding="async" src="@/assets/Images/icons/icon_bag.svg" width="17" height="15" alt="" /> {{ company.employees_count }} {{ $t("employees") }}</span>
                        <span><img loading="lazy" decoding="async" src="@/assets/Images/icons/icon_type.svg" width="17" height="15" alt="" /> {{ company.type }}</span>
                    </div>
                </div>
                <div class="links">
                    <a :href="company.website" target="_blank" rel="noopener noreferrer">
                        <font-awesome-icon :icon="['fas', 'globe']" class="h-5 w-5" />
                    </a>
                    <a :href="company.facebook" target="_blank" rel="noopener noreferrer">
                        <font-awesome-icon :icon="['fab', 'facebook']" class="h-5 w-5" />
                    </a>
                    <a :href="company.linkedIn" target="_blank" rel="noopener noreferrer">
                        <font-awesome-icon :icon="['fab', 'linkedin']" class="h-5 w-5" />
                    </a>
                </div>
            </div>
        </div>
        <div class="overview">
            <span class="title">{{ $t("Overview") }}</span>
            <div class="about">
                <span>{{ $t("Get to know") }} {{ company.name }}</span>
                <p v-html="company.description"></p>
            </div>

            <div class="benefits">
                <span class="titre">{{ $t("Benefits") }}</span>
                <div>
                    <span v-for="(benefit, index) in company.benefit_list" :key="index">
                        <img loading="lazy" decoding="async" :src="require(`@/assets/Images/icons/${benefit.icon_path}`)" alt="" />
                        {{ benefit.benefit }}
                    </span>
                </div>
                <span class="titre">{{ $t("Team") }}</span>
                <div class="team">
                    <div class="member" v-for="(member, index) in company.team" :key="index">
                        <img loading="lazy" decoding="async" :src="require(`@/assets/Images/icons/${member.image}`)" alt="" width="35" height="35" />
                        <span class="font-semibold"
                            >{{ member.name }}
                            <span class="role">{{ member.role }}</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="tags">
            <div class="tag" v-for="(tag, index) in company.taglist" :key="index">
                <img loading="lazy" decoding="async" :src="require(`@/assets/Images/icons/${tag.image}`)" alt="" height="73" width="73" />
                <span>
                    {{ tag.title }}
                    <span class="text">{{ tag.text }}</span>
                </span>
            </div>
        </div>
    </div>
    <!--
    <div v-if="show" class="modal-overlay fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm transition-opacity duration-300">
        <div class="modal-content bg-white rounded-lg shadow-xl w-[90%] max-w-3xl max-h-[90vh] overflow-y-auto transform transition-transform duration-300 scale-100">
            <div class="modal-header flex items-center justify-between p-6 border-b border-gray-200">
                <h2 class="text-2xl font-semibold text-gray-800">Edit Company Profile</h2>
                <button @click="toggleModal" class="close-btn text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none">
                    <font-awesome-icon :icon="['fas', 'xmark']" class="w-6 h-6" />
                </button>
            </div>
            <div class="modal-body p-6">
                <form @submit.prevent="saveCompanyProfile" class="space-y-6">
                    <div class="form-group">
                        <label for="company-name" class="block text-sm font-medium text-gray-700 mb-1">Company Name</label>
                        <input
                            type="text"
                            id="company-name"
                            v-model="editCompany.name"
                            required
                            class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>
                    <div class="form-group">
                        <label for="company-slogan" class="block text-sm font-medium text-gray-700 mb-1">Slogan</label>
                        <input
                            type="text"
                            id="company-slogan"
                            v-model="editCompany.slogan"
                            class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        />
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="company-industry" class="block text-sm font-medium text-gray-700 mb-1">Industry</label>
                            <input
                                type="text"
                                id="company-industry"
                                v-model="editCompany.industry"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div class="form-group">
                            <label for="company-location" class="block text-sm font-medium text-gray-700 mb-1">Location</label>
                            <input
                                type="text"
                                id="company-location"
                                v-model="editCompany.location"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="company-employees" class="block text-sm font-medium text-gray-700 mb-1">Number of Employees</label>
                            <input
                                type="text"
                                id="company-employees"
                                v-model="editCompany.employees_count"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div class="form-group">
                            <label for="company-type" class="block text-sm font-medium text-gray-700 mb-1">Company Type</label>
                            <input
                                type="text"
                                id="company-type"
                                v-model="editCompany.type"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="company-description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea
                            id="company-description"
                            v-model="editCompany.description"
                            rows="4"
                            class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                        ></textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="company-website" class="block text-sm font-medium text-gray-700 mb-1">Website</label>
                            <input
                                type="url"
                                id="company-website"
                                v-model="editCompany.website"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div class="form-group">
                            <label for="company-facebook" class="block text-sm font-medium text-gray-700 mb-1">Facebook</label>
                            <input
                                type="url"
                                id="company-facebook"
                                v-model="editCompany.facebook"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="company-linkedin" class="block text-sm font-medium text-gray-700 mb-1">LinkedIn</label>
                            <input
                                type="url"
                                id="company-linkedin"
                                v-model="editCompany.linkedIn"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                        <div class="form-group">
                            <label for="company-instagram" class="block text-sm font-medium text-gray-700 mb-1">Instagram</label>
                            <input
                                type="url"
                                id="company-instagram"
                                v-model="editCompany.instagram"
                                class="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    </div>
                    <div class="form-buttons flex justify-end space-x-4 pt-4 border-t border-gray-200 mt-6">
                        <button
                            type="button"
                            @click="toggleModal"
                            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
                        >
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 bg-NeonBlue text-white rounded-md hover:opacity-85 transition-colors duration-200 focus:outline-none">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    -->
</template>

<script>
// import imageoCarousel from "@/components/imageCarousel.vue";
import LoadingComponent from "@/components/LoadingComponent.vue";
import { BASE_URL } from "@/constants";
import axios from "axios";

export default {
    name: "companyProfile",
    components: {
        // imageCarousel,
        LoadingComponent,
    },
    data() {
        return {
            selected: [true, false, false],
            current: 0,
            show: false,
            company: {},
            editCompany: {}, // This will hold the editable copy of company data
            isLoading: true,
            logo: "",
            cover: "",
            jobList: [
                {
                    picture: "profile_image.svg",
                    role: "Compensation & Benefit Consultant",
                    company: "Deall Jobs",
                    location: "online",
                    type: "Freelance",
                    salary: "Negotiable",
                    OnSite: "Remote",
                },
                {
                    picture: "profile_image.svg",
                    role: "Compensation & Benefit Consultant",
                    company: "Deall Jobs",
                    location: "online",
                    type: "Freelance",
                    salary: "Negotiable",
                    OnSite: "Remote",
                },
                {
                    picture: "profile_image.svg",
                    role: "Compensation & Benefit Consultant",
                    company: "Deall Jobs",
                    location: "online",
                    type: "Freelance",
                    salary: "Negotiable",
                    OnSite: "Remote",
                },
                {
                    picture: "profile_image.svg",
                    role: "Compensation & Benefit Consultant",
                    company: "Deall Jobs",
                    location: "online",
                    type: "Freelance",
                    salary: "Negotiable",
                    OnSite: "Remote",
                },
                {
                    picture: "profile_image.svg",
                    role: "Compensation & Benefit Consultant",
                    company: "Deall Jobs",
                    location: "online",
                    type: "Freelance",
                    salary: "Negotiable",
                    OnSite: "Remote",
                },
                {
                    picture: "profile_image.svg",
                    role: "Compensation & Benefit Consultant",
                    company: "Deall Jobs",
                    location: "online",
                    type: "Freelance",
                    salary: "Negotiable",
                    OnSite: "Remote",
                },
            ],
        };
    },
    computed: {},
    methods: {
        selectTab(num) {
            this.selected[this.current] = false;
            this.selected[num] = true;
            this.current = num;
        },
        toggleModal: function () {
            this.show = !this.show;
            if (this.show) {
                // Create a deep copy of company data for editing
                this.editCompany = JSON.parse(JSON.stringify(this.company));
            }
        },
        /*
        saveCompanyProfile() {
            // Update the company object with edited values
            let updatedCompany = {
                name: this.editCompany.name,
                slogan: this.editCompany.slogan,
                industry: this.editCompany.industry,
                location: this.editCompany.location,
                employees_count: this.editCompany.employees_count,
                type: this.editCompany.type,
                description: this.editCompany.description,
                taglist: this.editCompany.taglist,
                benefit_list: this.editCompany.benefit_list,
                team: this.editCompany.team,
                website: this.editCompany.website,
                facebook: this.editCompany.facebook,
                linkedIn: this.editCompany.linkedIn,
                instagram: this.editCompany.instagram,
                twitter: this.editCompany.twitter,
            };
            console.log(updatedCompany);

            axios
                .put(
                    `${BASE_URL}/company/update`,
                    {
                        company_id: this.editCompany.company_id,
                        updates: updatedCompany,
                    },
                    {
                        withCredentials: true,
                    },
                )
                .then(() => {
                    // Update the local company object
                    this.company = this.editCompany;
                    this.toggleModal();
                    // You might want to add a success notification here
                    //alert("Company profile updated successfully!");
                })
                .catch((error) => {
                    if (error.response) {
                        console.log("Backend error:", error.response.data);
                        alert("Failed to update company profile: " + (error.response.data.message || "Please try again."));
                    } else {
                        console.log("Error:", error.message);
                        alert("Failed to update company profile. Please try again.");
                    }
                });
        },
        */
        /*
        toggleModal: function () {
            this.show = !this.show;
        },
        updateProfile: function (updates) {
            this.company = updates;
            this.toggleModal();
            let updatedCompany = {
                name: this.company.name,
                slogan: this.company.slogan,
                industry: this.company.industry,
                location: this.company.location,
                employees_count: this.company.employees_count,
                type: this.company.type,
                description: this.company.description,
                taglist: this.company.taglist,
                benefit_list: this.company.benefit_list,
                team: this.company.team,
                website: this.company.website,
                facebook: this.company.facebook,
                linkedIn: this.company.linkedIn,
                instagram: this.company.instagram,
                twitter: this.company.twitter,
            };

            axios
                .put(`${BASE_URL}/company/update`, {
                    name: this.company.name,
                    updates: updatedCompany,
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        */
        change_cover(event) {
            if (event.target.files[0]?.type !== "image/png") {
                alert("Please upload a PNG file");
                return;
            }

            this.cover = event.target.files[0];
            var formdata = new FormData();
            formdata.append("cover", this.cover);
            var requestOptions = {
                method: "POST",
                body: formdata,
                credentials: "include",
                redirect: "follow",
                withCredentials: true,
            };

            fetch(`${BASE_URL}/company/changeCover`, requestOptions).then(async () => {
                await this.fetchCompanyData();
            });
        },
        change_profileImg(event) {
            if (event.target.files[0]?.type !== "image/png") {
                alert("Please upload a PNG file");
                return;
            }
            if (event.target.files[0].size > 100000) {
                alert("please upload a logo with smaller size");
                return;
            }

            this.company.profile_image = event.target.files[0].name;
            this.logo = event.target.files[0];
            this.uploadNewLogo(this.logo);
        },
        uploadNewLogo(logo) {
            var formdata = new FormData();
            formdata.append("logo", logo);
            var requestOptions = {
                method: "POST",
                body: formdata,
                credentials: "include",
                redirect: "follow",
                withCredentials: true,
            };

            fetch(`${BASE_URL}/company/changeLogo`, requestOptions).then(async () => {
                await this.fetchCompanyData();
            });
        },
        uploadNewCover(cover) {
            var formdata = new FormData();
            formdata.append("cover", cover);
            var requestOptions = {
                method: "POST",
                body: formdata,
                credentials: "include",
                redirect: "follow",
                withCredentials: true,
            };

            fetch(`${BASE_URL}/company/changeCover`, requestOptions).then(() => {});
        },
        add_photos: function (event) {
            this.company.company_images = event.target.files;
        },
        getImgUrl(imgFileId) {
            if (imgFileId) {
                var image = `data:image/png;base64,${imgFileId}`;
                return image;
            }
            return "https://storage.googleapis.com/proudcity/mebanenc/uploads/2021/03/placeholder-image.png";
        },

        async fetchCompanyData() {
            this.isLoading = true;
            await axios
                .get(`${BASE_URL}/company/info`, {
                    withCredentials: true,
                    headers: {
                        "Cache-Control": "no-cache", // or 'no-store'
                    },
                })
                .then((res) => {
                    this.company = res.data;
                    this.isLoading = false;
                })
                .catch((err) => {
                    this.isLoading = false;
                    console.log({ err: err.response.data });
                });
        },
    },
    async mounted() {
        // await axios
        //   .get(`${BASE_URL}/company/info`, {
        //     withCredentials: true,
        //   headers: {
        //     "Cache-Control": "no-cache", // or 'no-store'
        //   },
        //   })
        //   .then((res) => {
        //     this.company = res.data;
        // })
        // .catch((err) => {
        //   console.log({ err: err.response.data });
        // });

        await this.fetchCompanyData();
    },
};
</script>

<style lang="scss" scoped>
body {
    font-family: "Inter";
    font-style: normal;
}

.company-container {
    padding: 24px 8px;

    //div{border: 2px solid #000;}
    .profile_header {
        margin: 1rem 0;

        .edit_photo {
            display: flex;
            border-radius: 50%;
            padding: 2%;
            justify-content: center;
            align-items: center;
            position: relative;
            background: transparent;
            height: fit-content;
            background: rgba(0, 0, 0, 0.5);
        }

        .edit_photo:hover {
            background: rgba(0, 0, 0, 0.8);
        }

        .profile_cover {
            position: relative;
            width: 100%;
            height: 14rem;
            margin-bottom: 0; // Remove negative margin
            display: flex;
            justify-content: center;
            align-items: flex-end;
            background: #f8fafc;
            border-radius: 1rem 1rem 0 0;
            overflow: hidden;

            .cover_img {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                object-fit: cover;
                border-radius: 1rem 1rem 0 0;
            }
        }

        .profile_infos {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            //justify-content: flex-start;
            margin-top: -4.5rem;
            position: relative;
            z-index: 2;
            gap: 2rem;

            .profile_image {
                //box-shadow: 0 4px 16px 0 #e0e7ef33;
                margin-bottom: 0;
                margin-right: 2rem;
                margin-left: 2rem;

                .profile_img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 50%;
                    border: 2px solid #e3eafc;
                }

                .edit_photo {
                    position: absolute;
                    background: #2196f3;
                    border-radius: 50%;
                    padding: 0.4rem;
                    //box-shadow: 0 2px 8px 0 #e0e7ef33;
                    cursor: pointer;
                    transition: background 0.2s;

                    &:hover {
                        opacity: 85%;
                    }
                }
            }

            .infos {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                text-align: left;
                margin-top: 0.5rem;
                padding-left: 2rem;

                > :first-child {
                    font-weight: 700;
                    font-size: 1.5rem;
                    color: #2f343a;
                    display: flex;
                    gap: 1rem;
                    align-items: center;
                    margin-bottom: 0.75rem; // Add space below company name

                    button {
                        background: #f8fafc;
                        border: none;
                        border-radius: 9999px;
                        padding: 0.3rem 0.5rem;
                        margin-left: 0.5rem;
                        transition: background 0.2s;
                        cursor: pointer;

                        &:hover {
                            background: #e3f0fc;
                        }
                    }
                }

                > :nth-child(2) {
                    font-weight: 600;
                    font-size: 1rem;
                    color: #6b7280;
                    margin-bottom: 16px;
                }

                > :nth-child(3) {
                    font-weight: 600;
                    font-size: 0.95rem;
                    color: #585f65;
                    width: 100%;
                    display: flex;
                    gap: 0.5rem;
                    flex-wrap: wrap;
                    justify-content: center;

                    span {
                        background: #fff;
                        border: 1px solid #e3eafc;
                        border-radius: 0.5rem;
                        padding: 0.25rem 0.75rem;
                        display: flex;
                        align-items: center;
                        gap: 0.25rem;
                    }
                }
            }

            .links {
                display: flex;
                gap: 0.5rem;
                margin-top: 0.2rem;
                justify-content: flex-start;
                padding-left: 2rem;
                margin-bottom: 2rem; // Add space below links before overview

                a {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #fff;
                    color: #2196f3;
                    border: 1px solid #e3eafc;
                    border-radius: 50%;
                    padding: 0.5rem;
                    box-shadow: 0 2px 8px 0 #e0e7ef33;
                    transition: background 0.2s;
                    flex-shrink: 0; /* Prevents the icon from shrinking */

                    &:hover {
                        background: #e3f0fc;
                    }
                }
            }
        }
    }

    .overview {
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        color: #2f343a;
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1rem;
        padding-left: 2rem;
        padding-right: 2rem;
        margin: 1rem 0;

        .title {
            font-weight: 700;
            font-size: 24px;
            line-height: 18px;
            text-align: center;
            color: #2196f3;
            display: flex;
            flex-direction: column;
            align-items: baseline;
            grid-row: 1;
            grid-column: 1 / span 3;
        }

        .title::after {
            background: #b9b9b9;
            width: 100%;
            height: 1px;
            content: "";
            margin: 0.9rem 0;
        }

        .about {
            grid-column: 1 / span 2;

            p {
                line-height: 22px;
                font-size: 14px;
                margin-top: 8px;
            }

            span {
                font-weight: 700;
                font-size: 16px;
                line-height: 18px;
            }
        }

        .benefits {
            grid-column: 3;
            grid-row: 2 / span 2;
            display: flex;
            flex-direction: column;
            height: fit-content;
            padding-left: 2rem;

            .titre {
                font-weight: 700;
                font-size: 12.2674px;
                line-height: 18px;
                margin: 1rem 0;
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .titre::after {
                background: #b9b9b9;
                width: 100%;
                height: 0.5px;
                content: "";
            }

            > :nth-child(2) {
                display: flex;
                flex-wrap: wrap;
                align-content: flex-start;
                gap: 0.5rem;

                span {
                    font-weight: 500;
                    font-size: 12.2674px;
                    line-height: 18px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 0.5rem;
                    gap: 0.25rem;
                    background: #ffffff;
                    border: 0.876242px solid #e9eaed;
                    border-radius: 3.50497px;
                    height: fit-content;
                }
            }

            .team {
                display: flex;
                flex-direction: column;
                overflow-y: scroll;
                gap: 0.75rem;
                padding: 0 1rem;
                height: 20rem;
                padding-right: 2rem;

                .member {
                    background: #fff;
                    border: 0.876242px solid #e9eaed;
                    border-radius: 3.50497px;
                    padding: 1rem;
                    display: flex;
                    gap: 1rem;

                    span {
                        display: flex;
                        flex-direction: column;
                        font-weight: 700;
                        font-size: 12.2674px;
                        line-height: 18px;

                        .role {
                            font-weight: 500;
                            font-size: 12.2674px;
                            line-height: 18px;
                        }
                    }
                }
            }
        }
    }

    .tags {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        gap: 1rem;
        margin: 2rem 0 0;

        .tag {
            flex-basis: 30%;
            display: flex;
            align-items: center;
            padding: 1%;
            background: #fff;
            border: 0.876242px solid #e9eaed;
            border-radius: 12.2674px;
            gap: 0.5rem;

            span {
                font-weight: 700;
                font-size: 12.2674px;
                line-height: 18px;
                text-transform: uppercase;
                color: #2f343a;
                display: flex;
                flex-direction: column;

                .text {
                    font-weight: 500;
                    font-size: 10.2674px;
                    line-height: 14px;
                    text-transform: lowercase;
                }
            }
        }
    }

    ::-webkit-scrollbar {
        width: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: #dbdbdc;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #b8b8b9;
    }

    input[type="file"] {
        display: none;
    }

    .our_company {
        background: #fff;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        width: 100%;
        gap: 2rem;

        span {
            font-weight: 500;
            font-size: 27.3142px;
            line-height: 41px;
            display: flex;
            justify-content: space-between;

            label {
                background: none;
                border: none;
                color: #2196f3;
                font-size: 18px;
            }
        }
    }

    .cover-placeholder {
        min-height: 12rem;
        background: #f8fafc;
        border-radius: 1rem 1rem 0 0;
    }
}

/*----------------------------------------------------------------*/

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-top: 48px; */
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 2px solid #edeff2;
    margin-bottom: 25px;
}

.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-family: "Roboto";
    font-weight: 700;
    font-size: 14px;
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links .nav-icon {
    font-size: 20px;
}

.nav-links img {
    margin-right: 12px;
}

::placeholder {
    font-family: "Roboto";
    font-weight: 700;
    font-size: 11px;
    color: #adb8cc;
}

input {
    border: none;
}

input:focus {
    outline: none;
}

.nav-links a::after {
    content: "";
    background: #7d8fb3;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    transition: width 0.4s ease-in-out;
    border-radius: 25px;
}

.nav-links > a:hover::after {
    width: 100%;
}

.nav-links a.active::after {
    background: #2196f3;
    width: 100%;
}

.nav-links a.active * {
    color: #2196f3;
}

.loader {
    width: 100%;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.modal-body {
    padding: 16px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
}

.cancel-btn {
    padding: 8px 16px;
    background-color: #f7f9fc;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.save-btn {
    padding: 8px 16px;
    background-color: #2196f3;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.close-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
}
</style>
