<template>
    <div>
        <h1>{{ $t("Access & Security") }}</h1>
        <hr />
        <div class="button-group">
            <button :class="{ inactive: !securityTab }" @click="swithcTab">{{ $t("Security") }}</button>
            <button :class="{ inactive: securityTab }" @click="swithcTab">{{ $t("Deligated Access") }}</button>
        </div>
        <div v-if="securityTab" id="security">
            <div class="form-control">
                <!-- prettier-ignore -->
                <label for="emsail">{{ $t("Sign-in Email") }}</label>
                <input type="email" name="email" value="<EMAIL>" />
            </div>
            <div class="form-control">
                <!-- prettier-ignore -->
                <label for="password">{{ $t("Password") }}</label>
                <span class="hidden-password" v-if="!showPassword" @click="showPasswordField">{{ $t("Change Password") }}</span>
                <input v-if="showPassword" type="text" name="password" id="password" value="password" />
            </div>
            <hr />
            <div class="form-control">
                <!-- prettier-ignore -->
                <label for="fa-auth">{{ $t("2-FA Authentification") }}</label>
                <input type="checkbox" name="fa-auth" />
            </div>
            <div class="form-control">
                <!-- prettier-ignore -->
                <label for="phone">{{ $t("Phone Number") }}</label>
                <input type="tel" name="phone" value="+380 93 123 45 67" />
            </div>
            <div class="form-control">
                <!-- prettier-ignore -->
                <label for="fa-auth">{{ $t("Reserve Codes") }}</label>
                <!-- prettier-ignore -->
                <span>{{ $t("9 out of 10 left") }}</span>
            </div>
            <!-- prettier-ignore -->
            <button class="btn">{{ $t("+ Generate new codes") }}</button>
            <hr />
            <!-- prettier-ignore -->
            <span class="text-span">{{ $t("Last Sign-in") }}</span>
            <!-- prettier-ignore -->
            <span class="text-span">{{ $t("today at 18:34") }}</span>
            <div class="active-sessions">
                <!-- prettier-ignore -->
                <span>{{ $t("Total Active Session (5)") }}</span>
                <button class="btn outline-btn">{{ $t("All") }}</button>
            </div>
            <div class="session">
                <span>DESKTOP-6TIG6EC • Kyiv, Ukraine</span>
                <span class="secondary-text">Chrome • Used right now</span>
            </div>
            <div class="session">
                <span>Iphone 11 • Kyiv, Ukraine</span>
                <span class="secondary-text">Chrome • 04/19/2022</span>
            </div>
            <button class="btn">{{ $t("+ Reset all active sessions") }}</button>
        </div>
        <div v-if="!securityTab" id="access">
            <span class="title-span">{{ $t("You’ve been granted access to the following cabinets:") }}</span>
            <div>
                <div class="profile">
                    <div class="profile-name">
                        <img loading="lazy" decoding="async" src="@/assets/Images/profile.png" alt="profile" />
                        <span>Profile Name</span>
                    </div>
                    <button class="btn outline-btn">{{ $t("Sign in") }}</button>
                </div>
                <div class="profile">
                    <div class="profile-name">
                        <img loading="lazy" decoding="async" src="@/assets/Images/profile.png" alt="profile" />
                        <span>Profile Name</span>
                    </div>
                    <button class="btn outline-btn">{{ $t("Sign in") }}</button>
                </div>
            </div>
            <hr />
            <span class="title-span">{{ $t("You gave access to the following cabinets:") }}</span>
            <div>
                <div class="grid-row table-header">
                    <div>{{ $t("Name") }}</div>
                    <div>{{ $t("Access") }}</div>
                    <div>{{ $t("Actions") }}</div>
                </div>
                <div class="grid-row table-data">
                    <div class="name">Jake Gilehaal</div>
                    <div class="access">{{ $t("View Channels & Profiles, View Finances Accure Money") }}</div>
                    <div class="actions">
                        <button>
                            <img loading="lazy" decoding="async" src="@/assets/Images/icons/edit.svg" alt="edit" />
                        </button>
                        <button>
                            <img loading="lazy" decoding="async" src="@/assets/Images/icons/delete.svg" alt="delete" />
                        </button>
                    </div>
                </div>
                <div class="grid-row table-data">
                    <div class="name">Jake Gilehaal</div>
                    <div class="access">{{ $t("View Channels & Profiles, View Finances Accure Money") }}</div>
                    <div class="actions">
                        <button>
                            <img loading="lazy" decoding="async" src="@/assets/Images/icons/edit.svg" alt="edit" />
                        </button>
                        <button>
                            <img loading="lazy" decoding="async" src="@/assets/Images/icons/delete.svg" alt="delete" />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "SecuritySettingsView",
    data: function () {
        return {
            securityTab: true,
            showPassword: false,
        };
    },
    methods: {
        swithcTab: function (e) {
            if (e["target"]["className"] === "inactive") this.securityTab = !this.securityTab;
        },
        showPasswordField: function () {
            this.showPassword = !this.showPassword;
        },
    },
};
</script>

<style scoped>
h1 {
    font-family: "DM Sans";
    font-weight: 700;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
}

hr {
    all: unset;
    display: block;
    margin: 16px 24px;
    background: #cfd8dc;
    height: 1px;
}

button {
    all: unset;
    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    padding: 4px 20px;
    background: #2196f3;
    box-shadow:
        0px 0px 2px rgba(0, 0, 0, 0.14),
        0px 2px 2px rgba(0, 0, 0, 0.12),
        0px 1px 3px rgba(0, 0, 0, 0.2);
    border-radius: 70px;
}

label {
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    color: #333333;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input {
    all: unset;
    margin: 0px;
    padding: 0px;
    font-weight: 400;
    font-size: 16px;
    color: #333333;
    text-align: end;
}

#access,
#security {
    padding: 24px;
}

.inactive {
    background: none;
    border: none;
    box-shadow: none;
    color: #82888d;
}

.button-group {
    display: flex;
    justify-content: center;
}

.btn {
    display: block;
    margin: 16px;
    margin-right: 0px;
    margin-left: auto;
}

.outline-btn {
    background: none;
    border: none;
    box-shadow: none;
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    color: #2196f3;
}
.form-control {
    display: flex;
    justify-content: space-between;
    margin: 24px 0px;
}

.hidden-password {
    font-weight: 500;
    font-size: 16px;
    letter-spacing: 0.5px;
    color: #2196f3;
}

.text-span {
    display: block;
    margin: 16px 0px;
}

.active-sessions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: -8px;
}

.session {
    border-bottom: 1px solid #cfd8dc;
    padding-bottom: 16px;
}

.session > span {
    display: block;
    margin: 8px 0px;
}

.secondary-text {
    opacity: 0.8;
}

.title-span {
    display: block;
    margin: 24px 0px;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
}

.profile {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.profile-name {
    display: flex;
    align-items: center;
}

.profile-name > img {
    border-radius: 50%;
    margin-right: 16px;
}

.grid-row {
    display: grid;
    grid-template-columns: 3fr 5fr 2fr;
    gap: 24px;
    padding: 0px 16px;
}

.table-header {
    font-weight: 700;
    font-size: 16px;
    color: #333333;
    margin-bottom: 16px;
}

.table-data {
    align-items: center;
    border-top: 1px solid #cfd8dc;
    padding: 8px 0px;
}

.table-data > .name {
    font-weight: 400;
    font-size: 16px;
    color: #333333;
}

.table-data > .access {
    font-weight: 400;
    font-size: 13px;
    color: #333333;
}

.actions {
    display: flex;
    justify-content: space-around;
    align-items: center;
}

.actions > button {
    all: unset;
}
</style>
