<template>
    <div v-if="isVisible" class="confirmWrapper">
        <div class="closeArea" @click="toggleConfirmEmail"></div>
        <div class="confirmEmail">
            <font-awesome-icon :icon="['far', 'envelope']" class="email-icon" />

            <div class="text-content">
                <h2 class="title">{{ $t("Email Confirmation Sent!") }}</h2>
                <p class="description">{{ $t("We've sent a verification link to your email address. Please check your inbox and click the link to confirm your account.") }}</p>
            </div>

            <button class="close-button" @click="toggleConfirmEmail">{{ $t("Close") }}</button>
        </div>
    </div>
</template>

<script>
export default {
    name: "ConfirmEmail",
    props: {
        isVisible: Boolean,
        toggleConfirmEmail: Function,
    },
};
</script>

<style scoped lang="scss">
.confirmWrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(5px);
    background: rgba(0, 0, 0, 0.4);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeIn 0.3s ease-out;

    .closeArea {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
    }

    .confirmEmail {
        background: white;
        border-radius: 16px;
        padding: 2.5rem;
        width: 90%;
        max-width: 600px;
        text-align: center;
        position: relative;
        animation: slideUp 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);

        .email-icon {
            color: #2196f3;
            font-size: 6rem;
            animation: float 3s ease-in-out infinite;
        }

        .text-content {
            margin: 1.5rem 0;

            .title {
                color: #0f172a;
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 1rem;
            }

            .description {
                color: #64748b;
                font-size: 1rem;
                line-height: 1.6;
                margin-bottom: 1.5rem;
            }
        }

        .close-button {
            background: #2196f3;
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 1rem;

            &:hover {
                opacity: 0.85;
            }
        }
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes float {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-8px);
    }
}
</style>
