<template>
    <div class="flex items-center mb-5 m-2">
        <div ref="container" class="flex flex-col justify-between w-full animate-fadeInUp">
            <!-- Label -->
            <p class="font-semibold text-gray-700 w-full">{{ label }}</p>

            <div class="flex flex-row w-full items-center justify-between gap-4">
                <!-- Progress Bar -->
                <div class="relative h-3 rounded-full w-[80%] bg-gray-200 overflow-hidden">
                    <!-- Animated Gradient Progress -->
                    <div class="absolute top-0 left-0 h-full rounded-full transition-all duration-700 ease-out" :class="gradientClass" :style="{ width: animatedProgress + '%' }"></div>

                    <!-- Highlight Circle -->
                    <div
                        class="absolute h-3 w-3 bg-white border-2 border-blue-500 rounded-full transition-all duration-700 ease-in-out shadow-circle"
                        :style="{ left: `calc(${animatedProgress}% - 8px)` }"
                    ></div>
                </div>

                <!-- Animated Percentage -->
                <p class="font-bold text-gray-700 text-xl animate-fadeInUp delay-100">{{ displayProgress }}%</p>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        label: {
            type: String,
            default: "Skill",
        },
        progress: {
            type: Number,
            default: 78,
        },
        gradientClass: {
            type: String,
            default: "bg-gradient-to-r from-blue-500 via-purple-500 to-purple-200",
        },
    },
    data() {
        return {
            animatedProgress: 0,
            displayProgress: 0,
            observer: null,
        };
    },
    mounted() {
        this.setupObserver();
    },
    methods: {
        setupObserver() {
            this.observer = new IntersectionObserver(
                (entries) => {
                    const [entry] = entries;
                    if (entry.isIntersecting) {
                        this.startAnimation();
                        this.observer.disconnect(); // Only run once
                    }
                },
                { threshold: 0.5 },
            );

            this.observer.observe(this.$refs.container);
        },
        startAnimation() {
            const duration = 1000; // in ms
            const frameRate = 1000 / 60;
            const totalFrames = Math.round(duration / frameRate);
            let currentFrame = 0;

            const counter = setInterval(() => {
                currentFrame++;
                const progressEase = this.easeOutCubic(currentFrame / totalFrames);
                this.animatedProgress = Math.round(this.progress * progressEase);
                this.displayProgress = Math.round(this.progress * progressEase);

                if (currentFrame >= totalFrames) {
                    clearInterval(counter);
                }
            }, frameRate);
        },
        easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        },
    },
};
</script>
<style scoped>
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease forwards;
}

.shadow-circle {
    box-shadow: 0 0 6px rgba(59, 130, 246, 0.6);
}

.delay-100 {
    animation-delay: 0.1s;
}
</style>
