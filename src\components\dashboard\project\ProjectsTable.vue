<template>
    <div class="w-full">
        <div class="w-full flex flex-col md:flex-row justify-between items-center gap-2 md:gap-0">
            <div class="w-full md:w-[30%] flex flex-col md:flex-row items-center gap-2 md:gap-4 mb-2 md:mb-0">
                <input :placeholder="$t('Search')"
                    class="text-base py-2 px-3 border-[1.5px] rounded-md h-[45px] w-full border-gray-200 outline-none focus:border-NeonBlue"
                    type="text" id="searchInput" name="searchInput" v-model="searchPayload" required />
                <ButtonComponent intent="primary" class="w-full md:w-auto" @click="searchProject"> {{ $t("Search") }}
                </ButtonComponent>
            </div>

            <div class="w-full md:w-[50%]">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-2.5 md:gap-0">
                    <Popper :content="$t('Show all projects')" placement="bottom" :hover="true">
                        <button class="statusCard border-2 border-r-0 border-gray-200 rounded-md md:rounded-l-md w-full"
                            @click="showAll" :class="this.selectedStatus.status === 'All' ? 'active' : ''"
                            id="statusCard">
                            {{ $t("All") }} ({{ this.Store.projectsData.totalProjects }})
                        </button>
                    </Popper>

                    <Popper :content="$t('Show active projects')" placement="bottom" :hover="true">
                        <button class="statusCard border-2 border-gray-200 rounded-md w-full" @click="showActive"
                            :class="this.selectedStatus.status === 'Active' ? 'active' : ''" id="statusCard">
                            {{ $t("Active") }} ({{ this.Store.projectsData.activeProjects }})
                        </button>
                    </Popper>

                    <Popper :content="$t('Show archived projects')" placement="bottom" :hover="true">
                        <button class="statusCard border-2  border-gray-200 rounded-md md:rounded-r-md w-full"
                            @click="showArchived" :class="this.selectedStatus.status === 'Archived' ? 'active' : ''"
                            id="statusCard">
                            {{ $t("Archived") }} ({{ this.Store.projectsData.archivedProjects }})
                        </button>
                    </Popper>
                </div>
            </div>
        </div>

        <div class="flex flex-col justify-between w-full rounded" style="background-color: #fff" id="table_body">
            <div class="sm:-mx-6">
                <div class="inline-block min-w-full sm:px-6">
                    <div class="">
                        <div class="grid lg:hidden grid-cols-[10fr_1fr] grid-rows-2 border-b border-gray-300 gap-3 relative"
                            v-for="(project, index) in this.myPojects" :key="index">
                            <div @click="navigateToDashBoard(project)"
                                class="w-full text-base font-light text-slate-700 flex flex-row justify-between row-start-1 row-end-2 col-start-1 col-end-2">
                                <span class="font-bold">{{ project.name }}</span>
                                <span>{{ project.min_score != 0 ? project.min_score : project.recommanded_score
                                }}%</span>
                            </div>
                            <div @click="navigateToDashBoard(project)"
                                class="flex flex-row justify-start items-center gap-5 text-base font-light text-slate-700 row-start-2 col-start-1 col-end-2">
                                <span>{{ getDate(project.createdAt) }}</span>
                                <span class="">{{ project.invitationsNbr }} {{ project.invitationsNbr > 1 ? "Candidates"
                                    : "Candidate" }}</span>
                            </div>
                            <button @click.stop @click="() => { project.showMenu = !project.showMenu; }"
                                style="cursor: pointer" class="col-start-2 row-start-1 row-span-2">
                                <font-awesome-icon :icon="['fas', 'ellipsis-vertical']" />
                            </button>
                            <div v-if="project.showMenu" class="absolute z-[50] right-10 w-[80px]">
                                <ProjectMenu :showDetails="project.showMenu"
                                    :editable="project.invitationsNbr > 0 ? false : true"
                                    :toggleMenu="() => { project.showMenu = !project.showMenu; }"
                                    :thisProject="project" />
                            </div>
                        </div>
                        <div class="min-h-[500px] border-b relative hidden lg:block">
                            <table class="min-w-full text-sm">
                                <thead class="sticky top-0 text-gray-700 bg-[#FBFBFB]"
                                    style="text-transform: uppercase">
                                    <tr class="border-b transition-colors hover:bg-muted/50">
                                        <th scope="col" class="text-[#1b2559] h-14 px-4 text-left font-medium">{{
                                            $t("Project") }}</th>
                                        <th scope="col" class="text-[#1b2559] h-14 px-4 text-center font-medium">{{
                                            $t("Date Created") }}</th>
                                        <!-- <th scope="col" class="px-6 py-3">Job Title</th> -->
                                        <th scope="col" class="text-[#1b2559] h-14 px-4 text-center font-medium">{{
                                            $t("Candidates") }}</th>
                                        <th scope="col" class="text-[#1b2559] h-14 px-4 align-middle font-medium">{{
                                            $t("Progress") }}</th>
                                        <th scope="col" class="text-[#1b2559] h-14 px-4 align-middle font-medium">{{
                                            $t("Success Score") }}</th>
                                        <th scope="col" class="text-[#1b2559] h-14 px-4 align-middle font-medium">{{
                                            $t("Actions") }}</th>
                                    </tr>
                                </thead>
                                <tbody class="max-h-[550px] overflow-y-scroll">
                                    <tr class="border-b border-[#e5e7eb] hover:bg-[#d5efff] text-gray-700 duration-300 cursor-pointer"
                                        v-for="(project, index) in this.myPojects" :key="index">
                                        <td class="p-3 align-middle" @click="navigateToDashBoard(project)">{{
                                            project.name }}</td>
                                        <td class="p-3 align-middle text-center" @click="navigateToDashBoard(project)">
                                            {{ getDate(project.createdAt) }}
                                        </td>
                                        <!-- <td class="whitespace-nowrap px-6 py-4 text-[#0F172A]">{{ project.jobTitle }}</td> -->
                                        <td class="p-3 align-middle text-center" @click="navigateToDashBoard(project)">
                                            <!-- {{ project.seniority }} -->
                                            <!-- invitationsNbr: 8, passedCandidatesNbr: 1 -->
                                            {{ project.invitationsNbr }}
                                        </td>
                                        <td class="p-3" @click="navigateToDashBoard(project)">
                                            <!-- <Popper :content="$t('project name tooltip')" placement="left" :hover="true" class="w-full"> -->
                                            <div class="h-[25px] w-full flex rounded relative"
                                                v-if="project.invitationsNbr"
                                                @mouseenter="() => (project.showInv = !project.showInv)"
                                                @mouseleave="() => (project.showInv = !project.showInv)">
                                                <div class="absolute top-0 w-[max-content] z-[10] p-2 right-[105%] popup"
                                                    v-if="project.showInv" style="transform: translateY(-20%)">
                                                    <div class="flex justify-between items-center">
                                                        {{ $t("Completed") }}: {{ project.passedCandidatesNbr }} |
                                                        <span class="text-right ml-4 opacity-60"> {{
                                                            parseInt((project.passedCandidatesNbr * 100) /
                                                                project.invitationsNbr) }}% </span>
                                                    </div>
                                                    <!-- <br /> -->
                                                    <div class="flex justify-between items-center">
                                                        {{ $t("Not Started") }}: {{ project.invitationsNbr -
                                                            project.passedCandidatesNbr }} |
                                                        <span class="text-right ml-4 opacity-60"> {{ 100 -
                                                            parseInt((project.passedCandidatesNbr * 100) /
                                                                project.invitationsNbr) }}% </span>
                                                    </div>
                                                </div>
                                                <div class="w-[50%] h-full relative flex items-center justify-center text-white"
                                                    style="background-image: linear-gradient(120deg, #2196f3 0%, #c084fc 82%)"
                                                    :style="{ width: `${parseInt(project.passedCandidatesNbr * 100) / project.invitationsNbr}%` }">
                                                    {{ project.passedCandidatesNbr > 0 ? project.passedCandidatesNbr :
                                                        "" }}
                                                </div>
                                                <div class="h-full relative flex items-center justify-center"
                                                    style="background-color: #ededed"
                                                    :style="{ width: `${100 - parseInt(project.passedCandidatesNbr * 100) / project.invitationsNbr}%` }">
                                                    {{ project.invitationsNbr - project.passedCandidatesNbr > 0 ?
                                                        project.invitationsNbr - project.passedCandidatesNbr : "" }}
                                                </div>
                                            </div>
                                            <!-- </Popper> -->
                                        </td>
                                        <td class="text-center p-3 align-middle" @click="navigateToDashBoard(project)">
                                            {{ project.min_score != 0 ? project.min_score : project.recommanded_score
                                            }}%
                                        </td>
                                        <td class="text-center p-3 align-middle" style="position: relative">
                                            <Popper :content="$t('Edit, Duplicate and Delete')" placement="top"
                                                :hover="true">
                                                <div>
                                                    <button @click.stop @click="
                                                        () => {
                                                            project.showMenu = !project.showMenu;
                                                        }
                                                    " style="cursor: pointer" class="menuBtn">
                                                        <font-awesome-icon :icon="['fas', 'ellipsis-vertical']" />
                                                    </button>
                                                    <div class="absolute z-[50]" style="left: 50%">
                                                        <ProjectMenu :showDetails="project.showMenu"
                                                            :editable="project.invitationsNbr > 0 ? false : true"
                                                            :toggleMenu="() => {
                                                                project.showMenu = !project.showMenu;
                                                            }
                                                                " :thisProject="project" />
                                                    </div>
                                                </div>
                                            </Popper>
                                        </td>
                                    </tr>
                                    <tr v-if="this.myPojects.length === 0"
                                        class="border-b border-[#e5e7eb] hover:bg-[#2371b631] text-gray-700 duration-300">
                                        <td class="p-3 align-middle" @click="navigateToDummyBoard">{{ $t("Dummy Project") }}</td>
                                        <td class="p-3 align-middle text-center" @click="navigateToDummyBoard">2024 - 10
                                            - 21</td>
                                        <!-- <td class="whitespace-nowrap px-6 py-4 text-[#0F172A]">{{ project.jobTitle }}</td> -->
                                        <td class="p-3 align-middle text-center" @click="navigateToDummyBoard">
                                            <!-- {{ project.seniority }} -->
                                            <!-- invitationsNbr: 8, passedCandidatesNbr: 1 -->
                                            4
                                        </td>
                                        <td class="p-3" @click="navigateToDummyBoard">
                                            <!-- <Popper :content="$t('project name tooltip')" placement="left" :hover="true" class="w-full"> -->
                                            <div class="h-[25px] w-full flex rounded relative"
                                                @mouseenter="() => (ShowDummyInv = !ShowDummyInv)"
                                                @mouseleave="() => (ShowDummyInv = !ShowDummyInv)">
                                                <div class="absolute top-0 w-[max-content] z-[10] p-2 right-[105%] popup"
                                                    v-if="ShowDummyInv" style="transform: translateY(-20%)">
                                                    <div class="flex justify-between items-center">
                                                        {{ $t("Completed") }}: 3 |
                                                        <span class="text-right ml-4 opacity-60"> 75% </span>
                                                    </div>
                                                    <!-- <br /> -->
                                                    <div class="flex justify-between items-center">
                                                        {{ $t("Not Started") }}: 1 |
                                                        <span class="text-right ml-4 opacity-60"> 25% </span>
                                                    </div>
                                                </div>
                                                <div class="w-[50%] h-full relative flex items-center justify-center text-white"
                                                    style="background: rgb(123, 42, 255); background: linear-gradient(90deg, rgba(123, 42, 255, 1) 0%, rgba(0, 174, 240, 1) 100%)"
                                                    :style="{ width: `75%` }">
                                                    3
                                                </div>
                                                <div class="h-full relative flex items-center justify-center"
                                                    style="background-color: #ededed" :style="{ width: `25%` }">1</div>
                                            </div>
                                            <!-- </Popper> -->
                                        </td>
                                        <td class="text-center p-3 align-middle" @click="navigateToDummyBoard">50%</td>
                                        <td class="text-center p-3 align-middle" style="position: relative">
                                            <button @click.stop style="cursor: pointer" class="menuBtn">
                                                <font-awesome-icon :icon="['fas', 'ellipsis-vertical']" />
                                            </button>
                                            <div class="absolute z-[5]" style="left: 50%">
                                                <!-- <ProjectMenu
                                                    :showDetails="project.showMenu"
                                                    :editable="project.invitationsNbr > 0 ? false : true"
                                                    :toggleMenu="
                                                        () => {
                                                            project.showMenu = !project.showMenu;
                                                        }
                                                    "
                                                    :thisProject="project"
                                                /> -->
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="this.myPojects.length > 0"
                class="w-full flex flex-row items-center flex-nowrap gap-9 lg:pr-3 justify-between lg:justify-end mt-8 mb-[40px]">
                <div class="lg:w-[10%]">
                    <Listbox v-model="selectedOption">
                        <div class="relative">
                            <ListboxButton
                                class="relative w-full cursor-default border-2 border-gray-200 rounded-md bg-white py-2 pl-3 pr-10 text-left sm:text-sm">
                                <span class="block truncate">{{ selectedOption }}</span>
                                <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                                    <ChevronUpDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                                </span>
                            </ListboxButton>

                            <transition leave-active-class="transition duration-100 ease-in"
                                leave-from-class="opacity-100" leave-to-class="opacity-0">
                                <ListboxOptions
                                    class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                                    <ListboxOption v-slot="{ active, selected }" v-for="option in options"
                                        :key="option?.number" :value="option" as="template">
                                        <li @click="selectOption(option)"
                                            :class="[active ? 'bg-[#d5efff] text-[#2196f3]' : 'text-gray-900', 'relative cursor-default select-none py-2 pl-10 pr-4']">
                                            <span
                                                :class="[selected ? 'font-medium' : 'font-normal', 'block truncate']">{{
                                                    option }}</span>
                                            <span v-if="selected"
                                                class="absolute inset-y-0 left-0 flex items-center pl-3 text-[#2196f3]">
                                                <CheckIcon class="h-5 w-5" aria-hidden="true" />
                                            </span>
                                        </li>
                                    </ListboxOption>
                                </ListboxOptions>
                            </transition>
                        </div>
                    </Listbox>
                </div>
                <div class="flex items-center">
                    <span class="text-s text-gray-500">{{ `${this.page} / ${this.totalPages}` }}</span>
                </div>
                <div class="w-[4%] flex justify-between text-gray-500">
                    <button @click="previousPage"><font-awesome-icon :icon="['fas', 'chevron-left']"
                            id="slide1" /></button>
                    <button @click="nextPage"><font-awesome-icon :icon="['fas', 'chevron-right']"
                            id="slide2" /></button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ProjectMenu from "./ProjectMenu.vue";
import { Listbox, ListboxButton, ListboxOptions, ListboxOption } from "@headlessui/vue";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/vue/20/solid";
import { useStore } from "@/store/index";
import ButtonComponent from "@/components/ReusableComponents/ButtonComponent.vue";
export default {
    name: "ProjectsTable",
    components: {
        ProjectMenu,
        Listbox,
        ListboxButton,
        ListboxOptions,
        ListboxOption,
        CheckIcon,
        ChevronUpDownIcon,
        ButtonComponent,
    },
    data() {
        return {
            selectedStatus: { status: "All" },
            types: [{ status: "All" }, { status: "Active" }, { status: "Archived" }],
            myPojects: this.projects,
            showMenu: false,
            myProject: {},
            searchPayload: "",
            showCom: false,
            showInv: false,
            showNbrPages: false,
            selectedOption: 10,
            nbrOfPages: null,
            totalPages: this.Store.totalPages,
            page: 1,
            options: [10, 15, 25], // Example options
            ShowDummyInv: false,
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    props: {
        projects: {
            type: Array,
            // required: true,
        },
        getProjects: {
            type: Function,
        },
        // totalPages: {
        //     type: Number,
        // },
    },
    created() {
        this.myPojects.map((project) => {
            project.showMenu = false;
            project.showCom = false;
            project.showInv = false;
        });
    },
    watch: {
        // "selectedStatus.status"(newVal) {
        //     this.page = 1;
        //     if (newVal === "All") {
        //         this.myPojects = this.projects.slice(0, this.selectedOption.number);
        //     } else {
        //         console.log({ SELECTEEEDDDSTATUSNEWVAL: newVal });
        //         this.myPojects = this.projects.filter((project) => project.project_status == newVal).slice(0, this.selectedOption.number);
        //     }
        // },

        projects(newVal) {
            this.myPojects = newVal;
            this.nbrOfPages = this.totalPages;
        },
    },
    methods: {
        navigateToDashBoard(project) {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/boards`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },
        navigateToDummyBoard() {
            this.$router.push("/dummy-dashboard");
        },
        nextPage() {
            if (this.page < this.totalPages) {
                this.page++;
                this.$router.push({ query: { ...this.$route.query, page: this.page } });

                this.getProjects(this.page, this.selectedOption);
            }
        },
        previousPage() {
            if (this.page > 1) {
                this.page--;
                this.$router.push({ query: { ...this.$route.query, page: this.page } });

                this.getProjects(this.page, this.selectedOption);
            }
        },
        toggleDropdown() {
            this.showNbrPages = !this.showNbrPages;
        },
        selectOption(option) {
            this.selectedOption = option;
            this.page = 1;
            this.$router.push({ query: { ...this.$route.query, page: this.page, perPage: option } });

            this.getProjects(this.page, option);
        },
        toggleShowCom(project) {
            this.showCom = !this.showCom;
            if (project) {
                this.myProject.showCom = !this.myProject.showCom;
                this.myProject = project;
            }
        },
        toggleShowInv() {
            this.showInv = !this.showInv;
        },

        toggleMenu(project) {
            if (project) {
                this.showMenu = !this.showMenu;
                this.myProject.showMenu = true;
                this.myProject = project;
            }
        },
        showAll() {
            this.selectedStatus.status = "All";
            this.myPojects = this.projects;
            this.$router.push({ query: { ...this.$route.query, page: 1, status: "All" } });
            this.getProjects(this.page, this.selectedOption, "All");
        },
        showActive() {
            this.selectedStatus.status = "Active";
            this.$router.push({ query: { ...this.$route.query, page: 1, status: "Active" } });
            this.getProjects(this.page, this.selectedOption, "Active");
        },
        showArchived() {
            this.selectedStatus.status = "Archived";
            this.$router.push({ query: { ...this.$route.query, page: 1, status: "Archived" } });
            this.getProjects(this.page, this.selectedOption, "Archived");
        },
        searchProject() {
            this.selectedStatus.status = "All";
            this.$router.push({ query: { ...this.$route.query, page: 1, status: "All" } });
            this.getProjects(this.page, this.selectedOption, this.selectedStatus.status, this.searchPayload);
        },
        getDate(str) {
            // const parts = str.split("-");
            const date = new Date(str);
            const extractedDate = date.toISOString().split("T")[0];
            // console.log("the date ", extractedDate);
            return extractedDate;
        },
    },
    mounted() {
        this.selectedStatus = this.types[0];
        if (!this.$route.query.page) {
            this.$router.push({ query: { ...this.$route.query, page: this.page, perPage: 10, status: "All" } });
        }
        this.page = this.$route.query.page || 1;
        this.selectedOption = this.$route.query.perPage || 10;
        this.selectedStatus.status = this.$route.query.status || "All";
    },
};
</script>

<style scoped lang="scss">
.popup {
    background: #2196f3;
    padding: 10px;
    border-radius: 10px;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    max-width: 300px;
}

.statusCard {
    height: 50px;
    width: 100%;
    background-color: #fff;
    color: #0f172a;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 400;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
        background-color: #2196f3;
        color: #fff;
    }
}

.active {
    background-color: #2196f3;
    color: #fff;
}

.menuBtn {
    height: 35px;
    width: 35px;
    border-radius: 50%;
    transition: all 0.3s ease-in-out;

    &:hover {
        background-color: #fff;
    }
}

@media (max-width: 768px) {
    table {
        display: none;
        //  overflow-x: scroll;
        white-space: nowrap;
    }

    th,
    td {
        min-width: 120px;

        // display: inline-block;
    }
}

#table_body {
    width: 100%;
    max-height: calc(89% - 1.6rem);

    margin: 2.5rem 0;
    border-radius: 0.6rem;
}

#table_body::-webkit-scrollbar {
    width: 0.5rem;
    height: 0.5rem;
}

#table_body::-webkit-scrollbar-thumb {
    border-radius: 0.5rem;
    background-color: #0004;
    visibility: hidden;
}

#table_body:hover::-webkit-scrollbar-thumb {
    visibility: visible;
}

table {
    width: 100%;
}

thead th {
    top: 0;
    left: 0;
    cursor: pointer;
    text-transform: capitalize;
}

tbody tr.hide {
    opacity: 0;
    transform: translateX(100%);
}

tbody tr td,
tbody tr td p,
tbody tr td img {
    transition: 0.2s ease-in-out;
}

tbody tr.hide td,
tbody tr.hide td p {
    padding: 0;
    font: 0 / 0 sans-serif;
    transition: 0.2s ease-in-out 0.5s;
}

@media (max-width: 1000px) {
    td:not(:first-of-type) {
        min-width: 12.1rem;
    }
}

#slide:hover {
    color: #2196f3;
}

#slide1:hover {
    color: #2196f3;
}

#slide2:hover {
    color: #2196f3;
}
</style>
