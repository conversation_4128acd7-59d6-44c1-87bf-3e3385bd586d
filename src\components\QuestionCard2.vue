<template>
    <UpdateCustomQuestion :isShown="isShown" :question_id="assessement._id" :closePanel="closePanel" :type="question_type" />
    <ConfirmDeleteQuestion :closeConfirm="closeConfirm" :isShown="isShownDelete" :id="assessement._id" />
    <div class="top-assessement w-full">
        <div class="relative flex flex-col items-start justify-between h-full w-full px-3 py-3 bg-white">
            <div class="w-full">
                <div class="flex relative justify-between">
                    <div class="flex gap-0 pb-1 flex-col justify-center items-start w-full">
                        <h2 class="title-question w-full text-lg font-semibold text-left relative pb-4 mb-1">
                            {{ $t(assessement.name) }}
                        </h2>

                        <div class="flex justify-center gap-5 items-center mb-1  ">
                            <span class="flex items-center text-sm text-NeonBlue  font-normal">
                                <font-awesome-icon
                                    :icon="assessement.category === 'Essay' ? ['fas', 'file-alt'] : ['fal', 'folder']"
                                    class="mr-1" />
                                {{ $t(assessement.category) }}
                            </span>
                            <span class="text-sm text-NeonBlue  font-normal">|</span>
                            <span class="flex items-center text-sm text-NeonBlue  font-normal">
                                <font-awesome-icon :icon="['fas', 'stopwatch']" class="mr-1" />
                                {{ assessement.time }} '
                            </span>
                            <span class="text-sm text-NeonBlue font-normal">|</span>
                            <span class="flex items-center text-sm text-NeonBlue  font-normal">
                                <font-awesome-icon :icon="['fas', 'database']" class="mr-1" />
                                {{ !assessement.company ? "Go Platform" : "My library" }}
                            </span>
                        </div>
                    </div>
                    <div class="absolute top-0 right-0 ">
                        <font-awesome-icon v-if="assessement.company" @click.stop="isOpen = !isOpen"
                            class="cursor-pointer rounded-full w-5 h-5 hover:bg-[#a7c6e3] p-1 rotate-90"
                            :icon="['fas', 'ellipsis']" />
                    </div>
                    <div v-if="isOpen" ref="container"
                        class="absolute -right-12 p-2 bottom-[20%] rounded-md w-25 flex bg-white border border-gray-200 flex-col gap-2 z-10">
                        <font-awesome-icon @click="defineType(assessement.category)"
                            class="cursor-pointer hover:bg-[#a7c6e3] rounded-md p-1" :icon="['far', 'pen-to-square']" />
                        <font-awesome-icon @click="isShownDelete = true"
                            class="cursor-pointer rounded-md hover:bg-[#a7c6e3] p-1" :icon="['far', 'trash-can']" />
                    </div>
                </div>
                <p class="text-base font-light py-4 text-left leading-relaxed  border-t border-NeonBlue">
                    {{ getFirstPhrase($t(assessement.question)) }}
                </p>
            </div>
            <div v-if="showedMore" class="w-full flex flex-row gap-4 mt-6">
                <div class="bg-gray-50 rounded-lg p-5 shadow-sm flex flex-col md:flex-row">
                    <div class="flex-1 mr-2">

                        <h3 class="font-semibold text-slate-700 mb-2 text-base">
                            <font-awesome-icon :icon="['far', 'lightbulb']" class="mr-1 " style="color:#94A3B8 ;" />
                            {{ $t("Why is this question relevant?") }}
                        </h3>
                        <p class="text-base font-normal text-slate-700 break-words"> {{ $t(assessement.look) }}</p>

                        <p class="text-base font-normal text-slate-700 break-words"></p>
                    </div>
                    <div class="flex-1 ml-2">
                        <h3 class="font-semibold text-slate-700 mb-2 text-base">
                            <font-awesome-icon :icon="['fas', 'search']" class="mr-1 " style="color: #94A3B8   ;" />
                            {{ $t("What to look for in the answer?") }}
                        </h3>
                        <p class="text-base font-normal text-slate-700 break-words">{{ $t(assessement.goal) }}</p>
                        <p class="text-base font-normal text-slate-700 break-words"></p>
                    </div>
                </div>
            </div>
            <div class="w-full flex justify-between items-center relative mt-6">
                <button @click="showedMore = !showedMore" class="flex items-center justify-center text-NeonBlue" style="background: none; border: none; padding: 0; outline: none; box-shadow: none">
                    <span class="mr-1 text-base font-medium text-NeonBlue">
                        {{ showedMore ? $t("Show less") : $t("Show more") }}
                    </span>
                    <font-awesome-icon :icon="showedMore ? ['fas', 'chevron-up'] : ['fas', 'chevron-down']" class="text-sm ml-1 text-NeonBlue" />
                </button>
                <div>
                    <button
                        v-if="!verifyAddedQuestion"
                        class="bg-[#2196f3] text-white hover:opacity-85 text-m rounded w-[8rem] min-h-[30px] px-4 py-[10px] font-semibold flex items-center justify-center relative cursor-pointer"
                        @click="add(assessement)"
                    >
                        {{ $t("Add now") }}
                    </button>
                    <button v-else class="bg-red-600 text-gray-700 hover:opacity-85 w-[8rem] text-m rounded min-h-[30px] px-5 py-[10px] font-semibold" @click="deleteSelectedQst(addedQuestionId)">
                        <font-awesome-icon class="text-white" :icon="['fas', 'xmark']" />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import UpdateCustomQuestion from "@/components/UpdateCustomQuestion";
import ConfirmDeleteQuestion from "@/components/ConfirmDeleteQuestion";
export default {
    name: "QuestionCard",
    components: {
        UpdateCustomQuestion,
        ConfirmDeleteQuestion,
    },
    props: ["assessement", "addAssessment", "selectedQuestions", "deleteSelectedQst"],
    data() {
        return {
            imagePath: "",
            selected: false,
            showedMore: false,
            isOpen: false,
            isShown: false,
            isShownDelete: false,
        };
    },
    computed: {
        verifyAddedQuestion() {
            return !!this.selectedQuestions.find((question) => question.question === this.assessement?.question);
        },
        addedQuestionId() {
            const selectedQuestion = this.selectedQuestions.find((question) => question.question === this.assessement.question);
            return selectedQuestion ? selectedQuestion.id : null;
        },
    },

    methods: {
        defineType(type) {
            this.question_type = type;
            this.isShown = true;
        },
        closeConfirm() {
            this.isShownDelete = false;
        },
        closePanel() {
            this.isShown = false;
        },
        add(assessment) {
            this.addAssessment(assessment);
        },

        getFirstPhrase(description) {
            if (!this.showedMore) {
                // Split the string into an array of words
                let words = description?.split(/\s+/);

                // Extract the first 200 words
                let first30Words = words?.slice(0, 30)?.join(" ");
                if (words.length < 30) return first30Words;
                first30Words += "...";
                return first30Words;
            }
            return description;
        },
        handleClickOutside(event) {
            if (this.$refs.container && !this.$refs.container.contains(event.target)) {
                this.isOpen = false;
            }
        },
    },
    mounted() {
        window.addEventListener("click", this.handleClickOutside);
    },
    unmounted() {
        window.removeEventListener("click", this.handleClickOutside);
    },
};
</script>

<style lang="scss" scoped>
.top-assessement {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
    padding: 0.2rem;
    gap: 2rem;
    padding-bottom: 1rem;
    background: #ffffff;
    width: 100%;
    /* Remove fixed height for auto expand */
}
</style>
