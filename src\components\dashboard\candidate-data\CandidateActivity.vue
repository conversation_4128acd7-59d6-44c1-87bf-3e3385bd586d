<template>
    <CompareModal :compareModal="compareModal" :toggleCompare="toggleCompare" :candidates="Store.evaluations" />
    <!-- <div ref="pdfContent">
        <TalentDataDownload />
    </div> -->
    <AntiCheatingPolicy :show="showAntiCheat" :toggleModal="toggleAntiCheat" />
    <TalentDataDownload :candidateInfo="candidateInfo" :candidate="candidate" ref="talentDataDownload" :candidateInterpretations="candidateInterpretations" />
    <InterpretResult :show="showInterpretation" :toggleModal="toggleInterpretation" />
    <div v-if="isLoading" class="loader">
        <LoadingComponent />
    </div>
    <div class="candidateActivityContainer">
        <div v-if="!isLoading" class="w-full candidateActivity">
            <div class="w-full px-16 mb-5 flex items-center bg-[#fff] py-4">
                <button
                    class="backBtn mr-4"
                    @click="
                        () => {
                            this.$router.go(-1);
                        }
                    "
                >
                    <font-awesome-icon :icon="['fas', 'angle-left']" />
                </button>
                <div>
                    <span class="profile-name">{{ candidateInfo?.Last_name + " " + candidateInfo?.First_name }}</span
                    ><br />
                    <span class="email" @click="copyEmail">{{ candidateInfo?.Email }} <img loading="lazy" decoding="async" src="@/assets/Images/icons/copy-icon.svg" alt="copy-icon" /></span>
                </div>
            </div>
            <div class="activityContent">
                <div class="full px-3 mb-2 flex justify-end items-center gap-2">
                    <h1 class="projectTitle mr-auto">
                        {{ $t("Project") }}:
                        <span class="label-span">{{ getProjectName(this.projectId) }}</span>
                    </h1>
                    <button class="backBtn text-xs" @click="toggleInterpretation">
                        <font-awesome-icon class="w-4 h-4 p-1 border border-slate-700 rounded-full text-slate-700" :icon="['fas', 'question']" />
                    </button>
                    <button class="backBtn" @click="printDownload">
                        <font-awesome-icon :icon="['fas', 'cloud-arrow-down']" />
                    </button>
                </div>
                <!-- <div class="grid-row-widgets">
                <div class="widget background-2 shadow">
                    <div>
                        <span class="widget-title">{{ $t("Score") }} </span>
                        <span class="widget-info">{{ calculateScore() }} %</span>
                    </div>
                    <img loading="lazy"  decoding="async" src="@/assets/Images/icons/Chart_100.svg" alt="bar-chart-icon" />
                </div>
                <div class="widget shadow">
                    <div>
                        <span class="widget-title">{{ $t("Rank") }}</span>
                        <span class="widget-info">4</span>
                    </div>
                    <img loading="lazy"  decoding="async" src="@/assets/Images/icons/bar-graph.svg" alt="bar-graph" />
                </div>

                <div class="widget shadow">
                    <img loading="lazy"  decoding="async" src="@/assets/Images/icons/bar-chart-icon.svg" alt="bar-chart-icon" />
                    <div style="width: 70%">
                        <span class="widget-title">{{ $t("Assessment time") }}</span>
                        <span class="widget-info">{{ calculateTime() }}</span>
                    </div>
                </div>

                <button class="compareBtn" @click="toggleCompare">{{ $t("Compare talent") }}</button>
            </div> -->
                <div class="candidateData">
                    <div class="w-full flex flex-col justify-start items-center gap-2">
                        <div class="flex flex-col p-4 w-full rounded bg-white items-center justify-between">
                            <h1 class="w-full text-left text-lg font-semibold text-slate-700">{{ $t("Invited") }}</h1>
                            <span class="w-full text-left text-sm font-light text-slate-700">{{ getDateAndTime(this.candidateInv?.createdAt) }}</span>
                        </div>
                        <div class="flex flex-col p-4 w-full rounded bg-white items-center justify-between">
                            <h1 class="w-full text-left text-lg font-semibold text-slate-700">{{ $t("Completed") }}</h1>
                            <span class="w-full text-left text-sm font-light text-slate-700">{{ getDateAndTime(this.candidate?.createdAt) }}</span>
                        </div>
                        <div class="flex flex-col p-4 w-full rounded bg-white items-center justify-between gap-3">
                            <div class="w-full flex flex-col justify-between items-start gap-1">
                                <h1
                                    class="w-full text-left text-lg font-semibold text-slate-700 relative pb-1 mb-2 after:content-('') after:absolute after:w-full after:h-px after:bg-slate-300 after:left-0 after:bottom-0"
                                >
                                    {{ $t("Rating") }}
                                </h1>
                                <span v-if="candidateRating?.rating" class="w-full flex flex-row" style="color: #2195f3; display: flex; flex-wrap: wrap">
                                    <font-awesome-icon v-for="(rating, index) in candidateRating.rating" :key="index" :icon="['fas', 'star']" />
                                </span>
                                <span v-else class="font-bold text-lg w-full text-left pl-2"> - </span>
                            </div>
                            <div class="w-full flex flex-col justify-between items-start gap-1">
                                <h1
                                    class="w-full text-left text-lg font-semibold text-slate-700 relative pb-1 mb-2 after:content-('') after:absolute after:w-full after:h-px after:bg-slate-300 after:left-0 after:bottom-0"
                                >
                                    {{ $t("Feedback") }}
                                </h1>
                                <span v-if="candidateRating?.feedback" class="font-light text-sm w-full text-left pl-0"> {{ candidateRating.feedback }} </span>
                                <span v-else class="font-bold text-lg w-full text-left pl-2"> - </span>
                            </div>
                        </div>
                    </div>
                    <div class="middleData">
                        <div class="avgScore shadow-[0_0_10px_0px_rgba(0,0,0,0.15)]">
                            <div class="w-full flex flex-row items-center justify-end mb-8 gap-6">
                                <h1 class="text-sm font-medium text-slate-700 w-fit">{{ $t("Scoring method:") }}</h1>
                                <button @click="ranking = !ranking" class="border-[#2196f3] rounded-md border-2 w-32 h-fit px-4 py-2 text-sm font-medium text-slate-700">
                                    {{ ranking ? $t("Ranking") : $t("Average") }}
                                </button>
                            </div>
                            <div class="w-full flex justify-between gap-4 items-center" v-if="avgScore !== null">
                                <div class="relative w-3/4 h-full" style="">
                                    <!-- <input class="w-full h-3 rounded-md bg-white outline-none" 
                                        v-if="avgScore"
                                        type="range"
                                        :value="avgScore"
                                        disabled
                                        id="success-slider"
                                        :style="{
                                            background: `linear-gradient(90deg, #2196f3 ${avgScore}%, #f4f7fe ${avgScore}%)`,
                                        }"
                                    /> -->
                                    <div v-if="calculateScore()" class="w-full h-4 rounded-full bg-white mt-auto shadow-[0_0_1px_1px_rgba(0,0,0,15%)]">
                                        <div
                                            v-if="calculateScore()"
                                            :id="'tooltip-' + index"
                                            class="tooltip"
                                            :style="{
                                                left: calculateScore() + '%',
                                            }"
                                        >
                                            {{ calculateScore() }}%
                                        </div>
                                        <div class="h-full rounded-s-full rounded-e-none bg-[#2196f3]" :class="`w-[${avgScore}%]`" :style="{ width: `${calculateScore()}%` }"></div>
                                    </div>
                                </div>
                                <div class="p-3 w-1/4 flex flex-col justify-between items-center">
                                    <h1 class="text-center text-4xl font-bold text-slate-700 w-full">{{ ranking ? candidateRank.globalRank : calculateScore() + "%" }}</h1>
                                    <span class="text-center text-sm font-light text-slate-500 w-full">{{
                                        ranking ? $t("Ranking") : candidateInfo?.Last_name?.charAt(0) + "." + candidateInfo?.First_name
                                    }}</span>
                                </div>
                            </div>
                            <div class="w-full flex flex-row items-center flex-start gap-5">
                                <div class="flex flex-row justify-start items-center gap-2">
                                    <h3 class="text-left text-lg font-medium text-slate-700">{{ candidateInfo?.Last_name + " " + candidateInfo?.First_name }}:</h3>
                                    <span class="text-left text-lg font-light text-slate-500">{{ calculateScore() }}%</span>
                                </div>
                                <div class="flex flex-row justify-start items-center gap-2">
                                    <h3 class="text-left text-lg font-medium text-slate-700">{{ $t("Average") }}:</h3>
                                    <span class="text-left text-lg font-light text-slate-500">{{ avgScore }}%</span>
                                </div>
                            </div>
                        </div>
                        <!-- <div class="score-applicants shadow"> -->
                        <!-- <div> -->
                        <!-- <CandidateCard2 :passed="true" :backgroundHeader="[1]" :candidate="candidate" :candidateInfo="candidateInfo" /> -->
                        <!-- </div> -->
                        <!-- <div class="individualScoreWrapper w-full shadow"> -->
                        <IndividualScore :candidateScore="candidate?.results" :candidateInterpretations="candidateInterpretations" :candidateRank="candidateRank" :rank="ranking" />
                        <CustomResults :questions_results="candidate?.results" />

                        <!-- </div> -->
                        <!-- </div> -->
                        <div class="candidatesChartWrapper shadow border border-slate-200">
                            <!-- <candidatesChart :histogramData="this.histogramData" class="shadow" /> -->
                            <div class="mt-5" v-if="candidate?.results?.length > 2">
                                <SpiderwebChart
                                    :data="[
                                        {
                                            name: candidateInfo?.First_name,
                                            results: getCandidateResults(candidate?.results),
                                        },
                                        {
                                            name: 'Project Average',
                                            results: getCandidateResults(averageResults),
                                        },
                                    ]"
                                    :labels="chartLabels"
                                />
                            </div>
                            <div v-else class="shadow empty">
                                <EmptySpyderWebChart />
                            </div>
                        </div>
                        <!-- Candidate Score Summary Card -->
                        <div v-if="evaluation" class="shadow border border-slate-200 bg-white rounded-md mt-4 p-4 flex flex-col items-center">
                            <div class="w-full flex flex-row items-stretch justify-between text-center divide-x divide-slate-200">
                                <div class="flex-1 px-4 flex flex-col items-center justify-center">
                                    <span class="font-bold text-lg text-slate-700">
                                        {{ evaluation.language || "Coding Challenge" }}
                                    </span>
                                    <span class="text-xs text-slate-500 mt-1 flex items-center justify-center gap-2">
                                        <font-awesome-icon :icon="['far', 'clock']" class="w-3 h-3" />
                                        <!-- Replace with actual time if available -->
                                        00:17 of 09:00 mins
                                    </span>
                                </div>
                                <div class="flex-1 px-4 flex flex-col items-center justify-center">
                                    <span class="font-bold text-lg text-slate-700">27th Percentile</span>
                                    <span class="text-xs text-slate-500 mt-1">Below Average</span>
                                </div>
                                <div class="flex-1 px-4 flex flex-col items-center justify-center">
                                    <span class="font-bold text-lg text-slate-700">{{ calculateRawScore() + "%" }}</span>
                                    <span class="text-xs text-slate-500 mt-1">Raw Score</span>
                                </div>
                            </div>
                            <div class="w-full mt-6 text-left">
                                <!--
                                <div class="w-full bg-blue-100 px-3 py-1 rounded-md block">
                                    <span class="text-sm text-slate-700">Scored <span class="font-bold">higher than 27%</span> of all candidates</span>
                                </div>
                                -->
                                <div class="flex flex-row items-center justify-between mt-4 px-3">
                                    <span class="font-semibold text-slate-700 mr-4">Answer by skill score</span>
                                    <span class="flex items-center gap-4">
                                        <span class="flex items-center gap-2 text-[#05CD99] font-medium text-sm">
                                            <span class="bg-green-100 rounded-md p-1 flex items-center justify-center">
                                                <font-awesome-icon :icon="['fas', 'check']" class="w-3 h-3" />
                                            </span>
                                            Correct
                                        </span>
                                        <span class="flex items-center gap-2 text-red-500 font-medium text-sm">
                                            <span class="bg-red-100 rounded-md p-1 flex items-center justify-center">
                                                <font-awesome-icon :icon="['fas', 'times']" class="w-3 h-3" />
                                            </span>
                                            Incorrect
                                        </span>
                                    </span>
                                </div>
                            </div>
                            <!-- Skill breakdown -->
                            <div class="w-full mt-6 grid grid-cols-1 gap-6 px-3">
                                <div class="flex items-center gap-3" v-for="(skill, idx) in skills" :key="idx">
                                    <span class="w-56 text-slate-700 text-sm">{{ skill.label }}</span>
                                    <span class="flex-1 h-4 ml-4 rounded-full flex overflow-hidden bg-slate-100 relative">
                                        <span
                                            :style="{ width: skill.score + '%' }"
                                            :class="['h-full', 'flex', 'items-center', 'justify-center', 'relative', skill.score > 50 ? 'bg-[#05CD99]' : 'bg-red-500']"
                                        >
                                            <span
                                                v-if="skill.score > 0"
                                                class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 text-white text-[10px] font-bold select-none w-full text-center"
                                                >{{ skill.score }}%</span
                                            >
                                        </span>
                                        <span :style="{ width: 100 - skill.score + '%' }" :class="['h-full', skill.score > 50 ? 'bg-green-100' : 'bg-red-100']"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-col justify-start items-start gap-3 py-6 px-1">
                        <h2
                            class="flex flex-row w-full justify-between items-center font-bold text-left text-base text-slate-700 relative pb-3 mb-2 after:content-('') after:absolute after:w-full after:h-px after:bg-slate-300 after:left-0 after:bottom-0"
                        >
                            {{ $t("Anti-Cheating monitor") }}
                        </h2>
                        <div class="w-full text-sm flex flex-row justify-between items-center">
                            <h2 class="font-m">{{ $t("Status") }}</h2>
                            <span v-if="fetchedCheater?.status == 'cheater'" class="font-bold text-[#E5484D]">{{ $t("Cheater") }}</span>
                            <span v-if="fetchedCheater?.status == 'potential-cheater'" class="font-bold text-[#EC9455]">{{ $t("Potential Cheater") }}</span>
                            <span v-if="!fetchedCheater" class="font-bold text-[#5BB98B]">{{ $t("Clean") }}</span>
                        </div>

                        <p class="p-3 w-full rounded-md bg-[#E5484D] font-semibold text-sm tracking-wide text-white" v-if="fetchedCheater?.status === 'cheater'">
                            {{ $t("This candidate has been marked as a cheater after verification. Please take appropriate action.") }}
                        </p>

                        <div v-if="fetchedCheater?.status === 'potential-cheater'" class="p-3 w-full rounded-md bg-[#EC9455] font-semibold text-sm tracking-wide text-white">
                            <p>{{ $t("This candidate has potentially cheated based on our AI detection. Please verify the data before proceeding.") }}</p>
                            <div class="w-full mt-1 flex items-center justify-center">
                                <button class="py-2 px-4 flex items-center justify-center gap-2 bg-white text-gray-700 hover:bg-[#F0F0F0] rounded-md" @click="goToCheatingCard(fetchedCheater?._id)">
                                    {{ $t("Verify") }} <font-awesome-icon class="w-5 h-5" :icon="['far', 'eye']" />
                                </button>
                            </div>
                        </div>

                        <p v-if="!fetchedCheater" class="p-3 w-full rounded-md bg-[#5BB98B] font-semibold text-sm tracking-wide text-white">
                            {{ $t("This candidate has been cleared of any cheating activity. No further action is needed.") }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import IndividualScore from "@/components/dashboard/candidate-data/IndividualScore.vue";
import CustomResults from "@/components/dashboard/candidate-data/CustomResults.vue";
import { BASE_URL } from "@/constants";
import SpiderwebChart from "@/charts/SpiderwebChart.vue";
import EmptySpyderWebChart from "@/charts/EmptySpyderWebChart.vue";
import CompareModal from "@/components/unsued/CompareModal.vue";
import { useStore } from "@/store/index";
import TalentDataDownload from "@/components/TalentDataDownload.vue";
import axios from "axios";
import LoadingComponent from "@/components/LoadingComponent.vue";
import AntiCheatingPolicy from "../../AntiCheatingPolicy.vue";
import InterpretResult from "./InterpretResult.vue";

export default {
    name: "CandidateActivity",
    components: {
        IndividualScore,
        AntiCheatingPolicy,
        InterpretResult,
        CustomResults,
        SpiderwebChart,
        EmptySpyderWebChart,
        CompareModal,
        TalentDataDownload,
        LoadingComponent,
    },
    setup() {
        const Store = useStore();
        return {
            Store,
        };
    },

    props: ["showActivity", "toggleActivity"],
    data() {
        return {
            evaluation: null,
            skills: [],
            fetchedCheater: {},
            currentIndex: 1,
            ranking: false,
            /*skills: [
                { label: "Code Correctness", score: 99 },
                { label: "Efficiency & Performance", score: 45 },
                { label: "Code Quality & Readability", score: 83 },
                { label: "Problem-Solving", score: 7 },
                { label: "Security & Robustness", score: 99 },
                { label: "Scalability", score: 53 },
                { label: "Tooling & Originality", score: 29 },
            ],*/
            questions_results: [
                { name: "Personal information", question: "Tell us about urself", answer: "My name is i  love ...." },
                { name: "Food knowledge", question: "What is ur favourite food", answer: "I like pizza, I like pizza,I like pizza,I like pizza,I like pizza,I like pizza," },
                { name: "Sport", question: "What is ur favorite sport", answer: "My favorite sport is basket ball" },
                {
                    name: "Story telling",
                    question: "Tell me a story ",
                    answer: "lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu lorem36hgafj gfryu hh arety gfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh aretygfryu lorem36hgafj gfryu hh arety",
                },
            ],
            candidateRank: {},
            showAntiCheat: false,
            showInterpretation: false,
            isLoading: true,
            candidateInfo: this.Store.candidateInfoAct,
            candidate: this.Store.candidateActivity,
            // chartData: [50, 80, 60, 90, 70], // Example data values
            chartLabels: ["Label 1", "Label 2", "Label 3", "Label 4", "Label 5"], // Example labels
            chartData: [
                {
                    name: "RAOUF",
                    results: [
                        {
                            assessmentName: "Watson Glaser Critical Thinking",
                            totalPoints: 4,
                            quesionsNbr: 6,
                        },
                        {
                            assessmentName: "Time management",
                            totalPoints: 1,
                            quesionsNbr: 3,
                        },
                        {
                            assessmentName: "Problem solving",
                            totalPoints: 8,
                            quesionsNbr: 12,
                        },
                    ],
                },
            ],
            compareModal: false,
            averageResults: [],
            graphData: [
                // Provide your dataset here
                { Date: new Date("2023-01-01"), Close: 20 },
                { Date: new Date("2023-01-02"), Close: 60 },
                { Date: new Date("2023-01-03"), Close: 30 },
                { Date: new Date("2023-01-04"), Close: 50 },
            ],
            histogramData: [],
            candidateScore: [
                {
                    title: "java",
                    score: 94,
                },
                {
                    title: "javaScript",
                    score: 80,
                },
                {
                    title: "python",
                    score: 65,
                },
                {
                    title: "leaderShip",
                    score: 85,
                },
                {
                    title: "communication",
                    score: 2,
                },
            ],
            candidateInterpretations: [],
            candidateInv: {},
            avgScore: null,
            projectId: "",
            candidateEmail: "",
            candidateRating: {},
        };
    },
    watch: {
        candidate: {
            handler: async function (val) {
                await this.getBestCandidate(val.projectId);
            },
            deep: true,
        },
        showActivity: {
            handler: async function (val) {
                if (val && this.candidate && this.candidate.projectId) {
                    await this.getCandidateInv();
                }
            },
            deep: true,
        },
    },
    methods: {
        goToCheatingCard(id) {
            this.$router.push({
                path: `/anti-cheat/${id}`,
                // query: { id: this.project._id },
            });
        },
        toggleAntiCheat() {
            this.showAntiCheat = !this.showAntiCheat;
        },
        toggleInterpretation() {
            this.showInterpretation = !this.showInterpretation;
        },

        printDownload() {
            const url = `${BASE_URL}/candidates/candidateReport/?email=${this.candidateInfo?.Email}&projectId=${this.projectId}`;
            window.open(url, "_blank");
        },

        copyEmail() {
            const emailText = this.candidateInfo.Email;
            const el = document.createElement("textarea");
            el.value = emailText;
            document.body.appendChild(el);
            el.select();
            document.execCommand("copy");
            document.body.removeChild(el);
            this.isVisible = true;
            setTimeout(() => {
                this.isVisible = false;
            }, 5000);
        },
        getProjectName(projectId) {
            const project = this.Store.projects?.find((project) => project._id === projectId);
            return `${project?.jobTitle} - ${project?.seniority} - ${project?.name}`;
        },
        toggleCompare() {
            this.compareModal = !this.compareModal;
            this.Store.transformEvaluations(this.candidate.projectId);
        },
        calculateScore() {
            let score = 0;
            // let resultsCalc = this.candidate?.results?.filter((element) => element?.totalPoints !== undefined);

            this.candidate?.results?.forEach((element) => {
                if (element.rangesPoint || element.totalPoints) {
                    score += element.rangesPoint ? (element.rangesPoint * 100) / (element.quesionsNbr * 5) : (element.totalPoints * 100) / element.quesionsNbr;
                }
            });
            const averageScore = score / this.candidate?.results?.length;
            const roundedScore = averageScore.toFixed(2);
            return roundedScore;
        },
        /*calculateRawScore() {
            const total = this.skills.reduce((sum, skill) => sum + skill.score, 0);
            const avg = total / this.skills.length;
            return Math.round(avg) + "";
        },*/
        getCandidateResults(results) {
            let resultsCalc = results?.map((result) => {
                if (result.rangesPoint) {
                    result.totalPoints = result.rangesPoint;
                } else if (result.totalPoints === undefined || result.totalPoints === null) {
                    result.totalPoints = 0;
                }
                return result;
            });
            return resultsCalc;
        },
        calculateTime() {
            let minutes = Math.floor(this.candidate.candidateTime / (60 * 1000));
            if (minutes > 59) minutes = 59;

            let seconds = this.candidate.candidateTime % (60 * 1000);
            if (seconds > 59) seconds = 59;

            minutes = minutes.toString().padStart(2, "0");

            if (seconds < 10) {
                seconds = "0" + seconds.toString();
            } else {
                seconds = seconds.toString();
            }

            return `${minutes}:${seconds}`;
        },

        averageScore() {
            let score = 0;

            this.averageResults?.forEach((element) => {
                if (element.rangesPoint || element.totalPoints) {
                    score += element.rangesPoint ? (element.rangesPoint * 100) / (element.questionsNbr * 5) : (element.totalPoints * 100) / element.questionsNbr;
                }
            });
            let avgScore = score / this.averageResults?.length;
            this.avgScore = avgScore.toFixed(2);
            return avgScore.toFixed();
        },
        async getBestCandidate(id) {
            if (id === "") {
                // this.filteredCandidates = this.candidatesResults;
                return;
            }
            this.histogramData = [];
            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/company/bestCandidate/${id}`,
                headers: {},
                withCredentials: true,
            };
            // this.isLoading = true;
            axios
                .request(config)
                .then(async (response) => {
                    this.histogramData = response.data.candidatesScore;
                    this.averageResults = response.data.averageData;
                    this.averageScore();
                    this.isLoading = false;
                })
                .catch((error) => {
                    console.log(error);
                    this.isLoading = false;
                });
        },
        mapSkills() {
            if (!this.evaluation) return;
            // Map each main criterion to a skill bar
            this.skills = [
                {
                    label: "Code Correctness",
                    score: this.evaluation.codeCorrectness?.score || 0,
                },
                {
                    label: "Efficiency & Performance",
                    score: this.evaluation.efficiencyPerformance?.score || 0,
                },
                {
                    label: "Code Quality & Readability",
                    score: this.evaluation.codeQualityReadability?.score || 0,
                },
                {
                    label: "Problem-Solving Approach",
                    score: this.evaluation.problemSolvingApproach?.score || 0,
                },
                {
                    label: "Security & Robustness",
                    score: this.evaluation.securityRobustness?.score || 0,
                },
                {
                    label: "Scalability",
                    score: this.evaluation.scalability?.score || 0,
                },
                {
                    label: "Tooling & Originality",
                    score: this.evaluation.toolingOriginality?.score || 0,
                },
            ];
        },
        calculateRawScore() {
            return this.evaluation ? this.evaluation.overall_score : 0;
        },
        async getCandidateEvaluation() {
            let config = {
                method: "post",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/evaluate/candidate-evaluations`,
                headers: {
                    "Content-Type": "application/json",
                },
                params: {
                    email: this.candidateEmail,
                    projectId: this.projectId,
                },
                withCredentials: true,
            };
            axios
                .request(config)
                .then((response) => {
                    console.log(response);
                    this.evaluation = response.data.results[0];
                    this.mapSkills();
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        async getCandidateInv() {
            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/candidates/candidateInfo`,
                headers: {
                    "Content-Type": "application/json",
                },
                params: {
                    email: this.candidateEmail,
                    projectId: this.projectId,
                },
                withCredentials: true,
            };

            axios
                .request(config)
                .then((response) => {
                    this.candidateInv = response.data.CandidateInvitation;
                    this.Store.candidateInfoAct = response.data.candidateInfo;
                    this.Store.candidateActivity = response.data.candidateScore;
                    this.candidateInfo = response.data.candidateInfo;
                    this.candidate = response.data.candidateScore;
                    this.candidateInterpretations = response.data.interpretations;
                    this.candidateRating = response.data.candidateRating;
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        async getCandidateRank() {
            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/AssessmentTest/allResults?candidate=${this.candidateEmail}&projectId=${this.projectId}`,
                headers: {
                    "Content-Type": "application/json",
                },
                body: {
                    candidate: this.candidateEmail,
                    projectId: this.projectId,
                },
                withCredentials: true,
            };
            axios.request(config).then((response) => {
                this.candidateRank = response.data;
            });
        },
        getDateAndTime(dateString) {
            const date = new Date(dateString);
            const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
            const year = date.getUTCFullYear();
            // const month = (date.getUTCMonth() + 1).toString().padStart(2, "0"); // Months are zero-based, so we add 1
            const month = monthNames[date.getUTCMonth()];
            const day = date.getUTCDate().toString().padStart(2, "0");
            const hours = date.getUTCHours().toString().padStart(2, "0");
            const minutes = date.getUTCMinutes().toString().padStart(2, "0");
            const seconds = date.getUTCSeconds().toString().padStart(2, "0");
            let str = "th";
            if (day === "1" || day === "21" || day === "31") {
                str = "st";
            }
            if (day === "2" || day === "22") {
                str = "nd";
            }
            // const formattedDateTime = `${year}-${month}-${day}, ${parseInt(hours) + 1}:${minutes}:${seconds}`;
            const formattedDateTime = `${month} ${day + str}, ${year} at ${parseInt(hours) + 1}:${minutes}:${seconds}`;
            return formattedDateTime;
        },
        async fetchCheater(email, project_id) {
            axios
                .get(`${BASE_URL}/anticheat/cheater/${email}/${project_id}`, { withCredentials: true })
                .then((response) => {
                    this.fetchedCheater = response.data.cheater;
                })
                .catch((error) => console.error(error));
        },
    },
    async mounted() {
        this.projectId = this.$route.query.projectId;
        this.candidateEmail = this.$route.query.email;
        await this.Store.fetchProjects();
        await this.getBestCandidate(this.projectId);
        await this.getCandidateInv();
        await this.fetchCheater(this.candidateEmail, this.projectId);
        await this.getCandidateRank();
        await this.getCandidateEvaluation();
        // this.isLoading = false;
    },
};
</script>

<style scoped lang="scss">
@use "swiper/css";
@use "swiper/css/navigation";

.swiper-container {
    width: 300px;
    height: 260px;
    /* padding: 2rem 3rem;*/
    background: #fff;
    border-radius: 10px;
    border: 1px solid #ccc;
    box-shadow: 0px 0px 4px 1px rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar {
    width: 10px;
    /* Set the width of the scrollbar */
    background-color: #2196f334;
    border-radius: 500px;
    margin-right: 12px;
}

/* Style the scrollbar thumb */
::-webkit-scrollbar-thumb {
    border-radius: 40px;
    /* Set the border-radius to 40px for rounded corners */
    background-color: #2196f3;
    /* Set the background color of the scrollbar thumb */
}

.candidateActivityContainer {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    // height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 10;
    background-color: #f5f6f6;

    .candidateActivity {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .activityContent {
        padding: 24px 8px;
        width: 85%;
        // height: min-content;
        margin: 1% 0;
        background-color: #fff;
        z-index: 20;
        border-radius: 5px;
    }
}

.cheat-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.grid-row-widgets {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 16px;
}

.widget {
    background: #fff;
    border-radius: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px;
}

.widget-info {
    text-align: left;
}

.widget-title {
    text-align: left;
    font-size: 140px;
    font-weight: 700;
    font-size: 16px;
    font-weight: 900;
    color: #7d8fb3;
    margin-bottom: 8px;
}

.compareBtn {
    background: #2196f3;
    border-radius: 20px;
    width: 156px;
    height: 46px;
    padding: 10px 22px;
    color: #fff;
    border: none;
    font-size: 16px;
    font-weight: 700;
    font-style: normal;
    line-height: 24px;
    /* 150% */
    letter-spacing: -0.32px;
    transition: all 0.2s ease-in-out;

    &:hover {
        font-size: 14px;
    }
}

.background-1 {
    background: linear-gradient(135deg, #868cff 0%, #2196f3 100%);
}

.background-1 > div > .widget-info {
    color: #fff;
}

.background-1 > div > .widget-title {
    color: #ffffff;
    font-weight: 900;
}

.background-1 > div > .widget-info {
    color: #fff;
}

.background-1 > div > .widget-title {
    color: #e9edf7;
}

.background-2 {
    background: linear-gradient(135deg, #fab2ff 0%, #2196f3 100%);
}

.background-2 > div > .widget-info {
    color: #fff;
}

.background-2 > div > .widget-title {
    color: #ffffff;
    font-weight: 900;
}

.background-2 > div > .widget-title {
    color: #fff;
}

.widget-title {
    display: block;
    font-weight: 500;
    font-size: 16px;
    color: #a3aed0;
    margin-bottom: 8px;
    font-weight: 700;
}

.widget-info {
    display: block;
    font-weight: 700;
    font-size: 28px;
    color: #1b2559;
}

.score-applicants {
    //   width: 700px;
    // display: grid;
    // grid-template-columns: 35% 60%;
    // padding: 36px 0px 0 0;
    gap: 20px;
    //   & > :first-child {
    //     padding: 2%;
    //     height: 150%;
    //     width: 100%;
    //   }
    border-radius: 20px;

    > :first-child {
        width: 100%;
        border-radius: 20px;
        // padding: 10px 0;
        background-color: #fff;
        z-index: 10;
    }
}

.score {
    display: grid;
    height: 420px;
    width: 100%;
    align-items: center;
    grid-template-rows: 1fr 2fr 3fr;
    background-color: #fff;
    border-radius: 20px;
    padding: 10px 16px 0 16px;
}

.applicants {
    background-color: #fff;
    border-radius: 20px;
    padding: 5px 16px;
    display: flow-root;
    text-align: left;
}

.candidatesChartWrapper {
    height: fit-content;
    border-radius: 6px;
    padding: 16px;
    display: flow-root;
    text-align: left;
    margin-top: 30px;
    // display: grid;
    // grid-template-columns: 55% 40%;
    display: flex;
    flex-direction: column;
    gap: 2%;

    > :nth-child(1) {
        background-color: #fff;
        border-radius: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}

.stat-widget {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    padding: 0px 48px;
}

.stat-widget > div {
    box-shadow: 0px 18px 40px rgb(112 144 176 / 12%);
    padding: 8px 16px;
    display: grid;
    grid-template-rows: 1fr 2fr;
    align-items: center;
    background: #fff;
    border-radius: 20px;
}

.stat-widget > div > div {
    display: grid;
    grid-template-columns: 1fr 1fr;
}

.avg {
    display: flow-root;
}

.calendar-candidate {
    display: grid;
    grid-template-columns: 30% 65%;
    gap: 20px;
    padding: 36px 0px;
}

.calendar-candidate > button {
    border-radius: 24px;
    color: #fff;
    background: linear-gradient(135deg, #868cff 0%, #2196f3 100%);
    right: -215%;
    width: 90%;
    padding: 10%;
    position: relative;
    margin-top: 15%;
    margin-bottom: 30%;
}

.calendar {
    height: 350px;
    display: flow-root;
    background-color: #fff;
    border-radius: 20px;
    padding: 0px 16px;
    padding-top: 5%;

    img {
        width: 33px;
        height: 33px;
    }
}

.calendar > button {
    margin: 3% 0% 3% 63%;
}

.candidate {
    background-color: #fff;
    border-radius: 20px;
    padding: 0px 16px;
}

.candidate > button {
    margin-top: 2%;
    margin-left: 83%;
}

.calendar-candidate > button:hover {
    box-shadow: 0px 0px 40px rgb(94 116 105 / 25%);
    text-decoration: none !important;
}

.middleData {
    // height: 90%;
    // margin-top: 6%;
    // overflow: scroll;
    margin-bottom: 0;
}

.middleData::-webkit-scrollbar {
    display: none;
}

/* Optionally, you can also hide the scrollbar for Firefox */
.middleData {
    scrollbar-width: none;
}

.avgScore {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    font-weight: 700;
    color: #1b2559;
    background-color: #2195f34f;
    border-radius: 6px;
    margin-bottom: 2rem;
    padding: 1.5rem 2rem;

    input {
        -webkit-appearance: none;

        &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #2196f3;
            cursor: pointer;
        }
    }

    .tooltip {
        position: absolute;
        top: calc(-100% - 24px);
        transform: translateY(-100%);
        // left: 50%;
        // transform: translateX(-50%);
        background-color: #2196f3;
        color: #fff;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 700;
        transform: translateX(-50%);
        width: fit-content;

        &::after {
            content: "";
            position: absolute;
            width: 2px;
            height: 12px;
            background-color: #2196f3;
            bottom: -8px;
            left: calc(50% - 2px);
        }
    }

    // h1 {
    //     font-size: 40px;
    //     text-align: center;
    //     display: flex;
    //     flex-direction: column;
    //     span {
    //         font-size: 18px;
    //         font-weight: 600;
    //         color: #1b2559;
    //     }
    // }
    .scores {
        h3 {
            font-size: 16px;
            font-weight: 700;
            color: #1b2559;
            margin-right: 20px;

            span {
                font-size: 16px;
                font-weight: 500;
                color: #1b2559;
            }
        }
    }
}

#success-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 10px;
    height: 10px;
    // margin-left: -1px;
    border-radius: 50%;
    background: #2196f3;
    border: 3px solid #2196f3;
    cursor: pointer;
    transform: translateX(-50%);
}

.score-head {
    display: grid;
    justify-items: baseline;
    align-items: center;
    grid-template-columns: 5fr 2fr;
    margin: 3% 0;

    & > :first-child {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        text-align: left;

        h3 {
            color: #a3aed0;
            font-size: 14px;
            font-weight: 400;
        }
    }
}

.score-head > div {
    display: grid;
    grid-template-columns: 1fr 1fr;
    justify-items: baseline;
    align-items: center;
}

.score-head > div > span {
    color: #1b2559;
}

.score-head > select {
    justify-self: end;
    color: #a3aed0;
    border: none;
    outline: none;
}

.score-head > select:focus {
    border: none;
    outline: none;
}

.score-head > img {
    justify-self: end;
    height: 33px;
    height: 33px;
}

.recentapp {
    color: #1b2559;
    font-family: DM Sans;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
    /* 160% */
    letter-spacing: -0.4px;
}

.individualScoreWrapper {
    background-color: #fff;
    border-radius: 20px;
    overflow: hidden;
}

.closeBtn {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
}

.viewbtn {
    width: 15%;
    height: 8vh;
    margin-right: 5%;
    background-color: #2196f3;
    border-radius: 20px;
    font-family: "DM Sans";
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 28px;
    border: none;
    letter-spacing: -0.02em;
    //   background: none;
    color: #fff;
}

.viewbtn:hover {
    text-decoration: underline #2196f3;
}

.recentcandidates {
    display: grid;
    padding: 5% 0%;
    gap: 10px;
}

.tasks {
    display: grid;
    grid-template-rows: 1fr 1fr 1fr;
    gap: 10px;
    display: grid;
    background-color: #fff;
    border-radius: 20px;
    margin: 10% 10% 10% 2%;
    padding: 0% 3%;
}

.tasks > div {
    border-left: 3px solid #4318ff;
    display: grid;
    gap: 3px;
    justify-items: left;
    padding-left: 3%;
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
}

.thedate {
    font-family: "DM Sans";
    font-style: normal;
    font-weight: 700;
    font-size: 34px;
    line-height: 42px;
    /* identical to box height, or 124% */

    letter-spacing: -0.02em;
}

.time-stamp {
    font-family: "DM Sans";
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    letter-spacing: -0.02em;
    color: #a3aed0;
}

.thetask {
    font-family: "DM Sans";
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 28px;
    letter-spacing: -0.02em;
    color: #1b2559;
}

.tasks > div:hover {
    box-shadow: 0px 0px 40px rgb(94 116 105 / 25%);
}

.on-track {
    font-family: "DM Sans";
    font-weight: 700;
    font-size: 34px;
    color: #1b2559;
}

.projectTitle {
    font-family: "DM Sans";
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: -0.02em;
    color: #1b2559;
    margin-bottom: 20px;

    span {
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        letter-spacing: -0.02em;
        color: #1b2559;
        margin-left: 20px;
    }
}

.rating {
    display: grid;
    grid-template-columns: 2fr 1fr 3fr;
    gap: 10px;
    padding: 5% 1%;
    background-color: #fff;
    // border-radius: 20px;
    // margin: 10% 10% 10% 2%;
    // border: 2px solid red;
    // padding: 0% 3%;
    box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
    -webkit-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);
    -moz-box-shadow: 10px 10px 5px 0px rgba(0, 0, 0, 0.75);

    div {
        border-left: 1px solid #4318ff;
        display: grid;
        gap: 3px;
        justify-items: left;
        padding-left: 3%;
        border-top-right-radius: 10px;
        border-bottom-right-radius: 10px;
        padding-left: 3%;
        font-size: 16px;
        font-weight: 600;
        color: #1b2559;

        span {
            font-size: 16px;
            font-weight: 500;
        }
    }
}

.profile-name {
    font-family: "DM Sans";
    font-style: normal;
    font-weight: 700;
    font-size: 28px;
    line-height: 36px;
    letter-spacing: -0.02em;
    color: #1b2559;
}

.email {
    cursor: pointer;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-weight: 400;
    font-size: 13px;
    color: #2196f3;

    img {
        margin-left: 10px;
    }
}

.backBtn {
    width: 60px;
    height: 50px;
    background-color: #ededed;
    border: 1px solid #ededed;
    border-radius: 10px;
    transition: all 0.3s ease-in-out;

    &:hover {
        background-color: #fff;
    }
}

.shadow {
    box-shadow: 0px 0px 6px 1px rgba(0, 0, 0, 0.08);
    -webkit-box-shadow: 0px 0px 6px 1px rgba(0, 0, 0, 0.08);
    -moz-box-shadow: 0px 0px 6px 1px rgba(0, 0, 0, 0.08);
}

.empty {
    padding: 60px 0;
    margin-top: 3%;
}

.candidateData {
    display: grid;
    gap: 20px;
    grid-template-columns: 2fr 5fr 3fr;
    margin-top: 3rem;
}

.candidateInfo {
    // background-color: #fff;
    height: 40%;
    margin-top: 20%;
    padding: 5%;

    h1 {
        font-family: "DM Sans";
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 24px;
        letter-spacing: -0.02em;
        color: #1b2559;

        span {
            font-family: "DM Sans";
            font-style: normal;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            letter-spacing: -0.02em;
            color: #1b2559;
        }
    }

    :nth-child(2) {
        margin-top: 20%;
    }
}

#success-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 10px;
    height: 10px;
    // margin-left: -1px;
    border-radius: 50%;
    background: #2196f3;
    border: 3px solid #2196f3;
    cursor: pointer;
    transform: translateX(-50%);
}

.loader {
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    z-index: 10;
    background-color: #f5f6f6;
}
</style>
