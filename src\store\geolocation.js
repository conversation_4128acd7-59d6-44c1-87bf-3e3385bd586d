import { defineStore } from "pinia";
import axios from "axios";
import { checkIPLocation, handleRedirect } from "@/services/locationService";
import { BASE_URL } from "@/constants";

export const useGeolocationStore = defineStore("geolocation", {
    state: () => ({
        lang: "en",
        currency: "USD",
        country: "US",
        initialized: false,
    }),
    actions: {
        async initialize() {
            if (this.initialized) return;

            try {
                // Special case: If we're on go-profiling.dz, set Algerian values
                if (window.location.hostname.includes("go-profiling.dz")) {
                    this.country = "DZ";
                    this.currency = "DZD";
                    this.lang = "fr";
                    this.initialized = true;
                    return;
                }

                // For other domains, check if redirection is needed
                if (window.location.hostname.includes("go-platform.com")) {
                    const { shouldRedirect } = await checkIPLocation();
                    if (shouldRedirect) {
                        // Store Algerian values before redirect
                        this.country = "DZ";
                        this.currency = "DZD";
                        this.lang = "fr";
                        this.initialized = true;
                        handleRedirect();
                        return;
                    }
                }

                // No redirect needed or on a different domain, get geolocation from server
                await this.fetchGeolocation();
            } catch (error) {
                console.error("Error during initialization:", error);
                await this.fetchGeolocation();
            }
        },

        async fetchGeolocation() {
            if (this.initialized) return;

            try {
                const response = await axios.get(`${BASE_URL}/api/geolocation`);
                this.lang = response.data.lang;
                this.currency = response.data.currency;
                this.country = response.data.country;
                this.initialized = true;
            } catch (error) {
                console.error("Error fetching geolocation:", error);
                // Set defaults if not initialized
                if (!this.initialized) {
                    this.lang = "en";
                    this.currency = "USD";
                    this.country = "US";
                    this.initialized = true;
                }
            }
        },
    },
});
