<template>
    <div class="fixed inset-0 flex items-center justify-center bg-gray-900 bg-opacity-50" style="z-index: 999999999">
        <div class="bg-white p-6 rounded-lg shadow-lg w-96" style="width: 46%">
            <div class="flex items-center justify-between w-full">
                <h2 class="text-xl font-bold">{{ $t("See your assessment in practice!") }}</h2>
                <button class="text-xl font-bold text-gray-500 hover:text-gray-700" @click="$emit('close')">&times;</button>
            </div>

            <p class="mt-4">{{ $t("Congratulations on completing the assessment! You can now use it internally with your team or share it with candidates.") }}</p>
            <p class="mt-4">{{ $t("Please be aware that once you send out invitations to the candidates, you won’t be able to make further changes.") }}</p>

            <div class="mt-10 grid grid-cols-2 gap-4">
                <div class="border p-4 rounded-lg">
                    <span class="block text-lg"
                        >👁️
                        <h2 style="display: inline">
                            <strong>{{ $t("Try assesment") }}</strong>
                        </h2>
                    </span>
                    <p class="mt-6 text-sm text-left">{{ $t("Try this assessment individually or with your team.") }}</p>
                    <button class="mt-6 px-4 py-2 border rounded-full hover:bg-gray-200 w-full" @click="tryAssessment">{{ $t("Try yourself") }}</button>
                </div>
                <div class="border p-4 rounded-lg">
                    <span class="block text-lg"
                        >✉️
                        <h2 style="display: inline">
                            <strong>{{ $t("Invite candidates") }}</strong>
                        </h2>
                    </span>
                    <p class="mt-6 text-sm text-left">{{ $t("Invite your candidates to take this assessment.") }}</p>
                    <button class="mt-4 px-4 py-2 bg-pink-500 text-white rounded-full hover:bg-pink-600 w-full" style="background-color: #2196f3" @click="inviteCandidates">{{ $t("Invite") }}</button>
                </div>
            </div>
            <div class="flex items-center justify-center">
                <button class="mt-10 text-sm text-pink-500" @click="$emit('close')">{{ $t("I'll do it later") }}</button>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "ProjectPopup",
    methods: {
        tryAssessment() {
            this.$emit("try-assessment");
        },
        inviteCandidates() {
            this.$emit("invite-candidates");
        },
    },
};
</script>
<style scoped></style>
