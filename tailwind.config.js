/** @type {import('tailwindcss').Config} */
module.exports = {
    content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
    theme: {
        extend: {
            // fontFamily: {
            //     inter: ["Inter", "sans-serif"], // Adding Inter font
            // },
            fontSize: {
                "40px": ["40px", { lineHeight: "44px" }], // Custom font-size with line-height
            },
            backgroundImage: {
                "custom-gradient": "linear-gradient(90deg, #2196f3 0%, #C084FC 100%)",
            },

            letterSpacing: {
                tightest: "-0.04em", // Custom letter-spacing
            },
            colors: {
                NeonBlue: "#2196f3",
                CustomBlue: "#2196f3",
            },
            boxShadow: {
                card: "0 0 6px 1px rgba(0, 0, 0, 0.15)",
            },
            fontFamily: {
                "dm-sans": ["DM Sans", "sans-serif"],
                animation: {
                    "fade-in": "fadeIn 0.5s ease-out",
                    "fade-out": "fadeOut 0.5s ease-out",
                    "slide-from-top": "slideFromTop 0.5s ease-out",
                },
                keyframes: {
                    fadeIn: {
                        "0%": { opacity: "0" },
                        "100%": { opacity: "1" },
                    },
                    fadeOut: {
                        "0%": { opacity: "1" },
                        "100%": { opacity: "0" },
                    },
                    slideFromTop: {
                        "0%": { transform: "translateY(-50%)", opacity: "0" },
                        "100%": { transform: "translateY(0)", opacity: "1" },
                    },
                },
            },
            screens: {
                sm: "360px",
                md: "768px",
                l: "901px",
                lg: "991.98px",
            },
        },
        plugins: [],
    },
};
