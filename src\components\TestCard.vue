<template>
    <div class="mt-8 h-[500px] w-[90%] bg-white flex flex-col rounded-md shadow px-4 py-6 mx-auto overflow-hidden">
        <div class="flex flex-col flex-grow">
            <h2 class="w-full px-4 pb-4 text-2xl text-slate-700 font-semibold">
                {{ $t(name) }}
            </h2>
            <hr class="h-px w-full bg-gray-200" />
            <!-- Preview -->
            <p class="w-full text-left text-[#6b6f71] text-[17px] font-light px-4 pt-6 pb-8">
                {{ this.preview ? truncatePreview : "" }}
            </p>
            <div class="flex flex-wrap gap-2 mb-3 px-4">
                <!-- Skills Recommendation -->
                <span v-for="(recommendation, index) in getThreeRec(recommendations)" :key="index" class="bg-blue-50 text-[#2c2f30] rounded-full py-2 px-4 text-sm font-bold w-fit h-fit">
                    {{ truncateRecommendation(recommendation) }}
                </span>
            </div>
        </div>

        <!-- Footer section -->
        <div class="mt-auto w-full">
            <hr class="h-px w-full bg-gray-200 mb-5" />
            <div class="w-full flex flex-row justify-between items-center gap-4 px-4">
                <router-link to="/register" class="flex-1 text-base py-3 px-4 rounded-md text-white bg-NeonBlue whitespace-nowrap hover:opacity-85 text-center">
                    {{ $t("Use assessment") }}
                </router-link>
                <span
                    class="flex-1 text-base py-3 px-4 rounded-md border border-slate-300 text-slate-700 cursor-pointer hover:border-NeonBlue hover:text-NeonBlue text-center"
                    @click="navigateToPreview(name)"
                >
                    {{ $t("Preview") }}
                </span>
            </div>
        </div>
    </div>
</template>

<script>
import truncate from "@/helpers/truncateText";
export default {
    name: "TestCard",
    props: {
        tag: { default: null },
        name: { type: String, required: true },
        preview: { type: String, required: true },
        duration: { type: Number, required: false },
        recommendations: { type: Array, default: () => [] },
    },
    computed: {
        truncatePreview() {
            // Check if preview exists and is a string before translating
            return this.preview ? truncate(this.$t(this.preview), 90) : "";
        },
    },
    methods: {
        getThreeRec(recommendations) {
            return recommendations ? recommendations.slice(0, 3) : [];
        },
        truncateRecommendation(recommendation) {
            // Check if recommendation exists and is a string before translating
            return recommendation ? truncate(this.$t(recommendation), 25) : "";
        },
        navigateToPreview(jobPosition) {
            this.$router.push({
                path: "/job-position-preview",
                query: { jobPosition: jobPosition },
            });
        },
    },
};
</script>
