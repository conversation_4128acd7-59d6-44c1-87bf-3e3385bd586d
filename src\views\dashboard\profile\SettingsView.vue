<template>
    <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />

    <div class="company-container bg-white rounded-[8px] p-2">
        <div class="navigation-tabs">
            <div class="nav-links">
                <router-link to="/companyprofile" :class="`${this.$route.path == '/companyprofile' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'building']" class="w-4 h-4" />
                    <span class="text-base font-semibold">{{ $t("Company profile") }}</span>
                </router-link>

                <router-link to="/settings" :class="`${this.$route.path == '/settings' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'user-tie']" class="w-4 h-4" />
                    <span class="text-base font-semibold">{{ $t("Recruiter profile") }}</span>
                </router-link>
                <router-link to="/InvitedCoworkerTab" :class="`${this.$route.path == '/InvitedCoworkerTab' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'users']" class="w-4 h-4" />
                    <span class="text-base font-semibold">{{ $t("Invited Coworkers") }}</span>
                </router-link>
            </div>
        </div>
        <div class="w-[70%] mx-[15%] flex flex-col gap-[40px] mt-[3%]">
            <div class="flex gap-5">
                <div class="input_group">
                    <input type="text" id="first_name" v-model="newRecruiter.first_name" required name="first_name" :placeholder="$t('First Name')" />
                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                        />
                    </svg>
                    <span v-if="requiredFields.first_name" class="err_msg">{{ requiredFields.first_name }}</span>
                </div>
                <div class="input_group">
                    <input type="text" id="last_name" v-model="newRecruiter.last_name" required name="last_name" :placeholder="$t('Last Name')" />
                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                        />
                    </svg>
                    <span v-if="requiredFields.last_name" class="err_msg">{{ requiredFields.last_name }}</span>
                </div>
            </div>
            <div class="flex gap-5">
                <div class="input_group w-full relative">
                    <div class="phone-input-container">
                        <vue-tel-input
                            v-model="newRecruiter.phone_nbr"
                            mode="international"
                            :inputOptions="{ placeholder: $t('Phone number') }"
                            @validate="onPhoneValidate"
                            :validCharactersOnly="true"
                            :dropdownOptions="{ showFlags: true }"
                            class="custom-phone-input"
                        ></vue-tel-input>
                    </div>
                    <span v-if="requiredFields.phone_nbr" class="err_msg">{{ requiredFields.phone_nbr }}</span>
                </div>
                <div class="input_group">
                    <!-- Empty for layout symmetry -->
                </div>
            </div>
            <div class="flex justify-end gap-5">
                <button class="py-3 px-6 bg-NeonBlue text-base font-medium hover:opacity-80 text-white rounded-md" @click="updateRecruiter">{{ $t("Save") }}</button>
            </div>
            <hr />
            <h1 class="font-medium">{{ $t("Change password") }}</h1>
            <div class="flex gap-5">
                <div class="input_group">
                    <input :type="showPassword ? 'text' : 'password'" id="oldPassword" v-model="newRecruiter.oldPassword" required name="oldPassword" :placeholder="$t('Old password')" />
                    <button class="showpassword" @click.prevent="showPassword = !showPassword">
                        <img
                            loading="lazy"
                            decoding="async"
                            class="eye-icon"
                            :src="require(`@/assets/${showPassword ? 'icon_eye_closed.svg' : 'icon_eye.svg'}`)"
                            width="22"
                            height="22"
                            alt="Toggle password visibility"
                        />
                    </button>
                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                    </svg>
                    <span v-if="requiredFields.oldPassword" class="err_msg">{{ requiredFields.oldPassword }}</span>
                </div>
                <div class="input_group">
                    <input :type="showNewPassword ? 'text' : 'password'" id="newPassword" v-model="newRecruiter.newPassword" required name="newPassword" :placeholder="$t('New password')" />
                    <button class="showpassword" @click.prevent="showNewPassword = !showNewPassword">
                        <img
                            loading="lazy"
                            decoding="async"
                            class="eye-icon"
                            :src="require(`@/assets/${showNewPassword ? 'icon_eye_closed.svg' : 'icon_eye.svg'}`)"
                            width="22"
                            height="22"
                            alt="Toggle password visibility"
                        />
                    </button>
                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                        />
                    </svg>
                    <span v-if="requiredFields.newPassword" class="err_msg">{{ requiredFields.newPassword }}</span>
                </div>
            </div>
            <div class="flex justify-end gap-5">
                <button class="py-3 px-6 bg-NeonBlue text-base font-medium hover:opacity-80 text-white rounded-md my-[3%]" @click="updatePassword">{{ $t("Change password") }}</button>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { useStore } from "@/store/index";
import { BASE_URL } from "@/constants";
import ToastNotification from "@/components/ToastNotification";
import { VueTelInput } from "vue-tel-input";
import "vue-tel-input/vue-tel-input.css";

export default {
    name: "SettingsView",
    components: {
        ToastNotification,
        VueTelInput,
    },
    data() {
        return {
            recruiter: {
                first_name: "",
            },
            newRecruiter: {},
            requiredFields: {},
            showPassword: false,
            showNewPassword: false,
            isVisible: false,
            message: "",
            bgc: "",
            phoneValidation: {
                isValid: false,
                country: null,
            },
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        onPhoneValidate(phoneObject) {
            this.phoneValidation.isValid = phoneObject.valid;
            this.phoneValidation.country = phoneObject.country;

            // Clear previous phone validation errors when user types
            if (this.requiredFields.phone_nbr && phoneObject.valid) {
                delete this.requiredFields.phone_nbr;
            }
        },
        validatePhoneNumber(phoneNumber) {
            if (!phoneNumber) {
                return "Phone number is required";
            }

            // Remove all non-digit characters for length validation
            const digitsOnly = phoneNumber.replace(/\D/g, "");

            // Check if phone number has valid length (10-15 digits)
            if (digitsOnly.length < 10 || digitsOnly.length > 15) {
                return "Phone number must be between 10-15 digits";
            }

            // Check if the phone validation object indicates it's invalid
            if (!this.phoneValidation.isValid) {
                return "Please enter a valid phone number with correct country code";
            }

            return null; // Valid
        },
        async getUser() {
            try {
                axios
                    .get(`${BASE_URL}/user/info`, {
                        withCredentials: true,
                    })
                    .then((res) => {
                        this.Store.setCompanyName(this.$route.query.company_name);
                        this.user = res.data.userInfo;
                        this.recruiter = res.data.userInfo;
                        this.newRecruiter.first_name = res.data.userInfo.first_name;
                        this.newRecruiter.last_name = res.data.userInfo.last_name;
                        this.newRecruiter.phone_nbr = res.data.userInfo.phone_nbr;
                        this.Store.getCompanyCredit();
                    })
                    .catch((err) => {
                        console.log(err);
                        // Handle error if the token is invalid or expired
                        //this.$router.push('/');
                    });
            } catch (error) {
                console.error(error);
            }
        },
        updateRecruiter() {
            this.requiredFields = {};
            let isValid = true;
            if (!this.newRecruiter.first_name) {
                this.requiredFields.first_name = "First name is required";
                isValid = false;
            }
            if (!this.newRecruiter.last_name) {
                this.requiredFields.last_name = "Last name is required";
                isValid = false;
            }
            // Use the new phone validation method
            const phoneError = this.validatePhoneNumber(this.newRecruiter.phone_nbr);
            if (phoneError) {
                this.requiredFields.phone_nbr = phoneError;
                isValid = false;
            }
            if (isValid) {
                // console.log("Phone number from newRecruiter:", this.newRecruiter.phone_nbr);
                // console.log("Phone validation object:", this.phoneValidation);
                // console.log("Complete newRecruiter object:", this.newRecruiter);
                axios
                    .put(`${BASE_URL}/user/update`, this.newRecruiter, {
                        withCredentials: true,
                    })
                    .then(() => {
                        this.message = "Profile updated successfully";
                        this.bgc = "success";
                        this.isVisible = true;
                        setTimeout(() => {
                            this.isVisible = false;
                        }, 3000);
                        this.getUser();
                    })
                    .catch((err) => {
                        console.log(err);
                    });
            }
        },
        updatePassword() {
            this.requiredFields = {};
            let isValid = true;
            const passwordRegex = /^(?=.*[A-Z])(?=.*[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]).{8,}$/;

            if (!passwordRegex.test(this.newRecruiter.oldPassword)) {
                this.requiredFields.oldPassword = "Password must contain at least one uppercase letter, one special character, and be at least 8 characters long.";
                isValid = false; // Update the formValid flag
            }

            // this.requiredFields.newPassword = "New password is required";
            if (!passwordRegex.test(this.newRecruiter.newPassword)) {
                this.requiredFields.newPassword = "Password must contain at least one uppercase letter, one special character, and be at least 8 characters long.";
                isValid = false; // Update the formValid flag
            }

            if (isValid) {
                axios
                    .put(`${BASE_URL}/user/updatePass`, this.newRecruiter, {
                        withCredentials: true,
                    })
                    .then(() => {
                        this.message = "Password updated successfully";
                        this.bgc = "success";
                        this.isVisible = true;
                        setTimeout(() => {
                            this.isVisible = false;
                        }, 3000);
                        this.getUser();
                    })
                    .catch((err) => {
                        console.log(err);
                        this.requiredFields.oldPassword = err.response.data.oldPassword;
                    });
            }
        },
    },
    async mounted() {
        await this.getUser();
    },
};
</script>

<style scoped lang="scss">
.phone-input-container {
    position: relative;
    width: 100%;
    display: flex;
}

.phone-input-container .vue-tel-input {
    border-radius: 6px;
    border: 1.5px solid #e5e5ef;
    height: 48px;
    width: 100%;
    display: flex;
    align-items: center;
    transition: border-color 0.2s;
    box-sizing: border-box;
}

.phone-input-container .vue-tel-input:focus-within {
    border-color: #2196f3;
    box-shadow: none;
}

.phone-input-container .vti__input {
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0 6px 6px 0;
    width: 100%;
    height: 100%;
    flex-grow: 1;
    outline: none;
    font-size: 16px;
}

.phone-input-container .vti__dropdown {
    padding: 0 12px;
    border-right: 1px solid #e5e5ef;
    border-radius: 6px 0 0 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    min-width: 90px;
    max-width: 90px;
    flex-shrink: 0;
    height: 100%;
    font-size: 14px;
    font-weight: 500;
}

.phone-input-container .vti__dropdown:hover {
    background: #f8f9fa;
}

.phone-input-container .vti__dropdown-list {
    width: 320px;
    min-width: 320px;
    left: 0;
    top: calc(100% + 8px);
    border-radius: 6px;
    border: 1.5px solid #e5e5ef;
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-height: 200px;
    overflow-y: auto;
    background: white;
    z-index: 1000;
    position: absolute;
}

.phone-input-container .vti__dropdown-item {
    padding: 10px 12px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    font-size: 14px;
}
.company-container {
    padding: 24px 8px;
}

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-top: 48px; */
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 2px solid #edeff2;
    margin-bottom: 25px;
}

.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-weight: 700;
    /* Remove font-size here to allow Tailwind classes on span to take effect */
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links img {
    margin-right: 12px;
}

.nav-links a::after {
    content: "";
    background: #7d8fb3;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    transition: width 0.4s ease-in-out;
    border-radius: 25px;
}

.nav-links > a:hover::after {
    width: 100%;
}

.nav-links a.active::after {
    background: #2196f3;
    width: 100%;
}

.nav-links a.active * {
    color: #2196f3;
}

.input_group {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    border-radius: 20px;
    width: 100%;

    input {
        padding-left: 2.5rem;
        border: 1.5px solid #e5e5ef;
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
        padding-right: 1rem;
        border-radius: 6px;
        transition: border-color 0.2s;
        outline: none;
    }

    input:focus {
        border-color: #2196f3;
    }
}

.err_msg {
    color: #ff6969;
    font-size: 14px;
    font-weight: 300;
    position: absolute;
    top: 100%;
}

.showpassword {
    position: absolute;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
}
</style>
