<template>
    <section class="bg-gray-50 px-8 py-24 sm:py-32 lg:px-16 w-full">
        <div class="relative isolate">
            <div class="text-center mb-12 md:mb-20 space-y-4">
                <p class="text-3xl md:text-4xl font-bold text-gray-900">{{ $t("Choose the right plan for you") }}</p>
                <p class="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $t("Optimize your hiring process with plans designed to match your organization's unique requirements") }}
                </p>
            </div>

            <div class="mx-auto mt-16 grid max-w-7xl grid-cols-1 gap-8 sm:mt-20 md:grid-cols-3 lg:gap-6">
                <!-- Free Tier -->
                <div class="rounded-3xl p-8 ring-1 ring-gray-200 bg-white/60 transition-all hover:shadow-lg">
                    <h3 class="text-2xl font-bold text-gray-900 text-left">{{ $t("Free") }}</h3>
                    <p class="mt-2 text-gray-500 text-left">{{ $t("Best for personal use") }}</p>
                    <div class="mt-6 flex items-baseline gap-x-2">
                        <span class="text-3xl font-bold text-gray-900">{{ priceFree }} {{ currencySymbol }}</span>
                        <span class="text-gray-500">/{{ $t("month") }}</span>
                    </div>
                    <ul class="mt-8 space-y-3 text-gray-600 text-left">
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills Access: Essential skills only") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("AI Anti-Cheat System") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Result Interpretation & Reporting") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills Suggestion") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Admin Users") }}: 1
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills per Candidate") }}: 5
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Candidate Limit: Unlimited") }}
                        </li>
                    </ul>
                    <button
                        @click="() => this.$router.push('/register')"
                        class="mt-8 w-full rounded-md bg-gray-900 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 transition-colors"
                    >
                        {{ $t("Get started today") }}
                    </button>
                </div>

                <!-- Pay Per Use Tier -->
                <div class="rounded-3xl p-8 ring-1 ring-[#2196f3] bg-gradient-to-t from-[#2196f3]/10 to-white transition-all hover:shadow-lg">
                    <h3 class="text-xl font-bold text-gray-900 text-left">{{ $t("Pay per use") }}</h3>
                    <p class="mt-2 text-gray-500 text-left">{{ $t("Best for Small Business") }}</p>
                    <div class="mt-6 flex items-baseline gap-x-2">
                        <span class="text-3xl font-bold text-gray-900">{{ pricePayPerUse }} {{ currencySymbol }}</span>
                        <span class="text-gray-500">/1 {{ $t("candidate") }}</span>
                    </div>
                    <ul class="mt-8 space-y-3 text-gray-600 text-left">
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Hard skills assessments") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Soft skills assessments") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Custom questions") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Screening Questions") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("AI Anti-Cheat System") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills suggestion") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Result interpretation") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills per Assessment") }}: 5
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Assessment Limit: Create unlimited") }}
                        </li>
                    </ul>
                    <button
                        @click="() => this.$router.push('/register')"
                        class="mt-8 w-full rounded-md bg-[#2196f3] px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 transition-colors"
                    >
                        {{ $t("Get started today") }}
                    </button>
                </div>

                <!-- Subscription Tier -->
                <div class="rounded-3xl p-8 ring-1 ring-gray-200 bg-white/60 transition-all hover:shadow-lg">
                    <h3 class="text-xl font-bold text-gray-900 text-left">{{ $t("Subscription") }}</h3>
                    <p class="mt-2 text-gray-500 text-left">{{ $t("Best for enterprises") }}</p>
                    <div class="mt-6 text-left">
                        <span class="text-3xl font-bold text-gray-900">{{ $t("Custom") }}</span>
                    </div>
                    <ul class="mt-8 space-y-3 text-gray-600 text-left">
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills Access: All skills") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Custom questions") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Qualifycational questions") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("AI Anti-Cheat System") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills suggestion") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Result interpretation") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Admin Users") }}: 10
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Candidate Limit: Unlimited") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Assessment Limit: Create unlimited") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Skills per Assessment: Customizable") }}
                        </li>
                        <li class="flex gap-x-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t("Custom Assessments") }}
                        </li>
                    </ul>
                    <button
                        @click="() => this.$router.push('/register')"
                        class="mt-8 w-full rounded-md bg-gray-900 px-4 py-3 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 transition-colors"
                    >
                        {{ $t("Get started today") }}
                    </button>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    name: "PricingTable",
    props: {
        currency: {
            type: String,
            default: "USD",
        },
    },
    computed: {
        currencySymbol() {
            return this.currency;
        },
        priceFree() {
            return "0";
        },
        pricePayPerUse() {
            switch (this.currency) {
                case "DZD":
                    return "900";
                case "EUR":
                    return "7";
                case "USD":
                default:
                    return "7";
            }
        },
    },
};
</script>

<style scoped></style>
