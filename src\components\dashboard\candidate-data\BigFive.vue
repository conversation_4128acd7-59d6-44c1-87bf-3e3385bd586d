<template>
    <div v-if="show" class="fixed inset-0 z-50 flex items-center justify-center bg-[rgba(0,0,0,0.3)]">
        <div @click="toggleModal" class="fixed inset-0 -z-10"></div>
        <div class="w-full max-w-md sm:max-w-lg md:max-w-xl bg-white rounded-lg flex flex-col justify-between items-center pb-5 gap-4 overflow-hidden shadow-lg" v-if="!fullReport">
            <div class="bg-[#2196f3] w-full flex flex-row justify-between items-center p-5 rounded-t-lg">
                <h1 class="text-lg sm:text-xl font-semibold text-white truncate">{{ results.assessmentName }} {{ $t("results:") }}</h1>
                <button class="text-white text-xl font-bold ml-4" @click="toggleModal" aria-label="Close">
                    <font-awesome-icon :icon="['fas', 'times']" />
                </button>
            </div>
            <div class="w-full px-4 py-6 flex flex-col gap-2">
                <div v-for="trait in traitsArray" :key="trait.name" class="flex flex-row justify-between items-center w-full py-2 border-b border-[#e5e7eb]">
                    <div class="flex flex-col">
                        <span class="text-base font-semibold text-[#2196f3]">{{ trait.name }}</span>
                        <span class="text-xs text-slate-500">{{ trait.degree }}</span>
                    </div>
                    <div
                        class="flex items-center justify-center w-10 h-10 rounded-full font-bold text-base transition-all duration-200"
                        :class="{
                            'bg-[#2196f3] text-white border border-[#2196f3]': trait.degree === 'High' || trait.degree === 'Very High',
                            'bg-white text-[#2196f3] border border-[#2196f3]': trait.degree === 'Medium' || trait.degree === 'Low' || trait.degree === 'Very Low',
                        }"
                    >
                        {{ trait.name.charAt(0) }}
                    </div>
                </div>
            </div>
            <div class="flex flex-row justify-end items-center gap-2 w-full px-4 py-3">
                <button class="w-fit px-4 py-2 rounded text-base font-normal bg-white text-[#2196f3] border border-[#2196f3]" @click="toggleModal">{{ $t("Close") }}</button>
                <button class="w-fit px-4 py-2 rounded text-base font-normal bg-[#2196f3] text-white shadow" @click="fullReport = true">{{ $t("Full report") }}</button>
            </div>
        </div>
        <div v-else class="w-full max-w-2xl h-[95vh] bg-white overflow-y-scroll overflow-x-hidden rounded-lg reportWrapper shadow-lg">
            <div class="bg-[#2196f3] w-full text-lg flex flex-row justify-between items-center px-4 py-3 rounded-t-lg">
                <h1 class="font-medium text-white">{{ results.assessmentName }} {{ $t("interpretations :") }}</h1>
                <button class="text-white text-lg font-semibold" @click="fullReport = false"><font-awesome-icon :icon="['fas', 'times']" /></button>
            </div>
            <div class="px-4 py-6" v-for="(trait, index) in traitsScores" :key="index">
                <h1 class="text-lg mb-2 font-semibold text-[#2196f3]">{{ trait.name }}</h1>
                <p class="mb-3 text-sm text-slate-600">{{ trait.definition }}</p>
                <h2 class="text-base mb-2 mt-2 font-medium text-[#2196f3]">{{ trait.status }} {{ $t("in") }} {{ trait.name }}</h2>
                <h3 class="text-sm text-slate-700">{{ $t("Description") }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.description }}</p>
                <h3 class="text-sm text-slate-700">{{ $t("In personal relationships") }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.relationship }}</p>
                <h3 class="text-sm text-slate-700">{{ $t("At work") }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.atWork }}</p>
                <h3 class="text-sm text-slate-700">{{ $t("Their best attributes") }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.attributes }}</p>
                <h3 class="text-sm text-slate-700">{{ $t("Their main challenges") }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.challenges }}</p>
                <h3 class="text-sm text-slate-700">{{ $t("Tips for communicating and working with people") }} {{ trait.status }} {{ $t("in") }} {{ trait.name }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.tips }}</p>
                <h3 class="text-sm text-slate-700">{{ $t("Discussion points") }}</h3>
                <p class="mb-3 text-sm text-slate-600">{{ trait.discussion }}</p>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { BASE_URL } from "@/constants";
export default {
    name: "BigFive",
    props: ["show", "toggleModal", "results"],
    computed: {
        traitsArray() {
            const keys = Object.keys(this.results.personalityResults);
            return keys.map((key) => {
                return { name: key, degree: this.results.personalityResults[key] };
            });
        },
        traitsScores() {
            // Convert traitsData object into an array of objects with embedded attributes
            return Object.keys(this.interpretations).map((traitName) => {
                const trait = this.interpretations[traitName];
                return {
                    name: traitName,
                    definition: trait.definition,
                    status: trait?.scoreData?.status,
                    description: trait.scoreData?.description,
                    relationship: trait.scoreData?.relationship,
                    atWork: trait.scoreData?.at_work,
                    attributes: trait.scoreData?.attributes,
                    challenges: trait.scoreData?.challenges,
                    tips: trait.scoreData?.tips,
                    discussion: trait.scoreData?.discussion,
                };
            });
        },
    },
    mounted() {
        axios
            .post(`${BASE_URL}/interpretations/scores/${this.results.assessmentName}`, { traitScores: this.results.personalityResults })
            .then((res) => (this.interpretations = res.data))
            .then(() => console.log("traits scores", this.interpretations))
            .catch((err) => console.log(err));
    },
    data() {
        return {
            interpretations: {},
            fullReport: false,
        };
    },
};
</script>

<style lang="scss" scoped>
.reportWrapper::-webkit-scrollbar {
    width: 10px;
}
.reportWrapper::-webkit-scrollbar-track {
    background-color: #2195f31a;
}
.reportWrapper::-webkit-scrollbar-thumb {
    background-color: #2196f3;
    border-radius: 10px;
}
@media (max-width: 640px) {
    .max-w-md,
    .max-w-lg,
    .max-w-xl,
    .max-w-2xl {
        max-width: 98vw !important;
    }
    .px-4,
    .px-8 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }
    .py-6,
    .py-3 {
        padding-top: 1rem !important;
        padding-bottom: 1rem !important;
    }
}
</style>
