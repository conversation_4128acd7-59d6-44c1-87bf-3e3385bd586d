





<svg width="158" height="199" viewBox="0 0 158 199" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_3_718)">
<rect y="0.204102" width="158" height="158" rx="50" fill="white" fill-opacity="0.05"/>
</g>
<rect x="1" y="1.2041" width="156" height="156" rx="49" stroke="url(#paint0_linear_3_718)" stroke-opacity="0.2" stroke-width="2"/>
<g filter="url(#filter1_f_3_718)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M111.98 158.505H49C49.637 141.663 63.4919 128.204 80.4902 128.204C97.4886 128.204 111.344 141.663 111.98 158.505Z" fill="#7F76FF" fill-opacity="0.31"/>
</g>
<g filter="url(#filter2_d_3_718)">
<path d="M93.9997 65.8269C100.443 62.0109 103.665 60.1029 106.032 61.4567C108.398 62.8104 108.341 66.5295 108.228 73.9675L108.199 75.8919C108.167 78.0055 108.151 79.0624 108.545 79.9911C108.939 80.9198 109.698 81.6153 111.216 83.0063L112.599 84.2727C117.943 89.1677 120.615 91.6152 119.988 94.3636C119.362 97.112 115.852 98.3413 108.832 100.8L107.016 101.436C105.022 102.134 104.024 102.484 103.259 103.184C102.494 103.884 102.048 104.856 101.155 106.8L100.342 108.57C97.2013 115.412 95.6308 118.832 92.8775 119.177C90.1242 119.522 88.0115 116.563 83.7862 110.644L82.6931 109.113C81.4924 107.431 80.8921 106.59 80.0253 106.094C79.1585 105.598 78.1234 105.503 76.0532 105.314L74.1684 105.141C66.8833 104.474 63.2408 104.141 62.1655 101.606C61.0902 99.0704 63.2943 96.0122 67.7025 89.8958L68.8429 88.3134C70.0956 86.5754 70.7219 85.7063 70.9514 84.6995C71.1808 83.6927 70.9874 82.6621 70.6006 80.6009L70.2484 78.7244C68.887 71.471 68.2064 67.8443 70.2951 65.9325C72.3839 64.0207 75.8587 65.09 82.8084 67.2286L84.6064 67.7819C86.5813 68.3896 87.5687 68.6934 88.5773 68.5671C89.5859 68.4408 90.5015 67.8986 92.3326 66.8142L93.9997 65.8269Z" fill="url(#paint1_linear_3_718)"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M70.0817 40.117C71.299 38.8998 73.2725 38.8998 74.4897 40.117L82.8014 48.4287C84.0186 49.6459 84.0186 51.6194 82.8014 52.8366C81.5842 54.0539 79.6106 54.0539 78.3934 52.8366L70.0817 44.525C68.8645 43.3077 68.8645 41.3342 70.0817 40.117ZM47.2246 46.3508C48.4418 45.1336 50.4153 45.1336 51.6325 46.3508L62.0222 56.7404C63.2394 57.9576 63.2394 59.9311 62.0222 61.1483C60.8049 62.3655 58.8314 62.3655 57.6142 61.1483L47.2246 50.7587C46.0074 49.5415 46.0074 47.568 47.2246 46.3508ZM84.6272 54.6625C85.8444 53.4453 87.8179 53.4453 89.0351 54.6625L91.1131 56.7404C92.3303 57.9576 92.3303 59.9311 91.1131 61.1483C89.8958 62.3655 87.9223 62.3655 86.7051 61.1483L84.6272 59.0704C83.41 57.8532 83.41 55.8797 84.6272 54.6625ZM38.9129 62.9742C40.1301 61.7569 42.1036 61.7569 43.3209 62.9742L45.3988 65.0521C46.616 66.2693 46.616 68.2428 45.3988 69.46C44.1816 70.6772 42.2081 70.6772 40.9908 69.46L38.9129 67.3821C37.6957 66.1649 37.6957 64.1914 38.9129 62.9742ZM49.3025 73.3638C50.5197 72.1465 52.4932 72.1465 53.7105 73.3638L59.9442 79.5975C61.1614 80.8148 61.1614 82.7883 59.9442 84.0055C58.727 85.2227 56.7535 85.2227 55.5363 84.0055L49.3025 77.7717C48.0853 76.5545 48.0853 74.581 49.3025 73.3638Z" fill="white"/>
</g>
<defs>
<filter id="filter0_i_3_718" x="0" y="0.204102" width="158" height="163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_718"/>
</filter>
<filter id="filter1_f_3_718" x="9" y="88.2041" width="142.98" height="110.301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_3_718"/>
</filter>
<filter id="filter2_d_3_718" x="34" y="39.2041" width="90.0779" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_718"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_718" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3_718" x1="79" y1="0.204102" x2="79" y2="158.204" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint1_linear_3_718" x1="90.987" y1="61.0223" x2="90.987" y2="119.204" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF6E19"/>
<stop offset="0.552993" stop-color="#FFC225"/>
<stop offset="0.881518" stop-color="white"/>
</linearGradient>
</defs>
</svg>
