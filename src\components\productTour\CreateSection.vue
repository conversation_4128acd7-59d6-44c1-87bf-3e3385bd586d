<template>
    <section class="flex flex-col justify-between items-center gap-5 py-10 px-20">
        <span class="font-light text-base text-center w-fit px-5 py-3 rounded-full bg-[#EFECFF] text-[#2196f3]">
            {{ $t("How it works") }}
        </span>
        <h1 class="text-2xl lg:text-5xl font-bold text-center lg:text-left text-slate-700 w-full">
            {{ $t("1. Create high quality assessments, fast.") }}
        </h1>
        <h2 class="text-base lg:text-lg font-normal text-slate-700 text-center lg:text-left w-full">
            {{ $t("createAssessments.subtitle") }}
        </h2>

        <div class="flex flex-col justify-center items-center w-full lg:gap-[5rem] sm:gap-[2rem]">
            <div class="w-full mt-[5rem] flex flex-col lg:flex-row justify-between">
                <div class="w-1/2 flex flex-col justify-center items-center">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">
                        {{ $t("Set up your project") }}
                    </h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("createAssessments.setup") }}
                    </p>
                </div>
                <img loading="lazy" decoding="async" src="../../assets/Images/project_setup.png" alt="" class="w-full h-auto lg:w-[45%] aspect-auto" />
            </div>

            <div class="w-full flex flex-col lg:flex-row gap-4 lg:gap-12">
                <img loading="lazy" decoding="async" src="../../assets/Images/assessments_setup.png" alt="" class="w-full h-auto lg:w-[45%] aspect-auto" />
                <div class="flex flex-col justify-center items-center">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">
                        {{ $t("Select the tests that work for you") }}
                    </h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("createAssessments.selectTests") }}
                    </p>
                </div>
            </div>

            <div class="w-full flex flex-col-reverse lg:flex-row-reverse gap-4 lg:gap-12">
                <img loading="lazy" decoding="async" src="../../assets/Images/custom_questions_setup.png" alt="" class="w-full h-auto lg:w-[45%] aspect-auto" />
                <div class="flex flex-col justify-center items-center">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">
                        {{ $t("Select the additional questions") }}
                    </h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("createAssessments.additionalQuestions") }}
                    </p>
                </div>
            </div>

            <div class="w-full flex flex-col lg:flex-row gap-4 lg:gap-12">
                <img loading="lazy" decoding="async" src="../../assets/Images/review_setup.png" alt="" class="w-full h-auto lg:w-[45%] aspect-auto" />
                <div class="flex flex-col justify-center items-center">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">
                        {{ $t("Review and Configure") }}
                    </h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("createAssessments.reviewConfigure") }}
                    </p>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    name: "CreateSection",
    data() {
        return {
            step: 1,
        };
    },
};
</script>

<style lang="scss" scoped>
.buttons-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;

    @media (min-width: 640px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
        grid-template-columns: repeat(4, 1fr);
    }
}

button {
    flex: 1;
    min-width: 0;
}

img {
    @media (max-width: 1200px) {
        display: none;
    }
}
</style>
