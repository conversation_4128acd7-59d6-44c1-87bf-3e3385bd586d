image: node:slim

matrix:
  - env: PASS=$PASS USERNAME=$USERNAME DO_IP=$DO_IP

before_install:
  - apt-get update
  - apt-get install -y sshpass rsync
script:
  - npm install
  - npx --yes update-browserslist-db@latest
  - npm run build
  - sshpass -p "$PASS" rsync -av --progress --stats --delete -e "ssh -o StrictHostKeyChecking=no" --rsync-path="sudo rsync" dist/ $USERNAME@$DO_IP:/var/www/html/app/dist
cache:
  - node_modules
