# OpenRouter API Integration with Llama

This document describes the implementation of the OpenRouter API integration that allows users to create projects from job descriptions using AI-powered skill extraction.

## Overview

The integration adds a new feature to the project creation flow that allows recruiters to:
1. Paste a job description into a text field
2. Use AI (Llama model via OpenRouter) to extract relevant skills
3. Select which skills to include in their assessment project
4. Automatically populate project recommendations based on extracted skills

## Architecture

### Components

1. **OpenRouter Service** (`src/services/openRouterService.js`)
   - Handles API communication with OpenRouter
   - Processes job descriptions and extracts skills
   - Formats responses for application use
   - Includes error handling and validation

2. **UI Integration** (`src/views/dashboard/NewAssessment.vue`)
   - Adds job description input field to project creation
   - Displays extracted skills with selection interface
   - Integrates with existing project creation flow

3. **Configuration** (`.env` and `src/constants.js`)
   - API key management
   - Service configuration constants

## Features

### Job Description Processing
- **Input Validation**: Ensures job descriptions meet minimum/maximum length requirements
- **AI Processing**: Uses Llama 3.2 model via OpenRouter to extract skills
- **Skill Categorization**: Organizes skills into categories:
  - Technical Skills
  - Soft Skills
  - Tools & Technologies
  - Certifications

### User Interface
- **Alternative Input Method**: Secondary option alongside traditional project creation
- **Real-time Processing**: Shows loading states during API calls
- **Skill Selection**: Interactive checkboxes for skill selection
- **Error Handling**: User-friendly error messages for various failure scenarios

### Integration with Existing System
- **Auto-population**: Fills project name based on extracted skills
- **Recommendation Matching**: Attempts to match skills with existing job positions
- **Custom Recommendations**: Creates custom assessment recommendations based on skills

## Setup Instructions

### 1. Environment Configuration

Add your OpenRouter API key to the `.env` file:

```env
VUE_APP_OPENROUTER_API_KEY=your-actual-api-key-here
```

### 2. Get OpenRouter API Key

1. Visit [OpenRouter.ai](https://openrouter.ai/)
2. Sign up for an account
3. Navigate to the API Keys section
4. Create a new API key
5. Copy the key to your `.env` file

### 3. Model Configuration

The integration uses the free Llama 3.2 model by default:
- Model: `meta-llama/llama-3.2-3b-instruct:free`
- This can be changed in `src/constants.js` if needed

## Usage

### For Users

1. Navigate to the project creation page
2. Fill in basic project information (name, category, role, seniority)
3. Scroll down to see the "Alternative: Create from Job Description" section
4. Paste a job description (minimum 50 characters)
5. Click "Extract Skills" button
6. Wait for AI processing (usually 5-15 seconds)
7. Review extracted skills organized by category
8. Select desired skills using checkboxes
9. Click "Apply Selected Skills" to integrate with project
10. Continue with normal project creation flow

### For Developers

```javascript
import openRouterService from '@/services/openRouterService';

// Extract skills from job description
try {
    const skills = await openRouterService.extractSkillsFromJobDescription(jobDescription);
    console.log('Extracted skills:', skills);
} catch (error) {
    console.error('Extraction failed:', error.message);
}

// Validate job description
const isValid = openRouterService.validateJobDescription(jobDescription);
const errorMessage = openRouterService.getValidationError(jobDescription);
```

## API Response Format

The service returns skills in the following format:

```javascript
[
    {
        name: "JavaScript",
        category: "Technical Skills",
        type: "technical",
        selected: false
    },
    {
        name: "Communication",
        category: "Soft Skills", 
        type: "soft",
        selected: false
    }
    // ... more skills
]
```

## Error Handling

The integration handles various error scenarios:

- **Invalid API Key**: Clear message about API key configuration
- **Rate Limiting**: Informative message about trying again later
- **Network Errors**: Generic network connectivity message
- **Validation Errors**: Specific feedback about job description requirements
- **Parsing Errors**: Fallback when AI response cannot be parsed

## Testing

### Unit Tests
Run the unit tests:
```bash
npm test src/services/__tests__/openRouterService.test.js
```

### Manual Testing
Use the demo script:
```bash
node src/services/demo/openRouterDemo.js
```

### Integration Testing
1. Set up a real API key
2. Navigate to project creation page
3. Test with various job descriptions
4. Verify skill extraction and selection works

## Limitations

1. **API Costs**: While using the free model, there may be rate limits
2. **Accuracy**: AI extraction quality depends on job description clarity
3. **Language**: Currently optimized for English job descriptions
4. **Skill Mapping**: Custom skill-to-assessment mapping is simplified

## Future Enhancements

1. **Improved Skill Mapping**: More sophisticated mapping between skills and assessments
2. **Multi-language Support**: Support for job descriptions in other languages
3. **Skill Confidence Scores**: Display confidence levels for extracted skills
4. **Batch Processing**: Process multiple job descriptions at once
5. **Skill Suggestions**: Suggest additional relevant skills based on extracted ones
6. **Integration with Job Boards**: Direct integration with popular job posting sites

## Troubleshooting

### Common Issues

1. **"Invalid API key" error**
   - Verify API key is correctly set in `.env`
   - Ensure no extra spaces or characters
   - Check that the key is active on OpenRouter

2. **"Rate limit exceeded" error**
   - Wait a few minutes before trying again
   - Consider upgrading OpenRouter plan for higher limits

3. **Skills not extracting properly**
   - Ensure job description is detailed and well-formatted
   - Try with a longer, more comprehensive job description
   - Check that the job description is in English

4. **Network timeout errors**
   - Check internet connectivity
   - Try again as OpenRouter service may be temporarily unavailable

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('debug', 'openrouter');
```

This will log detailed information about API requests and responses to the browser console.

## Security Considerations

1. **API Key Protection**: Never commit real API keys to version control
2. **Input Sanitization**: Job descriptions are validated before processing
3. **Error Information**: Error messages don't expose sensitive system information
4. **Rate Limiting**: Built-in protection against excessive API calls

## Contributing

When contributing to this feature:

1. Follow existing code style and patterns
2. Add unit tests for new functionality
3. Update documentation for any changes
4. Test with both dummy and real API keys
5. Consider error handling for edge cases

## Support

For issues related to:
- **OpenRouter API**: Contact OpenRouter support
- **Integration bugs**: Create an issue in the project repository
- **Feature requests**: Discuss with the development team
