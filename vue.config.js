const { defineConfig } = require("@vue/cli-service");
// const BundleAnalyzerPlugin = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
const CompressionWebpackPlugin = require("compression-webpack-plugin");
const Critters = require('critters-webpack-plugin');

module.exports = defineConfig({
    transpileDependencies: true,
    pluginOptions: {
        i18n: {
            locale: "en",
            fallbackLocale: "en",
            localeDir: "locales",
            enableLegacy: true,
            runtimeOnly: false,
            compositionOnly: true,
            fullInstall: true,
        },
        prerenderSpa: {
            registry: undefined,
            renderRoutes: ["/:locale/home", "/:locale/login", "/:locale/contact"],
            useRenderEvent: true,
            headless: true,
            onlyProduction: true,
        },
    },
    configureWebpack: {
        //mode: "development",
        plugins: [
            new CompressionWebpackPlugin({
                algorithm: "gzip",
                test: /\.(js|css|html|svg|png)$/,
                threshold: 10240,
                minRatio: 0.8,
            }),
             new Critters({
              preload: "swap",
              pruneSource: true, // removes CSS already inlined
      }),
            // new BundleAnalyzerPlugin(),
        ],
        optimization: {
            splitChunks: {
                chunks: "all",
                minSize: 15000,
                maxSize: 250000,
                maxAsyncRequests: 30,
                maxInitialRequests: 30,
                enforceSizeThreshold: 50000,
            },
        },
    },

    chainWebpack: (config) => {
        config.plugins.delete("prefetch");
    },
});
