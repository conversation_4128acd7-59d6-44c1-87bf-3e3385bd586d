<template>
    <section class="flex flex-col justify-between items-center gap-5 py-10 px-20">
        <h1 class="text-2xl lg:text-5xl font-bold text-center lg:text-left text-slate-700 w-full">{{ $t("3. Analyze and decide on the best candidates.") }}</h1>
        <h2 class="text-base lg:text-lg font-normal text-slate-700 text-center lg:text-left w-full">
            {{ $t("analyze.subtitle") }}
        </h2>
        <div class="flex flex-col justify-center items-center w-full lg:gap-[5rem] sm:gap-[2rem]">
            <div class="w-full mt-[5rem] flex flex-col lg:flex-row justify-between">
                <div class="flex flex-col justify-center items-center w-full lg:w-1/2">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">{{ $t("Real-time assessment results") }}</h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("analyze.realtime.description") }}
                    </p>
                </div>
                <img loading="lazy" decoding="async" src="../../assets/Images/analyse_step1.png" alt="" class="w-full lg:w-[45%] aspect-auto" />
            </div>
            <div class="w-full flex flex-col lg:flex-row gap-4 lg:gap-12 relative">
                <img loading="lazy" decoding="async" src="../../assets/Images/talent_mapping.png" alt="" class="w-full lg:w-[45%] aspect-auto" />

                <div class="flex flex-col justify-center items-center w-full">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">{{ $t("Compare your candidates instantly") }}</h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("analyze.compare.description") }}
                    </p>
                </div>
            </div>
            <div class="w-full flex flex-col-reverse lg:flex-row-reverse gap-4 lg:gap-12">
                <img loading="lazy" decoding="async" src="../../assets/Images/candidate_evaluation.png" alt="" class="w-full lg:w-[45%] aspect-auto" />
                <div class="flex flex-col justify-center items-center">
                    <h1 class="text-xl lg:text-3xl font-semibold text-left w-full text-slate-700">{{ $t("Go in-depth with a candidate review") }}</h1>
                    <p class="text-left text-base font-light text-slate-500 mt-6 mb-8">
                        {{ $t("analyze.review.description") }}
                    </p>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    name: "AnalyzeSection",
    data() {
        return {
            step: 1,
        };
    },
};
</script>

<style lang="scss" scoped>
.buttons-grid {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    gap: 1rem;

    @media (min-width: 640px) {
        grid-template-columns: repeat(2, 1fr);
    }

    @media (min-width: 1024px) {
        grid-template-columns: repeat(4, 1fr);
    }
}

button {
    flex: 1;
    min-width: 0;
}

img {
    @media (max-width: 1200px) {
        display: none;
    }
}
</style>
