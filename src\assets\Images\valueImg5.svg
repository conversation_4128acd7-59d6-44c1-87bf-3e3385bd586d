<svg width="312" height="250" viewBox="0 0 312 250" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.79378" y="1.85042" width="270.687" height="198.742" stroke="#C6C6C6" stroke-width="2.10319" stroke-linejoin="round" stroke-dasharray="3.15 3.15"/>
<rect x="29.1328" y="28.1426" width="282.101" height="213.015" fill="#F1F1F1"/>
<rect x="36.0469" y="59.2305" width="268.284" height="177.32" fill="white"/>
<rect x="285.906" y="41.959" width="17.2715" height="4.60573" rx="2.30286" fill="#C4C4C4"/>
<circle cx="38.9255" cy="43.6872" r="2.87858" fill="#C4C4C4"/>
<circle cx="46.9723" cy="43.6872" r="2.87858" fill="#C4C4C4"/>
<circle cx="55.0348" cy="43.6872" r="2.87858" fill="#C4C4C4"/>
<path d="M84.8672 108.062L253.122 189.561" stroke="#FFB422" stroke-width="4.20637"/>
<path d="M160.062 207.439L173.207 78.0935" stroke="#FFB422" stroke-width="4.20637"/>
<path d="M82.2422 199.025L252.6 107.537" stroke="#FFB422" stroke-width="4.20637"/>
<g filter="url(#filter0_d_5530_25910)">
<rect x="132.195" y="105.959" width="73.6115" height="73.6115" rx="36.8058" fill="#5236FF"/>
</g>
<rect x="233.148" y="170.105" width="42.0637" height="42.0637" rx="21.0319" fill="#F1F1F1"/>
<rect x="62.7891" y="81.7734" width="44.1669" height="44.1669" rx="22.0835" fill="#F1F1F1"/>
<rect x="64.8906" y="182.727" width="37.8574" height="37.8574" rx="18.9287" fill="#F1F1F1"/>
<rect x="228.938" y="87.0293" width="37.8574" height="37.8574" rx="18.9287" fill="#F1F1F1"/>
<rect x="146.914" y="197.445" width="25.2382" height="25.2382" rx="12.6191" fill="#F1F1F1"/>
<rect x="160.586" y="64.9453" width="25.2382" height="25.2382" rx="12.6191" fill="#F1F1F1"/>
<g filter="url(#filter1_d_5530_25910)">
<rect x="146.914" y="128.041" width="29.4446" height="23.1351" rx="3.15478" fill="white"/>
</g>
<g filter="url(#filter2_d_5530_25910)">
<rect x="161.633" y="135.402" width="29.4446" height="23.1351" rx="3.15478" fill="#F0F0F0"/>
</g>
<defs>
<filter id="filter0_d_5530_25910" x="115.37" y="94.3915" width="117.776" height="117.778" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5.25797" dy="10.5159"/>
<feGaussianBlur stdDeviation="11.0417"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.700272 0 0 0 0 0.700272 0 0 0 0 0.700272 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5530_25910"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5530_25910" result="shape"/>
</filter>
<filter id="filter1_d_5530_25910" x="142.708" y="128.041" width="37.8581" height="31.5475" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.20637"/>
<feGaussianBlur stdDeviation="2.10319"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5530_25910"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5530_25910" result="shape"/>
</filter>
<filter id="filter2_d_5530_25910" x="157.426" y="135.402" width="37.8581" height="31.5475" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.20637"/>
<feGaussianBlur stdDeviation="2.10319"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5530_25910"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5530_25910" result="shape"/>
</filter>
</defs>
</svg>
