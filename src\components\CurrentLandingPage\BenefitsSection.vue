<template>
    <section class="px-8 sm:px-8 lg:px-16 py-24 sm:py-32 w-full">
        <div class="max-w-7xl mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-12 md:mb-20 space-y-4">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900">
                    {{ $t("Smarter Talent Choices, Better Results") }}
                </h2>
                <p class="text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $t("Reduce hiring mistakes, train teams effectively, and keep employees thriving.") }}
                </p>
            </div>

            <!-- Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-stretch">
                <div
                    v-for="(card, index) in cards"
                    :key="index"
                    @click="openModal(card)"
                    :class="[card.span, 'cursor-pointer rounded-3xl p-8 ring-1 ring-gray-200 bg-white/60 transition-all h-full hover:ring-[#2196f3] duration-300']"
                >
                    <div :class="['w-12 h-12 mb-6', card.iconBg, 'rounded-xl flex items-center justify-center']">
                        <font-awesome-icon :icon="card.icon" :class="['w-6 h-6', card.iconColor]" />
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4 text-left">{{ $t(card.title) }}</h3>
                    <p class="text-base text-gray-600 text-left" :class="[card.lineClamp === 3 ? 'line-clamp-3' : 'line-clamp-2']">
                        {{ $t(card.content) }}
                    </p>
                </div>
            </div>

            <!-- Modal with Animations -->
            <transition name="fade">
                <div v-if="selectedCard" class="fixed inset-0 z-50 backdrop-blur-sm bg-black/50 flex items-center justify-center p-4" @click.self="closeModal">
                    <transition name="modal">
                        <div v-if="selectedCard" class="bg-white rounded-3xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                            <div class="p-8">
                                <div class="flex justify-between items-start mb-6">
                                    <div :class="['w-12 h-12', selectedCard.iconBg, 'rounded-xl flex items-center justify-center']">
                                        <font-awesome-icon :icon="selectedCard.icon" :class="['w-6 h-6', selectedCard.iconColor]" />
                                    </div>
                                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600 transition-colors">
                                        <font-awesome-icon :icon="['fas', 'times']" class="w-6 h-6" />
                                    </button>
                                </div>
                                <h3 class="text-xl font-bold text-gray-900 mb-4 text-left">{{ $t(selectedCard.title) }}</h3>
                                <p class="text-base text-gray-600 whitespace-pre-line text-left">
                                    {{ $t(selectedCard.content) }}
                                </p>
                            </div>
                        </div>
                    </transition>
                </div>
            </transition>
        </div>
    </section>
</template>

<script>
export default {
    data() {
        return {
            selectedCard: null,
            cards: [
                {
                    span: "md:col-span-2",
                    icon: ["fas", "user-check"],
                    iconBg: "bg-blue-100",
                    iconColor: "text-blue-600",
                    lineClamp: 3, // Show 3 lines
                    title: "benefits.skillsBasedHiring.title",
                    content: "benefits.skillsBasedHiring.content",
                },
                {
                    span: "",
                    icon: ["fas", "level-up-alt"],
                    iconBg: "bg-green-100",
                    iconColor: "text-green-600",
                    lineClamp: 2, // Show 2 lines
                    title: "benefits.efficientInternalCareer.title",
                    content: "benefits.efficientInternalCareer.content",
                },
                {
                    span: "",
                    icon: ["fas", "user-graduate"],
                    iconBg: "bg-orange-100",
                    iconColor: "text-orange-600",
                    lineClamp: 2,
                    title: "benefits.upskillStrategically.title",
                    content: "benefits.upskillStrategically.content",
                },
                {
                    span: "md:col-span-2",
                    icon: ["fas", "brain"],
                    iconBg: "bg-purple-100",
                    iconColor: "text-purple-600",
                    lineClamp: 3,
                    title: "benefits.learningDevelopmentInvestment.title",
                    content: "benefits.learningDevelopmentInvestment.content",
                },
            ],
        };
    },
    methods: {
        openModal(card) {
            this.selectedCard = card;
        },
        closeModal() {
            this.selectedCard = null;
        },
    },
};
</script>

<style scoped>
/* Backdrop fade animation */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

/* Modal scale animation */
.modal-enter-active {
    animation: modal-enter 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave-active {
    animation: modal-leave 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes modal-enter {
    0% {
        transform: scale(0.95);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes modal-leave {
    0% {
        transform: scale(1);
        opacity: 1;
    }

    100% {
        transform: scale(0.95);
        opacity: 0;
    }
}

/* Smooth scrolling in modal */
.overflow-y-auto {
    scroll-behavior: smooth;
}
</style>
