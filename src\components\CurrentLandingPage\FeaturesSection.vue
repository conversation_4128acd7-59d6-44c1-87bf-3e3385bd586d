<template>
    <section class="bg-gray-50 px-8 sm:px-8 lg:px-16 py-24 sm:py-32 w-full">
        <div class="max-w-7xl mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-8 md:mb-16 lg:mb-20 space-y-4 px-4">
                <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">{{ $t("Build Confidence in Every Hiring Decision") }}</h2>
                <p class="text-sm sm:text-base md:text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    {{ $t("Secure assessments, precision skill matching, and actionable insights—all in one platform.") }}
                </p>
            </div>

            <!-- Sections Container -->
            <div class="space-y-12 md:space-y-20 lg:space-y-24">
                <!-- First Section -->
                <div class="flex flex-col gap-8 md:flex-row md:items-center md:gap-12">
                    <!-- Text Content -->
                    <div class="md:w-1/2 text-center md:text-left">
                        <h2 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4">{{ $t("Ensure Assessment Integrity & Trustworthy Results") }}</h2>
                        <p class="text-sm sm:text-base md:text-lg text-gray-600">
                            {{
                                $t(
                                    "Protect the validity of your skills data. Go Platform employs robust anti-cheat mechanisms, including session monitoring, plagiarism detection, and identity verification to ensure assessment results genuinely reflect candidate ability.",
                                )
                            }}
                        </p>
                    </div>
                    <!-- Image -->
                    <div class="md:w-1/2 flex justify-center md:justify-end">
                        <img :src="AntiCheatMonitor" alt="Workflow" class="w-full max-w-[90vw] md:max-w-[300px] lg:max-w-[400px] h-auto object-contain" />
                    </div>
                </div>

                <!-- Second Section -->
                <div class="flex flex-col gap-8 md:flex-row-reverse md:items-center md:gap-12">
                    <!-- Text Content -->
                    <div class="md:w-1/2 text-center md:text-left">
                        <h2 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4">{{ $t("Instantly Align Skills Assessments with Job Requirements") }}</h2>
                        <p class="text-sm sm:text-base md:text-lg text-gray-600">
                            {{
                                $t(
                                    "Go Platform recommends the most relevant skills to assess for each position based on current market data and role requirements. Ensure your evaluations directly target the competencies needed for high performance.",
                                )
                            }}
                        </p>
                    </div>
                    <!-- Image -->
                    <div class="md:w-1/2 flex justify-center md:justify-start">
                        <img :src="AutomatedWorkflow" alt="Analytics" class="w-full max-w-[90vw] md:max-w-[300px] lg:max-w-[400px] h-auto object-contain" />
                    </div>
                </div>

                <!-- Third Section -->
                <div class="flex flex-col gap-8 md:flex-row md:items-center md:gap-12">
                    <!-- Text Content -->
                    <div class="md:w-1/2 text-center md:text-left">
                        <h2 class="text-xl sm:text-2xl md:text-3xl font-bold text-gray-900 mb-4">{{ $t("Clear Interpretation for Every Score") }}</h2>
                        <p class="text-sm sm:text-base md:text-lg text-gray-600">
                            {{
                                $t(
                                    "GO Platform provides clear, easy-to-understand interpretations for every assessment score. Assess proficiency levels, identify strengths, pinpoint development areas, and understand what the results mean in a practical context.",
                                )
                            }}
                        </p>
                    </div>
                    <!-- Image -->
                    <div class="md:w-1/2 flex justify-center md:justify-end">
                        <img :src="Competencies" alt="Integrations" class="w-full max-w-[90vw] md:max-w-[350px] lg:max-w-[450px] h-auto object-contain" />
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import AntiCheatMonitor from "@/assets/Images/Anti-Cheat-Monitor.png";
import AutomatedWorkflow from "@/assets/Images/Automated-Workflow.png";
import Competencies from "@/assets/Images/Competencies.png";

export default {
    data() {
        return {
            AntiCheatMonitor: AntiCheatMonitor,
            AutomatedWorkflow: AutomatedWorkflow,
            Competencies: Competencies,
        };
    },
};
</script>
