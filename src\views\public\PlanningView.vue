<template>
    <PlanningContact :showEmail="showEmail" :toggleEmailWindow="toggleEmailWindow" :selectedPlan="selectedPlan" />
    <section class="flex flex-col items-center gap-5 w-full px-4 my-[15%]">
        <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ $t("Revolutionize Your Hiring") }}</h1>
            <h4 class="text-lg text-gray-600">{{ $t("Choose the perfect plan for your business needs") }}</h4>
        </div>

        <div class="mx-auto mt-8 w-full max-w-7xl grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Free Tier -->
            <div class="rounded-3xl p-8 ring-1 ring-gray-200 bg-white/60 transition-all hover:shadow-lg text-left">
                <div class="flex flex-col gap-5">
                    <div class="flex flex-col gap-1">
                        <h3 class="text-2xl font-bold text-gray-900">{{ $t("Free") }}</h3>
                        <p class="text-gray-500">{{ $t("Best for personal use") }}</p>
                    </div>
                    <div class="flex items-baseline gap-2">
                        <span class="text-3xl font-bold text-gray-900">{{ priceFree }} {{ currencySymbol }}</span>
                        <span class="text-gray-500">/{{ $t("month") }}</span>
                    </div>
                </div>
                <button @click="() => $router.push('/register')" class="mt-8 w-full rounded-md bg-gray-900 p-4 text-base font-semibold text-white shadow-sm hover:bg-gray-800 transition-colors">
                    {{ $t("Get Started") }}
                </button>
                <div class="mt-8 space-y-3 text-gray-600">
                    <div class="text-base font-semibold text-gray-900">{{ $t("What you get:") }}</div>
                    <ul class="space-y-3">
                        <li v-for="(feature, index) in freeFeatures" :key="index" class="flex items-start gap-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t(feature) }}
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Pay Per Use Tier -->
            <div class="rounded-3xl p-8 ring-1 ring-NeonBlue bg-gradient-to-t from-blue-50 transition-all hover:shadow-lg text-left">
                <div class="flex flex-col gap-5">
                    <div class="flex flex-col gap-1">
                        <h3 class="text-2xl font-bold text-gray-900">{{ $t("Pay per use") }}</h3>
                        <p class="text-gray-500">{{ $t("Best for Small Business") }}</p>
                    </div>
                    <div class="flex items-baseline gap-2">
                        <span class="text-3xl font-bold text-gray-900">{{ pricePayPerUse }} {{ currencySymbol }}</span>
                        <span class="text-gray-500">/1 {{ $t("candidate") }}</span>
                    </div>
                </div>
                <button
                    @click="navigateToRegister(`Basic - ${Plan === 'year' ? 'yearly' : 'Monthly'}`)"
                    class="mt-8 w-full rounded-md bg-NeonBlue p-4 text-base font-semibold text-white shadow-sm hover:opacity-85 transition-colors"
                >
                    {{ $t("Get Started") }}
                </button>
                <div class="mt-8 space-y-3 text-gray-600">
                    <div class="text-base font-semibold text-gray-900">{{ $t("Upgraded features:") }}</div>
                    <ul class="space-y-3">
                        <li v-for="(feature, index) in paidFeatures" :key="index" class="flex items-start gap-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t(feature) }}
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Subscription Tier -->
            <div class="rounded-3xl p-8 ring-1 ring-gray-200 bg-white/60 transition-all hover:shadow-lg text-left">
                <div class="flex flex-col gap-5">
                    <div class="flex flex-col gap-1">
                        <h3 class="text-2xl font-bold text-gray-900">{{ $t("Subscription") }}</h3>
                        <p class="text-gray-500">{{ $t("Best for enterprises") }}</p>
                    </div>
                    <div class="text-gray-900">
                        <span class="text-3xl font-bold">{{ $t("Custom") }}</span>
                    </div>
                </div>
                <button @click="() => $router.push('/contact')" class="mt-8 w-full rounded-md bg-gray-900 p-4 text-base font-semibold text-white shadow-sm hover:bg-gray-800 transition-colors">
                    {{ $t("Contact Us") }}
                </button>
                <div class="mt-8 space-y-3 text-gray-600">
                    <div class="text-base font-semibold text-gray-900">{{ $t("Unlimited features:") }}</div>
                    <ul class="space-y-3">
                        <li v-for="(feature, index) in enterpriseFeatures" :key="index" class="flex items-start gap-3">
                            <font-awesome-icon :icon="['fas', 'check']" class="mt-1 h-4 w-4 text-[#2196f3]" />
                            {{ $t(feature) }}
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import PlanningContact from "@/components/PlanningContact";
import { mapState } from "pinia";
import { useGeolocationStore } from "@/store/geolocation";

export default {
    name: "PlanningView",
    components: {
        PlanningContact,
    },
    data() {
        return {
            isShown: false,
            Plan: "month",
            showEmail: false,
            selectedPlan: "",
            freeFeatures: [
                "Skills Access: Essential skills only",
                "AI Anti-Cheat System",
                "Result Interpretation & Reporting",
                "Skills Suggestion",
                "Admin Users: 1",
                "Skills per Candidate: 5",
                "Candidate Limit: Unlimited",
            ],
            paidFeatures: [
                "Hard skills assessments",
                "Soft skills assessments",
                "Custom questions",
                "Screening Questions",
                "AI Anti-Cheat System",
                "Skills suggestion",
                "Result interpretation",
                "Skills per Assessment: 5",
                "Assessment Limit: Create unlimited",
            ],
            enterpriseFeatures: [
                "Skills Access: All skills",
                "Custom questions",
                "Qualifycational questions",
                "AI Anti-Cheat System",
                "Skills suggestion",
                "Result interpretation",
                "Admin Users: 10",
                "Candidate Limit: Unlimited",
                "Assessment Limit: Create unlimited",
                "Skills per Assessment: Customizable",
                "Custom Assessments",
            ],
        };
    },
    computed: {
        ...mapState(useGeolocationStore, ["currency"]),
        currencySymbol() {
            return this.currency;
        },
        priceFree() {
            return "0";
        },
        pricePayPerUse() {
            switch (this.currency) {
                case "DZD":
                    return "900";
                case "EUR":
                    return "7";
                case "USD":
                default:
                    return "7";
            }
        },
    },
    methods: {
        showMore() {
            this.isShown = true;
        },
        SelectPlan(plan) {
            this.Plan = plan;
        },
        toggleEmailWindow(selectedPlan) {
            this.selectedPlan = selectedPlan;
            this.showEmail = !this.showEmail;
        },
        navigateToRegister(plan) {
            this.$router.push({
                path: "/register",
                query: { plan: plan },
            });
        },
    },
};
</script>
