name: Deploy to DigitalOcean

on:
    push:
        branches: ["dev"]
jobs:
    deploy:
        runs-on: ubuntu-latest

        steps:
            - name: Checkout code
              uses: actions/checkout@v3

            - name: Install SSH Client
              run: sudo apt-get install -y sshpass

            - name: Deploy to DigitalOcean
              run: |
                  sshpass -p "${{ secrets.PASS }}" ssh -o StrictHostKeyChecking=no ${{ secrets.USERNAME }}@${{ secrets.DO_IP }} << 'EOF'
                    git config --global --add safe.directory /var/www/html/app
                    cd /var/www/html/app
                    sudo git pull
                    sudo npm i
                    sudo npm run build
                    sudo service nginx restart
                    # Add additional deployment commands here, e.g., restart services
                    # Example: sudo systemctl restart my-service
                  EOF
