/*@import url("https://fonts.googleapis.com/css?family=Roboto+Condensed");
@import url("https://fonts.googleapis.com/css2?family=Barlow+Condensed:wght@500&display=swap");*/

html,
body {
    font-family: "Roboto", Condensed;
    scroll-behavior: smooth;
}

#app {
    font-family: "Roboto", Condensed;
}

#app::-webkit-scrollbar {
    width: 10px;
    /* Width of the scrollbar */
}

#app::-webkit-scrollbar-track {
    border-radius: 10px;
    /* Rounded corners */
}

#app::-webkit-scrollbar-thumb {
    background-color: #888;
    /* Color of the scrollbar thumb */
    border-radius: 100px;
    /* Rounded corners */
}

#app::-webkit-scrollbar-thumb:hover {
    /* background-color: #555;  */
    /* Color of the scrollbar thumb on hover */
}
