<svg width="591" height="146" viewBox="0 0 591 146" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_12_6032)">
<rect x="20.4043" y="61.5891" width="61.1491" height="63.6635" rx="8" fill="#2196f3"/>
<rect x="118.285" y="52.4941" width="61.1491" height="72.7583" rx="8" fill="#2196f3"/>
<rect x="216.165" y="66.7861" width="61.1491" height="58.4665" rx="8" fill="#2196f3"/>
<rect x="314.046" y="20.6626" width="61.1491" height="104.59" rx="8" fill="#2196f3"/>
<rect x="411.923" y="74.5815" width="61.1491" height="50.6709" rx="8" fill="#2196f3"/>
<rect x="509.932" y="46.6477" width="61.0214" height="78.6049" rx="8" fill="#2196f3"/>
</g>
<defs>
<filter id="filter0_d_12_6032" x="0.404297" y="0.662598" width="590.549" height="144.59" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12_6032"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12_6032" result="shape"/>
</filter>
</defs>
</svg>
