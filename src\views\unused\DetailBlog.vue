<template>
    <div class="blog-detail-page" v-if="blog">
        <section class="blog-header">
            <div class="meta-container">
                <!--<div class="category">{{ $t(blog.category.category) }}</div>
                <div class="date">{{ $t(getDate(blog.date)) }}</div> -->
                <router-link :to="`/blogs`">
                    <span class="src">{{ $t("Blog") }}</span>
                </router-link>
                <span>/ </span>
                <span class="header-title"> {{ $t(blog.title) }}</span>
            </div>

            <h1 class="title">
                {{ $t(blog.title) }}
            </h1>
        </section>

        <section class="featured-image">
            <img loading="lazy" decoding="async" :src="getImgUrl(blog.image)" :alt="blog.title" />
        </section>

        <section class="blog-content">
            <div class="content" v-html="blog.text"></div>
            <div class="aboutus">
                <CardBlog :key="blogId" :id="blogId" :author="blog.author" :description="blog.description" :time="blog.reading_time" :date="getDate(blog.date)" />
            </div>
        </section>
    </div>
    <section class="related-posts">
        <div class="articles-grid">
            <ArticleCardBlog
                v-for="article in recommendedArticles"
                :key="article._id"
                :id="article._id"
                :author="article.author"
                :image="article.image"
                :description="article.description"
                :title="article.title"
                :category="article.category.category"
                :time="article.reading_time"
                :text="article.text"
                :date="getDate(article.date)"
            />
        </div>
    </section>
    <section class="cta-container">
        <div class="flex flex-col justify-start items-start mx-auto">
            <h1 class="font-bold text-5xl text-slate-700 text-start w-full mb-6">{{ $t("Re-invent your hiring process now.") }}</h1>

            <div class="flex flex-col lg:flex-row justify-start items-start gap-4 w-full">
                <a
                    href="https://calendly.com/aouf-abdellah/20min"
                    target="_blank"
                    class="whitespace-nowrap text-gray-900 font-semibold border-2 border-gray-200 rounded-md hover:border-CustomBlue hover:text-CustomBlue transition-colors px-4 py-2 flex items-center justify-center bg-white"
                >
                    {{ $t("Schedule a demo") }}
                </a>
            </div>
        </div>
    </section>
</template>

<script>
import ArticleCardBlog from "@/components/ArticleCardBlog.vue";
import { useStore } from "@/store/index";
import CardBlog from "@/components/DetailBlogabout.vue";
export default {
    name: "DetailBlog",
    components: { ArticleCardBlog, CardBlog },
    data() {
        return {
            blogId: "",
        };
    },
    mounted() {
        this.blogId = this.$route.params.id;
        this.Store.fetchArticles();
        window.scrollTo(0, 0);
    },
    watch: {
        "$route.params.id": {
            immediate: true,
            handler(newId) {
                if (newId) {
                    this.blogId = newId;
                    window.scrollTo({ top: 0, behavior: "smooth" });
                    this.$nextTick(() => {
                        this.Store.fetchArticles();
                    });
                }
            },
        },
    },
    computed: {
        blog() {
            return this.Store.getArticleData(this.blogId);
        },
        recommendedArticles() {
            return this.getRandomArticles(this.Store.articles, 3);
        },
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        getRandomArticles(allArticles, count) {
            let shuffled = [...allArticles].sort(() => 0.5 - Math.random());
            return shuffled.slice(0, count);
        },
        getImgUrl(imgFileId) {
            if (imgFileId) {
                return `data:image/png;base64,${imgFileId}`;
            }
            return require("@/assets/Images/user.png");
        },
        getDate(str) {
            const date = new Date(str);
            const options = { year: "numeric", month: "long", day: "numeric" };
            return date.toLocaleDateString("en-US", options);
        },
    },
};
</script>

<style lang="scss" scoped>
body,
html {
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
}

.src {
    color: #7d7d7d;
    padding-right: 10px;
}

.src:hover {
    color: #050505;
}

.header-title {
    padding-left: 10px;
}

.blog-detail-page {
    width: 100%;
    max-width: 920px;
    margin: 0 auto;
    padding: 2rem 1rem;
    box-sizing: border-box; // Ensure padding is included in width calculation
}

.blog-header {
    margin-bottom: 3rem;

    margin-top: 6rem;
}

.meta-container {
    display: flex;
    margin-bottom: 1.5rem;
}

.category {
    background: #3361ff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px 0 0 4px;
    font-size: 0.5rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.date {
    background: #f3f4f6;
    color: #4b5563;
    padding: 0.5rem 1rem;
    border-radius: 0 4px 4px 0;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.title {
    margin: 5rem 5rem;
    font-size: 3rem;
    font-weight: 600;
    line-height: 1.2;
    color: #111827;
    margin: 0;
    text-align: start;
}

.featured-image {
    img {
        width: 100%;
        max-height: 500px;
        object-fit: cover;
        border-radius: 8px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }
}

.blog-content {
    margin-top: 5em;
    margin-bottom: 2em;
    text-align: start;

    .content {
        font-size: medium;

        ::v-deep(p) {
            margin-bottom: 0.25rem;
        }

        ::v-deep(a) {
            color: #3361ff;
            text-decoration: underline;
        }
    }
}

.related-posts {
    display: inline-block;
    width: fit-content;
    border-top: 1px solid #e5e7eb;
    margin: 3rem;
    padding-top: 1.5rem;
}

.articles-grid {
    display: flex;
    gap: 1rem;

    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.articles-grid::-webkit-scrollbar {
    display: none;
}

.article-card {
    /* Prevent internal content from stretching the card */
    flex: 0 0 300px;

    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

@media (max-width: 768px) {
    .blog-header,
    .blog-content {
        padding: 0 1rem;
    }

    .title {
        font-size: 2rem;
    }

    .articles-grid {
        grid-template-columns: 1fr;
        flex-direction: rows;
    }
}

.cta-container {
    width: 100%;
    background: linear-gradient(to right, #f8fafc 0%, #f1f5f9 100%);
    padding: 3rem 3rem;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
}

.cta-container > div {
    width: 100%;
    padding: 0 1rem;
    /* Adjust side padding as needed */
}

.articles-grid {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    /* Enable horizontal scroll on small screens */
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    /* Hide scrollbar for Firefox */
}

.articles-grid::-webkit-scrollbar {
    display: none;
    /* Hide scrollbar for Chrome/Safari */
}

.article-card {
    flex: 0 0 300px;
    /* Fixed width cards */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
}

/* Responsive styles for screens <= 768px */
@media (max-width: 768px) {
    .articles-grid {
        flex-direction: column;
        /* Stack cards vertically */
        overflow-x: visible;
        /* Disable horizontal scrolling */
    }
}
</style>
