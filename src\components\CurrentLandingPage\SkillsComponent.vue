<template>
    <section class="bg-gray-50 metrics-section w-full">
        <div class="py-10 md:py-18">
            <div class="max-w-7xl mx-auto px-4 sm:px-2 lg:px-8">
                <!-- Testimonial Content -->
                <div class="max-w-3xl mx-auto text-center">
                    <svg class="h-8 w-8 text-[#2196f3] mx-auto mb-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 7L8 11H11V17H5V11L7 7H10M18 7L16 11H19V17H13V11L15 7H18Z" />
                    </svg>
                    <p class="text-xl md:text-2xl font-medium text-gray-900 mb-6">{{ $t("“GO Platform has created a consistent and unbiased way to Measure our workforce.”") }}</p>
                    <p class="text-lg text-gray-600 font-semibold"><PERSON></p>
                    <p class="text-gray-500">{{ $t("Global Data Science Lead, Accenture Applied Intelligence") }}</p>
                </div>

                <!-- SVG Animation Container -->
                <div class="flex items-center justify-center">
                    <div class="relative w-full max-w-[800px] mx-4 sm:mx-auto" style="aspect-ratio: 4/3">
                        <svg viewBox="0 0 800 600" class="w-full h-full">
                            <g v-for="(circle, index) in circles" :key="index">
                                <!-- Orbit path -->
                                <circle cx="400" cy="300" :r="circle.radius" fill="none" stroke="#EEEEEE" stroke-width="2" />

                                <!-- Orbiting icons with exact original positioning -->
                                <g
                                    v-for="(icon, iconIndex) in circle.icons"
                                    :key="iconIndex"
                                    :transform="`translate(${400 + Math.cos(circle.angle + iconIndex * Math.PI) * circle.radius}, ${300 + Math.sin(circle.angle + iconIndex * Math.PI) * circle.radius})`"
                                >
                                    <foreignObject width="48" height="48" x="-15" y="-15">
                                        <font-awesome-icon :icon="icon.icon" class="text-3xl" :style="{ color: icon.color }" />
                                    </foreignObject>
                                </g>
                            </g>

                            <!-- Center Text -->
                            <text x="400" y="300" text-anchor="middle" dominant-baseline="middle" class="text-4xl md:text-5xl font-semibold" fill="#4a5568">{{ $t("Skills") }}</text>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

export default {
    components: {
        FontAwesomeIcon,
    },
    data() {
        return {
            circles: [
                {
                    radius: 50,
                    speed: 1.0,
                    angle: 0,
                    icons: [
                        { icon: ["fab", "python"], color: "#3776AB" },
                        { icon: ["fab", "flutter"], color: "#1E97F3" },
                    ],
                },
                {
                    radius: 90,
                    speed: 0.8,
                    angle: 0,
                    icons: [
                        { icon: ["fab", "vuejs"], color: "#41B883" },
                        { icon: ["fab", "git-alt"], color: "#F05032" },
                    ],
                },
                {
                    radius: 140,
                    speed: 0.6,
                    angle: 0,
                    icons: [
                        { icon: ["fab", "react"], color: "#61DAFB" },
                        { icon: ["fab", "node"], color: "#339933" },
                    ],
                },
                {
                    radius: 190,
                    speed: 0.5,
                    angle: 0,
                    icons: [
                        { icon: ["fab", "angular"], color: "#DD0031" },
                        { icon: ["fab", "swift"], color: "#F05138" },
                    ],
                },
                {
                    radius: 240,
                    speed: 0.45,
                    angle: 0,
                    icons: [
                        { icon: ["fab", "figma"], color: "#F24E1E" },
                        { icon: ["fab", "linux"], color: "#FCC624" },
                    ],
                },
            ],
        };
    },
    methods: {
        animate() {
            this.circles.forEach((circle) => {
                circle.angle += circle.speed * 0.02;
            });
            requestAnimationFrame(this.animate);
        },
    },
    mounted() {
        this.animate();
    },
};
</script>
