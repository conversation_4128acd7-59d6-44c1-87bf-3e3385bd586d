<template>
    <div class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-10 z-[100] flex items-center justify-center" style="z-index: 1000">
        <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />

        <div class="w-[90%] h-[95%] overflow-y-auto shadow-card bg-white rounded-md pb-10 pt-0 flex gap-4" style="z-index: 5">
            <div class="w-[100%] p-4 pb-8">
                <form @submit.prevent="submitForm" class="h-[95%] mb-5">
                    <div class="w-full flex items-center justify-content">
                        <div class="w-full flex text-[#A9AAAE] py-4 items-center gap-4">
                            <h1 class="text-3xl font-semibold text-NeonBlue">{{ $t("Create Assessment") }}</h1>
                        </div>
                    </div>
                    <hr class="h-[5px] bg-[#F5F8FF]" />

                    <div class="mt-[1%] h-[85%] overflow-y-scroll p-2">
                        <div class="flex mb-[2rem] items-center justify-between">
                            <h1 class="text-xl text-slate-700 font-semibold">{{ $t("Information") }}</h1>
                        </div>
                        <div class="relative mb-[3%]">
                            <div class="grid grid-cols-2 gap-4">
                                <div class="grid grid-cols-1 gap-4">
                                    <div class="flex flex-col items-start gap-1 w-full">
                                        <label>{{ $t("Assessment Name") }}<span class="text-red-500"> *</span></label>
                                        <input
                                            required
                                            v-model="assessment.name"
                                            class="border border-gray-300 text-gray-900 text-sm rounded-lg outline-none focus:ring-NeonBlue focus:border-NeonBlue block w-full p-2.5"
                                            type="text"
                                            :placeholder="$t('Enter assessment name')"
                                            id="testTitle"
                                            autocomplete="false"
                                            style=""
                                        />
                                    </div>
                                    <div class="flex flex-col items-start gap-1 w-full">
                                        <label>{{ $t("Assessment Category") }}<span class="text-red-500"> *</span></label>
                                        <div class="border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5">
                                            <select class="w-[97%] outline-none cursor-pointer h-full" v-model="assessment.category" required>
                                                <option value="" selected disabled>{{ $t("Select a category") }}</option>
                                                <option v-for="(category, index) in categoriesList" :key="index" :value="category">
                                                    {{ $t(category) }}
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 gap-4">
                                    <div class="flex flex-col items-start gap-1 w-full">
                                        <label>{{ $t("Assessment Description") }}<span class="text-red-500"> *</span></label>
                                        <textarea
                                            style="resize: none"
                                            id=""
                                            cols="30"
                                            rows="5"
                                            v-model="assessment.description_test"
                                            :placeholder="$t('Enter assessment description')"
                                            class="border border-gray-300 text-gray-900 text-sm rounded-lg outline-none focus:ring-NeonBlue focus:border-NeonBlue block w-full p-2.5"
                                        ></textarea>
                                    </div>
                                </div>
                            </div>

                            <hr class="h-[5px] bg-[#F5F8FF] mt-[3rem] mb-[2rem]" />

                            <div class="flex items-center justify-between">
                                <h1 class="text-xl leading-none mb-[2rem] text-slate-700 font-semibold">{{ $t("Questions") }}:</h1>
                            </div>

                            <div
                                v-for="(question, questionIndex) in assessment.questions_list"
                                :key="questionIndex"
                                class="flex border-b-[5px] border-[#F5F8FF] flex-col mb-5 pb-5 items-start w-full gap-4"
                            >
                                <label class="w-full text-NeonBlue text-start font-bold text-lg">{{ $t("QUESTION") }} {{ questionIndex + 1 }}</label>
                                <div class="flex mb-5 items-start w-full gap-4">
                                    <div class="flex w-1/2 flex-col justify-start items-start gap-5">
                                        <div class="flex flex-col items-start gap-1 w-full">
                                            <label>{{ $t("QUESTION") }}<span class="text-red-500"> *</span></label>
                                            <input
                                                required
                                                v-model="question.question"
                                                class="border border-gray-300 text-gray-900 text-sm rounded-lg outline-none focus:ring-NeonBlue focus:border-NeonBlue block w-full p-2.5"
                                                type="text"
                                                :placeholder="$t('Enter the question')"
                                                id="testTitle"
                                                autocomplete="false"
                                                style=""
                                            />
                                        </div>
                                        <div class="flex flex-col items-start gap-1 w-full">
                                            <label>{{ $t("Description") }}</label>
                                            <textarea
                                                id=""
                                                cols="30"
                                                rows="4"
                                                style="resize: none"
                                                v-model="question.description"
                                                :placeholder="$t('Enter the question description')"
                                                class="border border-gray-300 text-gray-900 text-sm rounded-lg outline-none focus:ring-NeonBlue focus:border-NeonBlue block w-full p-2.5"
                                            ></textarea>
                                        </div>
                                        <div class="flex flex-col items-start gap-1 w-full">
                                            <label>{{ $t("Code") }}</label>
                                            <input
                                                v-model="question.code"
                                                class="border border-gray-300 text-gray-900 text-sm rounded-lg outline-none focus:ring-NeonBlue focus:border-NeonBlue block w-full p-2.5"
                                                type="text"
                                                :placeholder="$t('Enter the question code')"
                                                id="category"
                                                autocomplete="false"
                                                style=""
                                            />
                                        </div>
                                        <div class="flex flex-col items-start gap-1 w-full">
                                            <label>{{ $t("Answer") }}:</label>
                                            <div class="border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5">
                                                <select
                                                    class="w-[97%] outline-none cursor-pointer h-full"
                                                    v-model="assessment.answers[questionIndex + 1]"
                                                    @change="updateAnswer($event, questionIndex)"
                                                    required
                                                >
                                                    <option value="" selected disabled>{{ $t("Select an answer") }}</option>
                                                    <option v-for="(value, key) in question.options" :key="key" :value="key">
                                                        {{ convertToAlphabet(key) }}
                                                    </option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col w-1/2 justify-start items-start gap-4">
                                        <div class="flex flex-col items-start gap-1 w-full">
                                            <label>{{ $t("Options") }}:</label>
                                            <div class="p-3 h-[19.5rem] overflow-y-scroll w-full bg-[#F4FAFF] rounded-lg">
                                                <div class="flex mb-1 w-full relative items-center gap-1" v-for="(value, key) in question.options" :key="key">
                                                    <span class="rounded-md h-12 w-12 bg-white flex items-center justify-center border border-[#d8e2ee]">
                                                        {{ convertToAlphabet(key) }}
                                                    </span>
                                                    <input
                                                        class="h-12 w-full border border-gray-300 text-gray-900 text-sm rounded-lg outline-none focus:ring-NeonBlue focus:border-NeonBlue block p-2.5"
                                                        v-model="question.options[key]"
                                                        type="text"
                                                        :placeholder="$t('Option text')"
                                                        required
                                                    />
                                                    <button
                                                        class="absolute flex items-center justify-center text-red-500 right-[2%]"
                                                        @click="removeOption(key)"
                                                        v-if="Object.keys(question.options).length - 1 == Object.keys(question.options).indexOf(key) && Object.keys(question.options).length > 4"
                                                    >
                                                        <font-awesome-icon class="w-6 h-6 hover:bg-red-200 rounded-full" :icon="['far', 'circle-xmark']" />
                                                    </button>
                                                </div>
                                                <button
                                                    class="rounded-md hover:opacity-80 bg-NeonBlue text-white font-bold h-12 w-12 flex items-center justify-center"
                                                    @click="addOption(questionIndex)"
                                                >
                                                    +
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="questionIndex == assessment.questions_list.length - 1" class="flex items-center justify-between w-full">
                                    <ButtonComponent v-if="assessment.questions_list.length > 15" intent="secondary" @click="removeQuestion(questionIndex)">
                                        {{ $t("Remove Question") }}
                                    </ButtonComponent>
                                    <div v-else class="h-12 mt-3 w-[40%]"></div>
                                    <ButtonComponent intent="primary" @click="addQuestion">{{ $t("Add Question") }} + </ButtonComponent>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center py-2 pr-2 gap-3 justify-end w-full">
                        <button
                            @click="
                                () => {
                                    toggleCreateAssesment();
                                }
                            "
                            class="w-[15%] h-15 py-3 rounded-md border border-gray-300 font-medium text-gray-900 hover:bg-gray-100"
                        >
                            {{ $t("Cancel") }}
                        </button>
                        <button class="w-[15%] h-15 py-3 rounded-md border-none bg-NeonBlue font-medium text-white hover:opacity-85" type="submit">{{ $t("Submit") }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script>
import ButtonComponent from "@/components/ReusableComponents/ButtonComponent.vue";
//import FloatingLabel from "@/components/ReusableComponents/FloatingLabel.vue";
//import SelectComponent2 from "@/components/ReusableComponents/SelectComponent2.vue";
import { useStore } from "@/store/index";
import ToastNotification from "@/components/ToastNotification.vue";
import axios from "axios";
import { BASE_URL } from "@/constants";
//import LoaderComponent from "@/components/LoaderComponent.vue";

export default {
    name: "ScreenerQst",
    components: {
        ButtonComponent,
        // FloatingLabel,
        // SelectComponent2,
        // LoaderComponent,
        ToastNotification,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    props: {
        toggleCreateAssesment: {
            type: Function,
        },
    },
    data() {
        return {
            type: "Text",
            categoriesList: ["Psychometrics", "Hard Skills", "Soft Skills"],
            currentQuestion: {
                question: "",
                description: "",
                options: {
                    1: "",
                    2: "",
                    3: "",
                    4: "",
                },
            },
            currentAnswer: "",
            assessment: {
                name: "",
                category: "",
                description_test: "",
                questions_list: [
                    {
                        question_number: 1,
                        question: "",
                        description: "",
                        code: "",
                        options: {
                            1: "",
                            2: "",
                            3: "",
                            4: "",
                        },
                    },
                ],
                answers: {},
            },
            interpretations: [
                { range: [0, 33], title: "", description: "" },
                { range: [34, 66], title: "", description: "" },
                { range: [67, 100], title: "", description: "" },
            ],
            isLoading: false,
            isVisible: false,
            isOpen: false,
            message: "",
            bgColor: "",
        };
    },
    methods: {
        clearQuestion() {
            this.currentQuestion = {
                question: "",
                description: "",
                options: {
                    1: "",
                    2: "",
                    3: "",
                    4: "",
                },
            };
        },
        addQuestionToTable() {
            // Add current question to the questions list
            const newQuestion = { ...this.currentQuestion };
            this.assessment.questions_list.push(newQuestion);

            // Add the selected answer to the answers object
            const questionNumber = this.assessment.questions_list.length;
            this.assessment.answers[questionNumber] = this.currentAnswer;

            // Clear the current question inputs
            this.currentQuestion = {
                question: "",
                description: "",
                options: {
                    1: "",
                    2: "",
                    3: "",
                    4: "",
                },
            };
            this.currentAnswer = ""; // Clear the selected answer
        },

        async createInterpretation() {
            const interpretationData = {
                assessmentName: this.assessment.name,
                interpretations: this.interpretations,
            };
            axios
                .post(`${BASE_URL}/interceptions/uploadInterception`, interpretationData)
                .then((response) => console.log("Here is the interpretation", response.data))
                .catch((error) => console.error(error));
        },
        addInterpretation() {
            this.interpretations.push({ range: [0, 0], title: "", description: "" });
        },
        removeInterpretation(index) {
            this.interpretations.splice(index, 1);
        },
        removeQuestion(index) {
            this.assessment.questions_list.splice(index, 1);
        },
        async submitForm() {
            this.isLoading = true;

            axios
                .post(`${BASE_URL}/uploadAssessment`, this.assessment, {
                    withCredentials: true,
                })
                .then((response) => console.log("Assessment submitted successfully", response.data))
                .then(() => this.createInterpretation())
                .then(() => {
                    const emptyAssessment = {
                        name: "",
                        category: "",
                        description_test: "",
                        questions_list: [
                            {
                                question_number: 1,
                                question: "",
                                description: "",
                                code: "",
                                options: {
                                    1: "",
                                    2: "",
                                    3: "",
                                    4: "",
                                },
                            },
                        ],
                        answers: {},
                    };

                    this.assessment = emptyAssessment;

                    const emptyInterpretation = [
                        { range: [0, 33], title: "", description: "" },
                        { range: [34, 66], title: "", description: "" },
                        { range: [67, 100], title: "", description: "" },
                    ];
                    this.interpretations = emptyInterpretation;
                    this.isVisible = true;
                    this.message = "Assessment uploaded successfully!";
                    this.bgc = "success";
                })
                .catch((error) => {
                    console.error("Error submitting assessment:", error.response || error.message);
                    this.isVisible = true;
                    this.message = "Error uploading assessment.";
                    this.bgc = "error";
                })
                .finally(() => {
                    this.isLoading = false;
                });
        },
        toggleType() {
            this.isOpen = !this.isOpen;
        },
        updateAnswer(event, questionIndex) {
            const selectedValue = Number(event.target.value); // Convert the selected value to a number
            this.assessment.answers = {
                ...this.assessment.answers,
                [(questionIndex + 1).toString()]: selectedValue, // Update the answers object reactively
            };
        },
        addQuestion() {
            this.assessment.questions_list.push({
                question_number: this.assessment.questions_list.length + 1,
                question: "",
                options: {
                    1: "",
                    2: "",
                    3: "",
                    4: "",
                },
            });
        },
        addOption(questionIndex) {
            const currentOptions = this.assessment.questions_list[questionIndex].options;
            const nextOptionNumber = (Object.keys(currentOptions).length + 1).toString(); // Ensure the key is a string
            // Directly set the new option
            this.assessment.questions_list[questionIndex].options[nextOptionNumber] = "";
        },
        removeOption(questionIndex, optionKey) {
            const currentOptions = this.assessment.questions_list[questionIndex].options;
            if (Object.keys(currentOptions).length > 4) {
                // Directly delete the option
                delete this.assessment.questions_list[questionIndex].options[optionKey];
            }
        },
        convertToAlphabet(key) {
            const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            return alphabet[parseInt(key) - 1]; // Convert numeric key to alphabet (1 -> A, 2 -> B, etc.)
        },
    },
};
</script>
