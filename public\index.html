<!doctype html>
<html lang="">
    <head>
        <meta name="msvalidate.01" content="BB058CC1333CA119FE8F4D7CD1350E34" />
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width,initial-scale=1.0" />
        <meta name="msvalidate.01" content="BB058CC1333CA119FE8F4D7CD1350E34" />
        <!-- Primary Meta Tags -->
        <title>GO PLATFORM - Professional Assessment & Skills Testing Platform</title>
        <meta name="title" content="GO PLATFORM - Professional Assessment & Skills Testing Platform" />
        <meta name="description" content="GO PLATFORM offers comprehensive professional skills assessment, testing, and candidate evaluation tools. Join our platform to assess technical skills, personality traits, and job fit." />
        <meta name="keywords" content="skills assessment, professional testing, candidate evaluation, job skills, technical assessment, recruitment platform" />
        <meta name="author" content="GO PLATFORM" />
        <meta name="robots" content="index, follow" />
        <meta name="language" content="English" />

        <!---->
        <title>Invite Candidate</title>

        <!-- Open Graph Meta Tags -->
        <meta property="og:type" content="website" />
        <meta property="og:url" content="https://go-platform.com/" />
        <meta property="og:title" content="Invitation to Join Our Platform and pass some tests" />
        <meta property="og:description" content="We invite you to join our amazing platform where you can learn, grow, and network with professionals." />
        <meta property="og:image" content="go_logo.svg" />
        <meta property="og:site_name" content="GO PLATFORM" />
        <!---->

        <!-- Twitter -->
        <meta name="twitter:card" content="summary_large_image" />
        <meta name="twitter:url" content="https://go-platform.com/" />
        <meta name="twitter:title" content="GO PLATFORM - Professional Skills Assessment Platform" />
        <meta name="twitter:description" content="Join our comprehensive skills assessment platform for professional testing and candidate evaluation. Measure technical skills, personality traits, and job fit." />
        <meta name="twitter:image" content="https://go-platform.com/go_logo.svg" />

        <!-- Preconnect to Google Fonts domains -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

        <!-- Load fonts (faster than @import) -->
        <link href="https://fonts.googleapis.com/css?family=Roboto+Condensed" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Barlow+Condensed:wght@500&display=swap" rel="stylesheet" />

        <link rel="icon" href="<%= BASE_URL %>go_logo.svg" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=DM Sans:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.1/css/all.min.css" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@500&display=swap" rel="stylesheet" />

        <!-- Yandex.Metrika counter -->
        <meta name="msvalidate.01" content="BB058CC1333CA119FE8F4D7CD1350E34" />
        <script type="text/javascript">
            (function (c, l, a, r, i, t, y) {
                c[a] =
                    c[a] ||
                    function () {
                        (c[a].q = c[a].q || []).push(arguments);
                    };
                t = l.createElement(r);
                t.async = 1;
                t.src = "https://www.clarity.ms/tag/" + i;
                y = l.getElementsByTagName(r)[0];
                y.parentNode.insertBefore(t, y);
            })(window, document, "clarity", "script", "m9b9xddo97");
        </script>

        <script type="text/javascript">
            (function (m, e, t, r, i, k, a) {
                m[i] =
                    m[i] ||
                    function () {
                        (m[i].a = m[i].a || []).push(arguments);
                    };
                m[i].l = 1 * new Date();
                for (var j = 0; j < document.scripts.length; j++) {
                    if (document.scripts[j].src === r) {
                        return;
                    }
                }
                (k = e.createElement(t)), (a = e.getElementsByTagName(t)[0]), (k.async = 1), (k.src = r), a.parentNode.insertBefore(k, a);
            })(window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");

            ym(97216576, "init", {
                clickmap: true,
                trackLinks: true,
                accurateTrackBounce: true,
            });
        </script>
        <noscript
            ><div><img loading="lazy" decoding="async" src="https://mc.yandex.ru/watch/97216576" style="position: absolute; left: -9999px" alt="" /></div
        ></noscript>
        <!-- /Yandex.Metrika counter -->
        <script type="text/javascript">
            (function (c, l, a, r, i, t, y) {
                c[a] =
                    c[a] ||
                    function () {
                        (c[a].q = c[a].q || []).push(arguments);
                    };
                t = l.createElement(r);
                t.async = 1;
                t.src = "https://www.clarity.ms/tag/" + i;
                y = l.getElementsByTagName(r)[0];
                y.parentNode.insertBefore(t, y);
            })(window, document, "clarity", "script", "m9bf29q94p");
        </script>
    </head>
    <body>
        <noscript>
            <strong> We invite you to join our amazing platform where you can learn, grow, and network with professionals.</strong>
        </noscript>
        <div id="app"></div>
        <!-- built files will be auto injected -->
    </body>
</html>
