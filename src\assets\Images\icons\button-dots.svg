<svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_117_10378)">
<rect x="5" y="3" width="40" height="40" rx="20" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3999 21.1338C18.3732 21.1338 17.5332 21.9738 17.5332 23.0005C17.5332 24.0271 18.3732 24.8671 19.3999 24.8671C20.4265 24.8671 21.2665 24.0271 21.2665 23.0005C21.2665 21.9738 20.4265 21.1338 19.3999 21.1338ZM30.5999 21.1338C29.5732 21.1338 28.7332 21.9738 28.7332 23.0005C28.7332 24.0271 29.5732 24.8671 30.5999 24.8671C31.6265 24.8671 32.4665 24.0271 32.4665 23.0005C32.4665 21.9738 31.6265 21.1338 30.5999 21.1338ZM24.9999 21.1338C23.9732 21.1338 23.1332 21.9738 23.1332 23.0005C23.1332 24.0271 23.9732 24.8671 24.9999 24.8671C26.0265 24.8671 26.8665 24.0271 26.8665 23.0005C26.8665 21.9738 26.0265 21.1338 24.9999 21.1338Z" fill="#C3CAD9"/>
</g>
<defs>
<filter id="filter0_d_117_10378" x="0" y="0" width="50" height="50" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.14902 0 0 0 0 0.2 0 0 0 0 0.301961 0 0 0 0.03 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_117_10378"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_117_10378" result="shape"/>
</filter>
</defs>
</svg>
