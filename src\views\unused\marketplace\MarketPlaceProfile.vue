<template>
    <div class="items-center flex bg-white flex-col gap-4 justify-center py-[10rem] px-[6rem] w-full">
        <div class="text-sm font-light text-[#3361FF] w-full flex items-center justify-between">
            <font-awesome-icon class="w-[20px] h-[20px]" :icon="['fas', 'arrow-left']" />
            <span class=""
                >{{ $t("Open profile in a new window") }}
                <font-awesome-icon class="w-[15px] ml-2 h-[15px]" :icon="['fas', 'arrow-up-right-from-square']" />
            </span>
        </div>
        <section class="flex border border-[1px] border-[#C3CAD9] flex-col w-full mt-[3rem] items-center">
            <div class="relative w-full flex items-center justify-center">
                <div class="absolute flex items-center text-[#3361FF] right-[2%] top-4 gap-5">
                    <font-awesome-icon class="w-[25px] h-[25px]" :icon="['far', 'heart']" />
                    <font-awesome-icon class="w-[25px] h-[25px]" :icon="['fas', 'ellipsis']" />
                </div>
                <div class="w-1/4 flex items-center justify-center">
                    <div class="relative rounded-full border-4 border border-[#05CD99] h-[70%] w-[70%]">
                        <img class="w-full h-full rounded-full object-contain" src="@/assets/abiola.png" alt="" />
                        <div
                            class="absolute rounded-full text-sm shadow-md font-black text-[#05CD99] left-0 top-0 border-2 border flex items-center justify-center bg-white border-[#05CD99] h-[25%] w-[25%]"
                        >
                            84%
                        </div>
                    </div>
                </div>
                <div class="w-3/4 py-[3rem] pr-[2%] flex flex-col items-start gap-3">
                    <span class="text-4xl font-bold text-[#181818]">Mahmoud A.</span>
                    <p class="font-light flex items-center text-gray/700">
                        <font-awesome-icon class="w-[20px] mr-2 h-[20px]" :icon="['fas', 'location-arrow']" />
                        {{ $t("Blida, Algeria - 7:22 PM Local time") }}
                    </p>
                    <span class="font-semibold mt-5 rounded-2xl px-3 border border-[1.5px] border-[#3361FF] text-[#3361FF]">
                        <font-awesome-icon class="w-[15px] h-[15px]" :icon="['fas', 'bolt']" />
                        {{ $t("Available Now") }}
                    </span>
                    <span class="text-right w-full text-sm text-[#3361FF]"> {{ $t("Share ") }}<font-awesome-icon class="w-[16px] ml-1 h-[16px]" :icon="['fas', 'arrow-up-from-bracket']" /> </span>
                    <div class="text-sm font-bold flex items-center justify-start gap-9">
                        <div class="flex items-center gap-2">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M18.4832 16.7671C17.1164 17.0953 15.6895 17.0794 14.3303 16.7208C12.9712 16.3622 11.7221 15.6721 10.6951 14.7124C9.66804 13.7526 8.89506 12.5531 8.44535 11.2213C7.99564 9.88951 7.88325 8.46696 8.11824 7.08105C8.0825 7.11557 8.04477 7.14796 8.00524 7.17805C7.72523 7.39105 7.37524 7.47005 6.67524 7.62805L6.04024 7.77205C3.58024 8.32905 2.35024 8.60706 2.05723 9.54806C1.76523 10.4881 2.60324 11.4691 4.28024 13.4301L4.71424 13.9371C5.19024 14.4941 5.42923 14.7731 5.53623 15.1171C5.64323 15.4621 5.60723 15.8341 5.53523 16.5771L5.46923 17.2541C5.21623 19.8711 5.08924 21.1791 5.85524 21.7601C6.62123 22.3421 7.77323 21.8121 10.0752 20.7511L10.6722 20.4771C11.3262 20.1751 11.6532 20.0251 12.0002 20.0251C12.3472 20.0251 12.6742 20.1751 13.3292 20.4771L13.9242 20.7511C16.2272 21.8111 17.3792 22.3411 18.1442 21.7611C18.9112 21.1791 18.7842 19.8711 18.5312 17.2541L18.4832 16.7671Z"
                                    fill="#05CD99"
                                />
                                <path
                                    opacity="0.5"
                                    d="M9.15465 5.408L8.82665 5.996C8.46665 6.642 8.28665 6.965 8.00665 7.178C8.04665 7.148 8.08365 7.116 8.11965 7.081C7.88461 8.46701 7.99699 9.88967 8.44675 11.2216C8.8965 12.5535 9.66959 13.7531 10.6968 14.7128C11.7239 15.6726 12.9731 16.3627 14.3324 16.7212C15.6917 17.0797 17.1188 17.0954 18.4857 16.767L18.4657 16.577C18.3947 15.834 18.3587 15.462 18.4657 15.117C18.5727 14.773 18.8107 14.494 19.2877 13.937L19.7217 13.43C21.3987 11.47 22.2367 10.489 21.9437 9.548C21.6517 8.607 20.4217 8.328 17.9617 7.772L17.3257 7.628C16.6267 7.47 16.2767 7.391 15.9957 7.178C15.7157 6.965 15.5357 6.642 15.1757 5.996L14.8487 5.408C13.5817 3.136 12.9487 2 12.0017 2C11.0547 2 10.4217 3.136 9.15465 5.408Z"
                                    fill="#05CD99"
                                />
                            </svg>
                            Label 1
                        </div>
                        <div class="flex items-center gap-2">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M18.4832 16.7671C17.1164 17.0953 15.6895 17.0794 14.3303 16.7208C12.9712 16.3622 11.7221 15.6721 10.6951 14.7124C9.66804 13.7526 8.89506 12.5531 8.44535 11.2213C7.99564 9.88951 7.88325 8.46696 8.11824 7.08105C8.0825 7.11557 8.04477 7.14796 8.00524 7.17805C7.72523 7.39105 7.37524 7.47005 6.67524 7.62805L6.04024 7.77205C3.58024 8.32905 2.35024 8.60706 2.05723 9.54806C1.76523 10.4881 2.60324 11.4691 4.28024 13.4301L4.71424 13.9371C5.19024 14.4941 5.42923 14.7731 5.53623 15.1171C5.64323 15.4621 5.60723 15.8341 5.53523 16.5771L5.46923 17.2541C5.21623 19.8711 5.08924 21.1791 5.85524 21.7601C6.62123 22.3421 7.77323 21.8121 10.0752 20.7511L10.6722 20.4771C11.3262 20.1751 11.6532 20.0251 12.0002 20.0251C12.3472 20.0251 12.6742 20.1751 13.3292 20.4771L13.9242 20.7511C16.2272 21.8111 17.3792 22.3411 18.1442 21.7611C18.9112 21.1791 18.7842 19.8711 18.5312 17.2541L18.4832 16.7671Z"
                                    fill="#FFB422"
                                />
                                <path
                                    opacity="0.5"
                                    d="M9.15465 5.408L8.82665 5.996C8.46665 6.642 8.28665 6.965 8.00665 7.178C8.04665 7.148 8.08365 7.116 8.11965 7.081C7.88461 8.46701 7.99699 9.88967 8.44675 11.2216C8.8965 12.5535 9.66959 13.7531 10.6968 14.7128C11.7239 15.6726 12.9731 16.3627 14.3324 16.7212C15.6917 17.0797 17.1188 17.0954 18.4857 16.767L18.4657 16.577C18.3947 15.834 18.3587 15.462 18.4657 15.117C18.5727 14.773 18.8107 14.494 19.2877 13.937L19.7217 13.43C21.3987 11.47 22.2367 10.489 21.9437 9.548C21.6517 8.607 20.4217 8.328 17.9617 7.772L17.3257 7.628C16.6267 7.47 16.2767 7.391 15.9957 7.178C15.7157 6.965 15.5357 6.642 15.1757 5.996L14.8487 5.408C13.5817 3.136 12.9487 2 12.0017 2C11.0547 2 10.4217 3.136 9.15465 5.408Z"
                                    fill="#FFB422"
                                />
                            </svg>
                            {{ $t(" Label 2") }}
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full border-t-[1px] border-[#C3CAD9] flex items-center">
                <div class="w-1/4 flex flex-col gap-6 p-[3rem] h-full items-start">
                    <span class="text-[#8898AA] text-left font-bold text-sm">{{ $t("LATEST EXPERIENCES") }}</span>
                    <div class="w-full flex flex-col gap-4">
                        <div class="flex w-full items-start gap-3">
                            <svg width="27" height="28" viewBox="0 0 27 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect y="0.443787" width="26.9673" height="26.9673" rx="13.4836" fill="#FFBC3F" fill-opacity="0.1" />
                                <path
                                    d="M14.7769 7.51815L16.0953 10.1767C16.2751 10.5467 16.7545 10.9018 17.159 10.9692L19.5479 11.37C21.076 11.6269 21.4356 12.7446 20.3344 13.8472L18.4767 15.7199C18.1621 16.0368 17.9898 16.6488 18.0872 17.087L18.619 19.4055C19.0385 21.2407 18.0722 21.9501 16.4616 20.9913L14.2219 19.6542C13.8174 19.413 13.1507 19.413 12.7387 19.6542L10.5004 20.9913C8.89732 21.9501 7.92351 21.2325 8.343 19.4055L8.87485 17.087C8.97223 16.6488 8.79994 16.0368 8.48532 15.7199L6.62758 13.8472C5.53466 12.7438 5.88673 11.6269 7.41412 11.37L9.80372 10.9692C10.2007 10.9018 10.6802 10.5467 10.8599 10.1767L12.1783 7.51815C12.8975 6.07615 14.0661 6.07615 14.7777 7.51815"
                                    stroke="#FBBC05"
                                    stroke-width="1.12364"
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                />
                            </svg>
                            <div class="flex flex-col items-start text-left justify-start gap-1">
                                <span class="text-sm font-semibold">{{ $t("Software Engineer") }}</span>
                                <span class="text-xs text-[#8898AA] font-semibold">{{ $t("Jun 2020 - February 2021") }}</span>
                            </div>
                        </div>
                        <div class="flex w-full items-start gap-3">
                            <svg width="27" height="28" viewBox="0 0 27 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect y="0.596375" width="26.9673" height="26.9673" rx="13.4836" fill="#5D5FEF" fill-opacity="0.1" />
                                <g clip-path="url(#clip0_1_47977)">
                                    <path
                                        fill-rule="evenodd"
                                        clip-rule="evenodd"
                                        d="M19.5706 8.46173H14.4206L14.0929 8.60219L13.4842 9.20146L12.8756 8.60219L12.5479 8.46173H7.39787L6.92969 8.92991V18.2935L7.39787 18.7617H12.3512L13.1565 19.5576H13.812L14.6172 18.7617H19.5706L20.0388 18.2935V8.92991L19.5706 8.46173ZM13.0161 18.125L12.8475 17.9658L12.5479 17.8254H7.86605V9.39809H12.3512L13.0441 10.091L13.0161 18.125ZM19.1024 17.8254H14.4206L14.0929 17.9658L13.9618 18.0875V10.0535L14.6172 9.39809H19.1024V17.8254ZM11.6115 11.2708H8.80241V12.2072H11.6115V11.2708ZM11.6115 15.0163H8.80241V15.9526H11.6115V15.0163ZM8.80241 13.1435H11.6115V14.0799H8.80241V13.1435ZM18.1661 11.2708H15.357V12.2072H18.1661V11.2708ZM15.357 13.1435H18.1661V14.0799H15.357V13.1435ZM15.357 15.0163H18.1661V15.9526H15.357V15.0163Z"
                                        fill="#5D5FEF"
                                    />
                                </g>
                                <defs>
                                    <clipPath id="clip0_1_47977">
                                        <rect width="14.9818" height="14.9818" fill="white" transform="translate(5.99219 6.58905)" />
                                    </clipPath>
                                </defs>
                            </svg>
                            <div class="flex flex-col text-left items-start gap-1">
                                <span class="text-sm font-semibold">{{ $t("Bachelor in Engineering") }}</span>
                                <span class="text-xs text-[#8898AA] font-semibold">{{ $t("Sep 2017 - May 2020") }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-3/4 flex flex-col gap-5 p-[3rem]">
                    <div class="flex items-center w-full justify-between">
                        <span class="text-md font-semibold">{{ $t("Software Engineer") }}</span>
                        <span class="text-xs font-light px-2 py-1 rounded-2xl bg-[#C3CAD9]"
                            ><span class="font-bold">{{ $t("Skill Match 22") }} </span>{{ $t(" out of") }} <span class="font-bold">27</span></span
                        >
                        <div class="w-[15%] bg-[#DADCFF] overflow-hidden h-[15px]">
                            <div class="h-full bg-[#3361FF]" :style="{ width: `${score}%` }"></div>
                        </div>
                    </div>
                    <div class="w-full text-left">
                        <span class="text-sm">Accounting - </span><span class="text-sm">{{ $t("Problem Solving - ") }}</span
                        ><span class="text-sm">{{ $t("Decision Making -") }} </span><span class="text-sm">T-sql - </span><span class="text-sm">Javascript</span>
                    </div>
                    <div class="flex border-y-[1px] py-3 border-[#E0E4EC] items-center justify-between w-full">
                        <div class="flex items-center gap-2">
                            <span class="font-semibold rounded-2xl text-xs px-2 py-1 bg-[#C3CAD9]">10</span>
                            <span class="font-semibold text-sm">{{ $t("Skills to improve") }}</span>
                        </div>
                        <button>
                            <font-awesome-icon :icon="['fas', 'chevron-down']" />
                        </button>
                    </div>
                    <div class="w-full">
                        <table class="w-full text-sm">
                            <thead>
                                <tr>
                                    <th class="py-2 text-left font-semibold">{{ $t("Similar Matches") }}</th>
                                    <th class="py-2 text-left font-semibold">{{ $t("Skill Match") }}</th>
                                    <th class="py-2 w-[15%]"></th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="py-2 text-left">{{ $t("Job name, company name, location") }}</td>
                                    <td class="py-2 text-left"><span class="font-bold">22</span> {{ $t("out of ") }}<span class="font-bold">26</span></td>
                                    <td class="">
                                        <div class="w-full bg-[#DADCFF] overflow-hidden h-[15px]">
                                            <div class="h-full bg-[#3361FF]" :style="{ width: `${score}%` }"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="py-2 text-left">{{ $t("Job name, company name, location") }}</td>
                                    <td class="py-2 text-left"><span class="font-bold">22</span> {{ $t("out of") }} <span class="font-bold">26</span></td>
                                    <td class="">
                                        <div class="w-full bg-[#DADCFF] overflow-hidden h-[15px]">
                                            <div class="h-full bg-[#3361FF]" :style="{ width: `${score}%` }"></div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>
        <section class="w-full flex border-[1px] border-[#C3CAD9]">
            <div class="w-[40%] flex flex-col px-[4rem] py-[3rem] gap-[2.5rem] items-start text-left border-r-[1px] border-[#C3CAD9]">
                <span class="text-[#8898AA] font-bold text-sm">{{ $t("PERSONALITY") }}</span>
                <div class="w-full h-fit flex flex-col justify-between items-center gap-5">
                    <div class="w-full flex flex-col gap-5">
                        <div v-for="(trait, index) in traitsArray" :key="index" class="flex flex-row justify-between items-end w-full">
                            <span class="text-left text-sm font-bold">{{ trait.name }}</span>
                            <div class="flex flex-col gap-3 justify-between items-center">
                                <div v-if="index === 0" class="w-full flex justify-between items-center text-slate-700 text-xs font-light">
                                    <h2>{{ $t("Very low") }}</h2>
                                    <h2>{{ $t("Very high") }}</h2>
                                </div>
                                <div class="w-full flex justify-between gap-1 items-center">
                                    <span class="w-10 h-4" :class="['Very Low', 'Low', 'Medium', 'High', 'Very High'].includes(trait.degree) ? 'bg-[#3361FF]' : 'bg-[#E1E8FF]'"></span>
                                    <span class="w-10 h-4" :class="['Low', 'Medium', 'High', 'Very High'].includes(trait.degree) ? 'bg-[#3361FF]' : 'bg-[#E1E8FF]'"></span>
                                    <span class="w-10 h-4" :class="['Medium', 'High', 'Very High'].includes(trait.degree) ? 'bg-[#3361FF]' : 'bg-[#E1E8FF]'"></span>
                                    <span class="w-10 h-4" :class="['High', 'Very High'].includes(trait.degree) ? 'bg-[#3361FF]' : 'bg-[#E1E8FF]'"></span>
                                    <span class="w-10 h-4" :class="trait.degree === 'Very High' ? 'bg-[#3361FF]' : 'bg-[#E1E8FF]'"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end mt-5 items-center w-full">
                        <button class="w-fit px-4 py-2 rounded-md text-md font-semibold bg-NeonBlue text-white">Full Report</button>
                    </div>
                </div>
            </div>
            <div class="w-[60%] flex flex-col px-[4rem] py-[3rem] gap-[2.5rem] items-start text-left">
                <span class="text-[#8898AA] font-bold text-sm">{{ $t("WORK HISTORY") }}</span>
                <div class="w-full gap-[2rem] flex flex-col items-start">
                    <div class="w-full gap-2 flex flex-col items-start">
                        <span class="text-left text-sm font-semibold">{{ $t("Technical Recruiter") }} | Altec Resource Group</span>
                        <span class="text-left text-sm text-[#5C5F62]">{{ $t("December 2022 - Present") }}</span>
                        <ul class="text-sm text-[#5C5F62]">
                            <li>{{ $t("-Hiring Top Talent for the US Federal Government") }}</li>
                            <li>{{ $t("-Using Boolean Searches to find the potential candidates over LinkedIn and Monster") }}</li>
                        </ul>
                    </div>
                    <div class="w-full gap-2 flex flex-col items-start">
                        <span class="text-left text-sm font-semibold">{{ $t("Technical Recruiter") }} | Altec Resource Group</span>
                        <span class="text-left text-sm text-[#5C5F62]">{{ $t("December 2022 - Present") }}</span>
                        <ul class="text-sm text-[#5C5F62]">
                            <li>{{ $t("-Hiring Top Talent for the US Federal Government") }}</li>
                            <li>{{ $t("-Using Boolean Searches to find the potential candidates over LinkedIn and Monster") }}</li>
                        </ul>
                    </div>
                    <div class="w-full gap-2 flex flex-col items-start">
                        <span class="text-left text-sm font-semibold">{{ $t("Technical Recruiter") }} | Altec Resource Group</span>
                        <span class="text-left text-sm text-[#5C5F62]">{{ $t("December 2022 - Present") }}</span>
                        <ul class="text-sm text-[#5C5F62]">
                            <li>{{ $t("-Hiring Top Talent for the US Federal Government") }}</li>
                            <li>{{ $t("-Using Boolean Searches to find the potential candidates over LinkedIn and Monster") }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>
        <section class="w-full flex border border-[1px] border-[#C3CAD9]">
            <div class="w-[40%] flex flex-col px-[4rem] py-[3rem] gap-4 items-start text-left border-r-[1px] border-[#C3CAD9]">
                <span class="text-[#8898AA] font-bold text-sm">{{ $t("REQUESTED COMPENSATION RATE (MONTH)") }}</span>
                <div class="flex w-full items-center justify-center">
                    <div class="w-4 h-4 bg-[#3361FF] ml-[10px] rounded-full"></div>
                    <div class="w-[40%] h-1 bg-[#3361FF]"></div>
                    <div class="w-4 h-4 bg-[#3361FF] mr-[10px] rounded-full"></div>
                </div>
                <span class="text-[#8898AA] font-bold mt-4 text-sm">{{ $t("LANGUAGES :") }}</span>
                <ul>
                    <li>{{ $t("English: Native or Bilingual") }}</li>
                    <li>{{ $t("Panjabi, Punjabi: Native or Bilingual") }}</li>
                    <li>{{ $t("Urdu: Native or Bilingual") }}</li>
                    <li>{{ $t("Hindi: Fluent") }}</li>
                </ul>
                <span class="text-[#8898AA] font-bold text-sm">{{ $t("VETTED") }}</span>
                <span class="font-bold flex items-center gap-2 text-sm"
                    >VERIFIED
                    <svg width="13" height="13" viewBox="0 0 13 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            fill-rule="evenodd"
                            clip-rule="evenodd"
                            d="M9.69882 0.549352C9.56973 0.343073 9.37955 0.182175 9.15473 0.0890572C8.92992 -0.0040611 8.68167 -0.0247674 8.44454 0.0298212L6.76156 0.416427C6.58908 0.456072 6.40986 0.456072 6.23738 0.416427L4.5544 0.0298212C4.31727 -0.0247674 4.06902 -0.0040611 3.84421 0.0890572C3.6194 0.182175 3.42921 0.343073 3.30012 0.549352L2.38281 2.0134C2.28921 2.16317 2.16284 2.28955 2.01308 2.38409L0.549128 3.30146C0.343218 3.43044 0.182567 3.62034 0.0894753 3.84478C-0.00361685 4.06922 -0.0245326 4.31708 0.0296313 4.55395L0.416212 6.23891C0.455711 6.4111 0.455711 6.59 0.416212 6.76219L0.0296313 8.44621C-0.0247431 8.68322 -0.00393262 8.93128 0.0891696 9.15592C0.182272 9.38055 0.34304 9.5706 0.549128 9.69964L2.01308 10.617C2.16284 10.7106 2.28921 10.837 2.38375 10.9868L3.30106 12.4508C3.56502 12.873 4.0686 13.0817 4.5544 12.9703L6.23738 12.5837C6.40986 12.5441 6.58908 12.5441 6.76156 12.5837L8.44548 12.9703C8.68247 13.0247 8.93052 13.0039 9.15514 12.9108C9.37976 12.8177 9.56979 12.6569 9.69882 12.4508L10.6161 10.9868C10.7097 10.837 10.8361 10.7106 10.9859 10.617L12.4508 9.69964C12.6568 9.57041 12.8175 9.38015 12.9105 9.15534C13.0034 8.93052 13.024 8.68233 12.9693 8.44528L12.5837 6.76219C12.544 6.5897 12.544 6.41046 12.5837 6.23798L12.9703 4.55395C13.0247 4.31704 13.004 4.06905 12.9111 3.84442C12.8182 3.6198 12.6576 3.4297 12.4517 3.30052L10.9868 2.38315C10.8372 2.28937 10.7108 2.16296 10.6171 2.0134L9.69882 0.549352ZM9.228 4.4126C9.2859 4.30613 9.30024 4.18136 9.26802 4.06453C9.23579 3.94771 9.15951 3.84793 9.05523 3.78621C8.95094 3.72448 8.82678 3.70561 8.70887 3.73358C8.59096 3.76154 8.48849 3.83415 8.42302 3.93613L5.9753 8.07927L4.4973 6.6639C4.45346 6.61887 4.40099 6.58314 4.34304 6.55883C4.28508 6.53452 4.22283 6.52214 4.15998 6.52241C4.09714 6.52269 4.03499 6.53562 3.97725 6.56043C3.91951 6.58524 3.86736 6.62143 3.82391 6.66683C3.78045 6.71224 3.74659 6.76593 3.72434 6.82471C3.70208 6.88348 3.69189 6.94614 3.69437 7.00894C3.69686 7.07174 3.71196 7.1334 3.73878 7.19023C3.76561 7.24707 3.80361 7.29792 3.85051 7.33976L5.75439 9.1642C5.80535 9.21292 5.86665 9.24951 5.93373 9.27122C6.00081 9.29292 6.07192 9.2992 6.14176 9.28957C6.21161 9.27994 6.27837 9.25465 6.33707 9.2156C6.39577 9.17655 6.44488 9.12473 6.48075 9.06403L9.228 4.4126Z"
                            fill="#3361FF"
                        />
                    </svg>
                </span>
            </div>
            <div class="w-[60%] flex flex-col px-[4rem] py-[3rem] gap-[3rem] items-start text-left">
                <span class="text-[#8898AA] font-bold text-sm">{{ $t("PORTFOLIO") }}</span>
                <div class="w-full flex mt-2 items-center gap-3 justify-center">
                    <div class="flex w-[49%] flex-col items-center justify-center gap-4">
                        <div class="border-[1px] border-[#C3CAD9] w-full overflow-hidden rounded-md h-[10rem]">
                            <img class="w-full h-full" src="@/assets/portfolio_1.png" alt="" />
                        </div>
                        <span class="font-semibold text-sm text-[#3361FF]">{{ $t("Website Frontend") }}</span>
                    </div>

                    <div class="flex w-[49%] flex-col items-center justify-center gap-4">
                        <div class="border-[1px] border-[#C3CAD9] w-full overflow-hidden rounded-md h-[10rem]">
                            <img class="w-full h-full" src="@/assets/portfolio_1.png" alt="" />
                        </div>
                        <span class="font-semibold text-sm text-[#3361FF]">{{ $t("Website Frontend") }}</span>
                    </div>
                </div>
                <div class="flex items-center justify-center w-full gap-5">
                    <font-awesome-icon class="text-[#8898AA]" :icon="['fas', 'chevron-left']" />
                    <span class="font-semibold text-xs text-[#8898AA]">{{ $t("Previous") }}</span>
                    <span class="font-semibold text-xs text-white rounded-full flex items-center justify-center w-5 h-5 bg-[#3361FF]">1</span>
                    <span class="font-semibold text-xs text-[#3361FF]">2</span>
                    <span class="font-semibold text-xs text-[#3361FF]">3</span>
                    <span class="font-semibold text-xs text-[#3361FF]">{{ $t("Next") }}</span>
                    <font-awesome-icon class="text-[#3361FF]" :icon="['fas', 'chevron-right']" />
                </div>
            </div>
        </section>
    </div>
</template>

<script>
export default {
    name: "MarketPlaceProfile",
    data() {
        return {
            score: 60,
            traitsArray: [
                { name: "Openness", degree: "Very Low" },
                { name: "Conscientiousness", degree: "Very High" },
                { name: "Extraversion", degree: "Low" },
                { name: "Agreeableness", degree: "High" },
                { name: "Neuroticism", degree: "Very Low" },
            ],
        };
    },
};
</script>

<style scoped></style>
