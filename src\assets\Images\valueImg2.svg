<svg width="409" height="304" viewBox="0 0 409 304" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="64.3324" y="51.3226" width="272.583" height="200.135" stroke="#C6C6C6" stroke-width="2.11792" stroke-linejoin="round" stroke-dasharray="3.18 3.18"/>
<rect x="0.414062" y="0.958984" width="284.077" height="214.507" fill="#F1F1F1"/>
<rect x="7.375" y="32.2637" width="270.163" height="178.563" fill="white"/>
<rect x="29.4062" y="55.457" width="223.783" height="129.864" rx="8.47168" fill="#F1F1F1"/>
<rect x="258.984" y="14.873" width="17.3925" height="4.63799" rx="2.31899" fill="#C4C4C4"/>
<rect x="46.1641" y="96.5508" width="83.5172" height="5.03116" rx="2.51558" fill="white"/>
<rect x="46.1641" y="146.861" width="83.5172" height="5.03116" rx="2.51558" fill="white"/>
<rect x="146.789" y="96.5508" width="83.5172" height="5.03116" rx="2.51558" fill="#5236FF"/>
<rect x="46.1641" y="88.502" width="83.5172" height="5.03116" rx="2.51558" fill="white"/>
<rect x="46.1641" y="138.812" width="83.5172" height="5.03116" rx="2.51558" fill="#FFB422"/>
<rect x="146.789" y="88.502" width="83.5172" height="5.03116" rx="2.51558" fill="white"/>
<rect x="46.1641" y="104.6" width="83.5172" height="5.03116" rx="2.51558" fill="white"/>
<rect x="46.1641" y="154.912" width="83.5172" height="5.03116" rx="2.51558" fill="#FFB422"/>
<rect x="146.789" y="104.6" width="83.5172" height="5.03116" rx="2.51558" fill="white"/>
<rect x="46.1641" y="112.65" width="47.2929" height="5.03116" rx="2.51558" fill="#5236FF"/>
<rect x="46.1641" y="162.963" width="47.2929" height="5.03116" rx="2.51558" fill="white"/>
<rect x="146.789" y="112.65" width="47.2929" height="5.03116" rx="2.51558" fill="white"/>
<circle cx="10.2737" cy="16.6136" r="2.89874" fill="#C4C4C4"/>
<circle cx="18.3831" cy="16.6136" r="2.89874" fill="#C4C4C4"/>
<circle cx="26.5003" cy="16.6136" r="2.89874" fill="#C4C4C4"/>
<g filter="url(#filter0_d_5527_25326)">
<rect x="169.93" y="127.742" width="210.609" height="142.618" rx="15.8844" fill="white"/>
</g>
<line x1="213.992" y1="199.67" x2="339.984" y2="199.67" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="212.188" y1="224.01" x2="338.179" y2="224.01" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="212.188" y1="187.5" x2="338.179" y2="187.5" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="212.188" y1="211.84" x2="338.179" y2="211.84" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="212.188" y1="175.332" x2="338.179" y2="175.332" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="221.717" y1="163.693" x2="221.717" y2="231.385" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="264.912" y1="163.693" x2="264.912" y2="231.385" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="308.115" y1="163.693" x2="308.115" y2="231.385" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="236.115" y1="162.932" x2="236.115" y2="230.623" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="279.319" y1="162.932" x2="279.319" y2="230.623" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="322.506" y1="162.932" x2="322.506" y2="230.623" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="250.514" y1="162.932" x2="250.514" y2="230.623" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="293.709" y1="162.932" x2="293.709" y2="230.623" stroke="#F1F1F1" stroke-width="1.05896"/>
<line x1="336.904" y1="162.932" x2="336.904" y2="230.623" stroke="#F1F1F1" stroke-width="1.05896"/>
<rect x="196.094" y="164.973" width="6.03739" height="3.01869" rx="1.50935" fill="#E0DBFF"/>
<rect x="197.102" y="225.346" width="6.03739" height="3.01869" rx="1.50935" fill="#E0DBFF"/>
<rect x="196.094" y="195.16" width="6.03739" height="3.01869" rx="1.50935" fill="#E0DBFF"/>
<rect x="209.172" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 209.172 249.496)" fill="#E0DBFF"/>
<rect x="239.359" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 239.359 249.496)" fill="#E0DBFF"/>
<rect x="269.555" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 269.555 249.496)" fill="#E0DBFF"/>
<rect x="299.742" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 299.742 249.496)" fill="#E0DBFF"/>
<rect x="329.922" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 329.922 249.496)" fill="#E0DBFF"/>
<rect x="196.094" y="180.066" width="6.03739" height="3.01869" rx="1.50935" fill="#E0DBFF"/>
<rect x="197.102" y="240.439" width="6.03739" height="3.01869" rx="1.50935" fill="#E0DBFF"/>
<rect x="196.094" y="210.254" width="6.03739" height="3.01869" rx="1.50935" fill="#E0DBFF"/>
<rect x="224.273" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 224.273 249.496)" fill="#E0DBFF"/>
<rect x="254.453" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 254.453 249.496)" fill="#E0DBFF"/>
<rect x="284.641" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 284.641 249.496)" fill="#E0DBFF"/>
<rect x="314.828" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 314.828 249.496)" fill="#E0DBFF"/>
<rect x="345.016" y="249.496" width="6.03739" height="3.01869" rx="1.50935" transform="rotate(-90 345.016 249.496)" fill="#E0DBFF"/>
<path d="M279.458 206.732C243.62 193.87 220.051 219.284 211.934 234.916C211.575 235.606 212.086 236.416 212.864 236.416H339.932C340.517 236.416 340.991 235.942 340.991 235.357V167.123C340.991 165.864 338.84 165.563 338.43 166.754C330.812 188.897 311.879 218.367 279.458 206.732Z" fill="#FFB422"/>
<path d="M201.117 156.924V246.478H351.549" stroke="#E0DBFF" stroke-width="3.17688"/>
<defs>
<filter id="filter0_d_5527_25326" x="152.986" y="116.094" width="255.086" height="187.095" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="5.2948" dy="10.5896"/>
<feGaussianBlur stdDeviation="11.1191"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.700272 0 0 0 0 0.700272 0 0 0 0 0.700272 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5527_25326"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5527_25326" result="shape"/>
</filter>
</defs>
</svg>
