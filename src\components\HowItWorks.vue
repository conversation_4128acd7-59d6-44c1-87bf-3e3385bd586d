<template>
    <!-- Third Section -->
    <div class="w-full py-16 overflow-x-hidden">
        <div class="px-6 max-w-7xl mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-8">{{ $t("How it works") }}</h2>
            <div class="flex flex-col lg:flex-row gap-12">
                <!-- Left Content -->
                <div class="lg:w-1/2 text-left">
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-800 mb-8">
                        {{ $t("Hire talents from Africa in") }}
                        <span class="relative inline-block">
                            <div class="absolute inset-0 bg-[#cbe1ef] -rotate-3"></div>
                            <span class="relative z-10"> {{ $t("72h only.") }}</span>
                        </span>
                    </h1>

                    <div class="space-y-8">
                        <div v-for="(step, index) in steps" :key="index" class="group">
                            <h3 class="text-[#2196f3] text-xl font-semibold mb-2">
                                <span class="text-2xl font-bold">0{{ index + 1 }} </span>
                                <span> </span>
                                {{ $t(step.title) }}
                            </h3>
                            <p class="text-gray-600">{{ $t(step.description) }}</p>
                        </div>
                    </div>
                </div>

                <!-- Right Image -->
                <div class="w-full lg:w-1/2 flex justify-center items-center relative px-3 sm:px-0">
                    <div class="relative w-full max-w-lg xl:max-w-2xl">
                        <!-- Africa Illustration -->
                        <img src="@/assets/landing-page-logos/africa-2.svg" alt="Africa illustration" class="w-full max-w-[600px] h-auto object-contain mx-auto" />

                        <!-- Experience Badge - Now perfectly aligned to SVG's top-left corner -->
                        <div class="absolute top-0 md:right-8 bg-[#51B0E7] text-white rounded-full w-24 h-24 md:w-32 md:h-32 flex flex-col items-center justify-center p-5">
                            <span class="font-bold text-xl md:text-2xl">32+</span>
                            <span class="text-sm text-center">{{ $t("Years of experience") }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "HowItWorks",
    data() {
        return {
            steps: [
                {
                    title: "Cost Efficient Talents",
                    description: "Reduce development expenses by at least 75% with African talents",
                },
                {
                    title: "Vetted Talents",
                    description: "Pre-screened professionals with verified skills and background",
                },
                {
                    title: "30 Days Guarantee",
                    description: "Free replacement during the first month trial period",
                },
            ],
        };
    },
};
</script>
