<template>
    <div class="fixed inset-0 z-[100] bg-[#f8f8fa] flex items-center justify-center">
        <!-- Modal -->
        <div v-if="isVisible" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-[400] flex items-center justify-center p-4 sm:p-6">
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-md p-6 flex flex-col gap-6 animate-in fade-in-0 zoom-in-95 duration-300">
                <div class="flex items-center gap-4">
                    <div class="flex-shrink-0 flex items-center justify-center w-12 h-12 rounded-full bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h1 class="text-xl font-semibold text-gray-800">{{ message }}</h1>
                </div>
                <div class="flex justify-end items-center">
                    <ButtonComponent
                        :action="
                            () => {
                                this.isVisible = false;
                                this.message = '';
                            }
                        "
                        intent="primary"
                        class="px-6 py-2.5"
                    >
                        {{ $t("Close") }}
                    </ButtonComponent>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="bg-white rounded-md w-full h-full flex">
            <div v-if="pass" class="flex flex-1 h-full">
                <!-- Enhanced Left Section -->
                <div class="hidden lg:block lg:w-1/2 h-full relative bg-NeonBlue">
                    <img src="@/assets/Start-Assessment.png" class="w-full h-full object-cover mix-blend-multiply opacity-90" alt="Career growth illustration" />
                </div>

                <!-- Right Section (Form) -->
                <div class="w-full lg:w-1/2 flex flex-col items-center justify-center px-4 lg:px-20">
                    <div class="w-full max-w-[420px]">
                        <!-- Form Header -->
                        <div class="mb-8 lg:mb-10 space-y-1">
                            <img :src="logoSrc" :alt="logoAlt" class="h-12 mb-12 mx-auto" />
                            <h2 class="text-lg md:text-2xl font-bold text-gray-900 text-center">{{ $t("Start Your Assessment Now!") }}</h2>
                            <p class="text-gray-500 text-base text-center">{{ $t("Enter your email to get started") }}</p>
                        </div>

                        <!-- Form Elements -->
                        <div class="space-y-8">
                            <!-- Email Input -->
                            <div class="space-y-4">
                                <div class="relative">
                                    <svg class="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <input
                                        id="log_email"
                                        v-model="email"
                                        type="email"
                                        required
                                        class="w-full px-5 py-3 pl-10 border border-gray-200 rounded-md outline-none border-gray-200 focus:border-2 focus:border-NeonBlue focus:border-blue-500"
                                        placeholder="Email"
                                    />
                                </div>
                                <span v-if="emailError" class="block text-sm text-red-600 -mt-2">{{ emailError }}</span>
                            </div>

                            <!-- Terms & Privacy -->
                            <div class="space-y-2">
                                <p class="text-sm text-gray-500 text-center">
                                    {{ $t("By continuing, you agree to our") }}
                                    <a href="/Terms-of-use" class="text-blue-600 hover:underline font-medium">{{ $t("Terms of Service") }}</a> &
                                    <a href="/privacy-policy" class="text-blue-600 hover:underline font-medium">{{ $t("Privacy Policy") }}</a>
                                </p>
                            </div>

                            <!-- CTA Button -->
                            <ButtonComponent
                                :loading="loading"
                                :disabled="isButtonDisabled"
                                :action="check"
                                class="w-full py-3 text-base font-semibold text-white bg-NeonBlue hover:opacity-85 rounded-md transition-all"
                            >
                                {{ $t("Start Assessment") }}
                            </ButtonComponent>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Success State -->
            <div v-else class="w-full h-full flex flex-col items-center justify-center bg-gray-50">
                <div class="max-w-2xl text-center space-y-8">
                    <div class="inline-block p-6 bg-green-100 rounded-full">
                        <svg class="w-20 h-20 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h2 class="text-4xl font-bold text-gray-900">{{ $t("Congratulations!") }}</h2>
                    <p class="text-xl text-gray-600 px-16">{{ $t("You're now ready to begin your assessment. Show us what you've got!") }}</p>
                    <ButtonComponent
                        :href="link"
                        :action="check"
                        class="mt-8 px-12 py-4 text-lg font-semibold text-white bg-blue-600 hover:bg-blue-700 rounded-xl shadow-lg hover:shadow-xl transition-all"
                    >
                        {{ $t("Begin Assessment") }}
                    </ButtonComponent>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ButtonComponent from "@/components/ReusableComponents/ButtonComponent.vue";
import { useGeolocationStore } from "@/store/geolocation";
import { BASE_URL } from "@/constants";
import { computed } from "vue";

import axios from "axios";
export default {
    name: "InviteC",
    components: { ButtonComponent },
    data() {
        return {
            emailError: "",
            pass: true,
            //isButtonDisabled: false,
            token: null,
            email: "",
            link: "",
            company: "",
            project: "",
            message: "",
            isVisible: false,
            loading: false,
        };
    },
    setup() {
        const geolocationStore = useGeolocationStore();

        const logoSrc = computed(() => {
            return geolocationStore.country === "DZ" ? require("@/assets/GoProfiling-logo.svg") : require("@/assets/Images/go_logo.svg");
        });

        const logoAlt = computed(() => {
            return geolocationStore.country === "DZ" ? "Go Profiling & Testing Logo" : "Go Platform Logo";
        });

        return { logoSrc, logoAlt };
    },
    computed: {
        // Disable button if email is empty or checkbox is not checked
        isButtonDisabled() {
            return !this.email;
        },
    },
    mounted() {
        /*
        this.$recaptcha.init({
      siteKey: 'your-site-key',
    })*/
    },
    methods: {
        check() {
            let isValid = true;
            const emailRegex = /^\S+@\S+\.\S+$/; // Regular expression for basic email format
            this.isButtonDisabled = true;
            if (!emailRegex.test(this.email)) {
                this.emailError = "Please enter a valid email address.";
                isValid = false; // Update the formValid flag

                this.isButtonDisabled = false;
            } else {
                this.emailError = "";
                this.pass = true;
                isValid = true;
                this.isButtonDisabled = true;
            }
            this.company = this.$route.query.company;
            this.project = this.$route.query.project;
            if (isValid) {
                this.isButtonDisabled = true;
                this.loading = true;
                axios
                    .post(
                        `${BASE_URL}/inviteCandidate/self-invite?companyName=${this.company}&project=${this.project}`,
                        { email: this.email },
                        {
                            withCredentials: true,
                        },
                    )
                    .then((response) => {
                        this.loading = false;
                        this.message = response.data.message;
                        this.isVisible = response.data.success;
                    })
                    .catch((error) => {
                        console.log(error);
                        this.pass = true;
                        this.loading = false;
                        this.isButtonDisabled = false;
                    });
            }
        },
    },
};
</script>
