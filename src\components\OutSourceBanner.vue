<template>
    <div class="mt-[30vh] mb-16">
        <div class="flex flex-col md:flex-row items-center justify-between px-6 max-w-7xl mx-auto gap-12">
            <!-- Text Content -->
            <div class="text-center md:text-left md:w-1/2 space-y-8 animate-fade-in-up">
                <h1 class="text-5xl sm:text-6xl md:text-7xl text-gray-900 font-bold leading-tight">
                    {{ $t("Hire Now, Pay") }}
                    <span class="relative inline-block whitespace-nowrap text-[#2196f3]">
                        <span class="relative z-10">{{ $t("Later.") }}</span>
                    </span>
                </h1>

                <p class="text-xl text-gray-600 max-w-lg mx-auto md:mx-0 leading-relaxed">
                    {{ $t("Access top-tier talents with flexible payment options.Get started today") }}
                </p>

                <div class="flex flex-col sm:flex-row gap-4 justify-center md:justify-start">
                    <router-link
                        to="/request-service"
                        class="group relative px-8 py-4 bg-[#2196f3] from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white font-semibold rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 hover:scale-105"
                    >
                        <span class="relative z-10">{{ $t("Hire Top Talents") }}</span>
                        <div class="absolute inset-0 bg-blue-700 opacity-0 group-hover:opacity-10 rounded-lg transition-opacity duration-300"></div>
                    </router-link>
                </div>

                <div class="flex flex-col items-center md:items-start gap-2 text-gray-500 mt-6">
                    <div class="flex items-center gap-3">
                        <div class="flex -space-x-3"></div>
                        <div class="text-left">
                            <p class="text-sm font-medium">{{ $t("Trusted by 100+ Companies") }}</p>
                            <div class="flex items-center gap-1 mt-1">
                                <svg class="w-4 h-4 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path
                                        d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                                    />
                                </svg>
                                <span class="text-xs">{{ $t("5 (100+ reviews)") }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Image/Illustration -->
            <div class="md:w-1/2 mt-8 md:mt-0 relative animate-fade-in-right">
                <div class="relative w-full max-w-lg mx-auto">
                    <div class="absolute -top-8 -left-8 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob"></div>
                    <div class="absolute -bottom-12 -right-8 w-64 h-64 bg-blue-400 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-2000"></div>
                    <div class="absolute top-20 -right-8 w-64 h-64 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-blob animation-delay-4000"></div>

                    <div class="relative bg-white p-1.5 rounded-2xl shadow-2xl border border-gray-100 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:scale-[1.02]">
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/20 to-blue-100/10 z-0"></div>
                        <img class="w-full h-auto rounded-xl relative z-10" src="@/assets/background/bg.png" alt="Team working together" />
                    </div>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="w-full py-20 px-6 mt-8">
            <div class="relative max-w-7xl mx-auto bg-white rounded-2xl border overflow-hidden animate-fade-in">
                <!-- Accent line -->
                <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-[#2c9bf4] via-[#6FCBE9] to-[#21cbf3]"></div>
                <div class="grid lg:grid-cols-2 gap-0">
                    <!-- Left Content -->
                    <div class="p-6 lg:p-8 flex flex-col justify-center">
                        <div class="mb-4 flex items-center gap-3">
                            <div class="w-12 h-12 rounded-full bg-[#e3f0ff] flex items-center justify-center">
                                <svg class="w-7 h-7 text-[#2c9bf4]" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12c0 4.97-4.03 9-9 9s-9-4.03-9-9 4.03-9 9-9 9 4.03 9 9z" />
                                </svg>
                            </div>
                            <span class="text-[#2c9bf4] font-semibold text-lg">{{ $t("Hassle-Free Hiring") }}</span>
                        </div>
                        <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-2 leading-tight">
                            {{ $t("Scale Your Team with") }} <span class="text-[#2c9bf4]">{{ $t("Top-Tier Talent") }}</span>
                        </h2>
                        <p class="text-lg text-gray-600 mb-4 leading-relaxed">
                            {{ $t("Connect with professionals who can drive your business forward.") }}
                        </p>
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center gap-3">
                                <div class="w-5 h-5 bg-[#d1fae5] rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-3 h-3 text-[#10b981]" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </div>
                                <span class="text-gray-700 font-medium">{{ $t("Average placement within 2 weeks") }}</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-5 h-5 bg-[#d1fae5] rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-3 h-3 text-[#10b981]" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </div>
                                <span class="text-gray-700 font-medium">{{ $t("98% client satisfaction rate") }}</span>
                            </div>
                            <div class="flex items-center gap-3">
                                <div class="w-5 h-5 bg-[#d1fae5] rounded-full flex items-center justify-center flex-shrink-0">
                                    <svg class="w-3 h-3 text-[#10b981]" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            fill-rule="evenodd"
                                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                            clip-rule="evenodd"
                                        />
                                    </svg>
                                </div>
                                <span class="text-gray-700 font-medium">{{ $t("Risk-free 30-day guarantee") }}</span>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3">
                            <a
                                href="https://calendly.com/aouf-abdellah/20min"
                                class="inline-flex items-center justify-center px-8 py-3 bg-gradient-to-r hover:from-[#2196f3] hover:to-[#21cbf3] from-[#21cbf3] to-[#2196f3] text-white font-semibold rounded-lg transition-all duration-500 ease-in-out focus:outline-none focus:ring-4 focus:ring-blue-200 focus:ring-offset-2 shadow-md hover:shadow-md"
                            >
                                {{ $t("Schedule Consultation") }}
                                <svg class="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                            </a>
                        </div>
                        <div class="mt-4 pt-3 border-t border-gray-100">
                            <p class="text-sm text-gray-500 mb-1">{{ $t("Questions? Speak with our team") }}</p>
                            <div class="flex items-center gap-4 text-sm">
                                <a class="flex items-center gap-2 text-[#2196f3] hover:text-[#21cbf3] transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                                        />
                                    </svg>
                                    +1 (413) 772 9811
                                </a>
                                <a class="flex items-center gap-2 text-[#2196f3] hover:text-[#21cbf3] transition-colors">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path
                                            stroke-linecap="round"
                                            stroke-linejoin="round"
                                            stroke-width="2"
                                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                                        />
                                    </svg>
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Right Stats/Testimonial Section -->
                    <div class="bg-[#f5faff] p-6 lg:p-8 flex flex-col justify-center">
                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-3xl font-bold text-[#21cbf3] mb-1">100+</div>
                                <div class="text-sm text-gray-600 font-medium">{{ $t("Companies Served") }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-[#21cbf3] mb-1">1k+</div>
                                <div class="text-sm text-gray-600 font-medium">{{ $t("Successful Placements") }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-[#21cbf3] mb-1">98%</div>
                                <div class="text-sm text-gray-600 font-medium">{{ $t("Satisfaction Rate") }}</div>
                            </div>
                            <div class="text-center">
                                <div class="text-3xl font-bold text-[#21cbf3] mb-1">14</div>
                                <div class="text-sm text-gray-600 font-medium">{{ $t("Days Avg. Hire") }}</div>
                            </div>
                        </div>
                        <div class="bg-white p-4 rounded-lg shadow-md border border-gray-100">
                            <div class="flex items-center mb-2">
                                <div class="flex text-yellow-400">
                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                                        />
                                    </svg>
                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                                        />
                                    </svg>
                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                                        />
                                    </svg>
                                    <svg class="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                        <path
                                            d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                                        />
                                    </svg>
                                </div>
                            </div>
                            <blockquote class="text-gray-700 text-sm mb-2 italic">
                                {{ $t("“We use Go Platform and we love Go Platform!”") }}
                            </blockquote>
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-300 rounded-full mr-3 bg-cover bg-center" :style="{ backgroundImage: `url(${require('@/assets/Images/Esteban-Salsano.jpg')})` }"></div>
                                <div>
                                    <div class="font-semibold text-sm text-gray-900">Esteban Salsano</div>
                                    <div class="text-xs text-gray-600">CEO at Odev.tech</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
@keyframes blob {
    0%,
    100% {
        transform: translate(0, 0) scale(1);
    }

    33% {
        transform: translate(30px, -50px) scale(1.1);
    }

    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
}

.animate-blob {
    animation: blob 7s infinite ease-in-out;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }

    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse-slow {
    0%,
    100% {
        opacity: 0.3;
        transform: scale(1);
    }

    50% {
        opacity: 0.5;
        transform: scale(1.05);
    }
}

/* Subtle fade-in animation */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fade-in 0.6s ease-out forwards;
}

/* Professional hover effects */
.hover\:shadow-md:hover {
    box-shadow:
        0 4px 6px -1px rgba(0, 0, 0, 0.1),
        0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Focus styles for accessibility */
.focus\:ring-4:focus {
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.focus\:ring-gray-200:focus {
    box-shadow: 0 0 0 4px rgba(229, 231, 235, 0.5);
}
</style>

<script>
export default {
    name: "OutSourceBanner",
};
</script>
