<template>
    <div class="cards">
        <AboutUsCard icon="lightbulb" title="Who we serve"> {{ $t("about us T1") }}. </AboutUsCard>
        <AboutUsCard icon="microchip" title="Our skill domains"> {{ $t("about us T2") }}. </AboutUsCard>
        <AboutUsCard icon="handshake" title="How we integrate">
            {{
                $t(
                    " GO Platform is designed to complement and strengthen the tools and content stack your enterprise uses today. We integrate seamlessly and help you make the most of your investments.",
                )
            }}
        </AboutUsCard>
    </div>
</template>

<script>
import AboutUsCard from "./AboutUsCard.vue";

export default {
    name: "AboutUsCards",
    components: {
        AboutUsCard,
    },
};
</script>

<style scoped>
.cards {
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
    margin: 80px 0;
}

@media (max-width: 1023px) {
    .cards {
        flex-direction: column;
        align-items: center;
        margin: 40px 0;
    }
}
</style>
