<template>
    <section class="main">
        <!-- prettier-ignore -->
        <h2>
            {{ $t("A Science-Backed Hiring Solution") }}
        </h2>
        <p>
            {{
                $t(
                    "Experience an enjoyable, accurate, and scientifically sound talent assessment with our multi-layered approach, enabling you to validate and rank your candidates' skills with confidence.",
                )
            }}
        </p>
        <div class="button-container">
            <a href="/register" class="cta-button">{{ $t("Start for free") }}</a>
            <a href="https://calendly.com/aouf-abdellah/20min" target="_blank" rel="noopener noreferrer" class="cta-button bordered-button">{{ $t("Book a demo") }}</a>
        </div>
    </section>
</template>

<script>
export default {
    name: "HeroSection",
};
</script>

<style scoped>
.main {
    position: relative;
    background-color: #f5f8ff;
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 140px 100px 40px 100px;
}

.main::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0.22;
    /* Adjust the opacity as needed */
    z-index: -1;
}

.main h2 {
    text-align: center;
    font-size: 54px;
    font-weight: 700;
    color: #2196f3;
    width: 70%;
}

.main p {
    margin: 25px 0;
    width: 60%;
    color: #334155;
    font-size: 18px;
    font-weight: 400;
    text-align: center;
}

.button-container {
    display: inline-flex;
    gap: 20px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    font-weight: 600;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    text-decoration: none;
    background-color: #2196f3;
    color: #ffffff;
}

.cta-button:hover {
    opacity: 0.85;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.button-container .bordered-button {
    background-color: transparent;
    color: #2196f3;
    border: 1.5px solid #2196f3;
}

.button-container .bordered-button:hover {
    background-color: rgba(33, 150, 243, 0.1);
}

@media (max-width: 1023px) {
    .main {
        padding: 60px 40px;
        width: 100%;
        min-height: 33vh;
        padding-top: 100px;
    }

    .main h2 {
        font-size: 20px;
        text-align: center;
        width: 80%;
    }

    .main p {
        width: 100%;
        font-size: 12px;
    }

    .main .cta-button {
        padding: 8px 20px;
        font-size: 12px;
    }
}
</style>
