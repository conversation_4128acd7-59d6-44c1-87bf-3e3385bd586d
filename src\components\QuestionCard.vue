<template>
    <UpdateCustomQuestion :isShown="isShown" :question_id="assessement._id" :closePanel="closePanel" :type="question_type" />
    <ConfirmDeleteQuestion :closeConfirm="closeConfirm" :isShown="isShownDelete" :id="assessement._id" />
    <div class="top-assessement">
        <div class="relative flex flex-col items-start justify-between h-full w-full px-3 py-3 bg-white">
            <div class="w-full">
                <div class="flex relative justify-between">
                    <div class="flex gap-0 border-[#2196f3] pb-1 flex-col justify-center items-start w-full">
                        <h2
                            class="title-question w-full text-lg font-semibold text-left relative pb-4 mb-4 after:content-('') after:absolute after:bottom-0 after:left-0 after:w-full after:h-px after:bg-NeonBlue"
                        >
                            {{ $t(assessement.name) }}
                        </h2>
                        <div class="flex justify-center gap-4 items-center mb-4">
                            <span class="text-sm text-NeonBlue font-normal">{{ $t(assessement.category) }}</span>
                            <span class="text-sm text-NeonBlue font-normal">|</span>
                            <span class="text-sm text-NeonBlue font-normal">{{ assessement.time }} '</span>
                            <span class="text-sm text-NeonBlue font-normal">|</span>
                            <span class="text-sm text-NeonBlue font-normal"> {{ !assessement.company ? "Go Platform" : "My library" }} </span>
                        </div>
                    </div>
                    <div class="absolute top-0 right-0">
                        <font-awesome-icon
                            v-if="assessement.company"
                            @click.stop="isOpen = !isOpen"
                            class="cursor-pointer rounded-full w-5 h-5 hover:bg-[#E0E0E0] p-1 rotate-90"
                            :icon="['fas', 'ellipsis']"
                        />
                    </div>
                    <div v-if="isOpen" ref="container" class="absolute -right-12 p-2 bottom-[20%] rounded-md w-25 flex bg-white border border-gray-200 flex-col gap-2 z-10">
                        <font-awesome-icon @click="defineType(assessement.category)" class="cursor-pointer hover:bg-[#E0E0E0] rounded-md p-1" :icon="['far', 'pen-to-square']" />
                        <font-awesome-icon @click="isShownDelete = true" class="cursor-pointer rounded-md hover:bg-[#E0E0E0] p-1" :icon="['far', 'trash-can']" />
                    </div>
                </div>
                <p class="text-sm leading-4 font-light py-4 text-left">{{ $t(getFirstPhrase(assessement.question)) }}</p>
            </div>
            <div class="w-full flex justify-between gap-2 items-center relative">
                <button
                    class="flex-1 text-gray-900 border border-gray-300 hover:border-CustomBlue hover:text-CustomBlue text-m rounded min-h-[30px] px-4 py-[10px] font-semibold"
                    v-if="!showedMore"
                    @click="showedMore = true"
                >
                    {{ $t("Show more") }}
                </button>
                <button
                    class="flex-1 text-gray-900 border border-gray-300 hover:border-CustomBlue hover:text-CustomBlue text-m rounded min-h-[30px] px-4 py-[10px] font-semibold"
                    v-else
                    @click="showedMore = false"
                >
                    {{ $t("Show less") }}
                </button>
                <button
                    v-if="!verifyAddedQuestion"
                    class="flex-1 bg-[#2196f3] text-white hover:opacity-85 text-m rounded w-[4rem] min-h-[30px] px-4 py-[10px] font-semibold flex items-center justify-center relative cursor-pointer"
                    @click="add(assessement)"
                >
                    {{ $t("Add now") }}
                </button>
                <button v-else class="flex-1 bg-red-600 text-gray-700 hover:opacity-85 w-[4rem] text-m rounded min-h-[30px] px-5 py-[10px] font-semibold" @click="deleteSelectedQst(addedQuestionId)">
                    <font-awesome-icon class="text-white" :icon="['fas', 'xmark']" />
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import UpdateCustomQuestion from "@/components/UpdateCustomQuestion";
import ConfirmDeleteQuestion from "@/components/ConfirmDeleteQuestion";
export default {
    name: "QuestionCard",
    components: {
        UpdateCustomQuestion,
        ConfirmDeleteQuestion,
    },
    props: ["assessement", "addAssessment", "selectedQuestions", "deleteSelectedQst"],
    data() {
        return {
            imagePath: "",
            selected: false,
            showedMore: false,
            isOpen: false,
            isShown: false,
            isShownDelete: false,
        };
    },
    computed: {
        verifyAddedQuestion() {
            return !!this.selectedQuestions.find((question) => question.question === this.assessement?.question);
        },
        addedQuestionId() {
            const selectedQuestion = this.selectedQuestions.find((question) => question.question === this.assessement.question);
            return selectedQuestion ? selectedQuestion.id : null;
        },
    },

    methods: {
        defineType(type) {
            this.question_type = type;
            this.isShown = true;
        },
        closeConfirm() {
            this.isShownDelete = false;
        },
        closePanel() {
            this.isShown = false;
        },
        add(assessment) {
            this.addAssessment(assessment);
        },

        getFirstPhrase(description) {
            if (!this.showedMore) {
                // Split the string into an array of words
                let words = description?.split(/\s+/);

                // Extract the first 200 words
                let first30Words = words?.slice(0, 30)?.join(" ");
                if (words.length < 30) return first30Words;
                first30Words += "...";
                return first30Words;
            }
            return description;
        },
        handleClickOutside(event) {
            if (this.$refs.container && !this.$refs.container.contains(event.target)) {
                this.isOpen = false;
            }
        },
    },
    mounted() {
        window.addEventListener("click", this.handleClickOutside);
    },
    unmounted() {
        window.removeEventListener("click", this.handleClickOutside);
    },
};
</script>

<style lang="scss" scoped>
.top-assessement {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
    padding: 0.2rem;
    gap: 2rem;
    padding-bottom: 1rem;
    background: #ffffff;
    height: 300px;
    width: 100%;
}
</style>
