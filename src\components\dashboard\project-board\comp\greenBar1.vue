<template>
    <div class="greenbar-container flex flex-col sm:flex-row items-center w-full mb-4 px-2 py-2 bg-white rounded-lg shadow-sm">
        <!-- Label -->
        <p class="label text-gray-600 font-semibold order-1 sm:order-1 sm:w-1/5 w-full text-[0.85rem] sm:text-sm md:text-base truncate mx-0 sm:mx-2 mb-1 sm:mb-0 tracking-wide uppercase">
            {{ title }}
        </p>

        <!-- Score -->
        <p class="score text-[#34d399] font-bold text-xs sm:text-xs md:text-sm order-2 sm:w-1/12 w-full text-center mx-0 sm:mx-2 mb-1 sm:mb-0 bg-[#f3f4f6] rounded px-2 py-1 shadow">
            {{ animatedScore }}
        </p>

        <!-- Progress Bar (prioritized, takes most space) -->
        <div class="progressbar flex flex-row items-center flex-1 order-3 sm:order-3 w-full sm:w-auto my-2 sm:my-0">
            <template v-for="(value, index) in maxScore" :key="index">
                <div
                    class="bar w-5 sm:w-7 h-2 mr-1 sm:mr-2 rounded-full transition-all duration-300 relative"
                    :class="{
                        'bg-[#93B44B]': index + 1 < animatedScore,
                        'bg-[#D9D9D9]': index + 1 > animatedScore,
                        'bg-custom-green': index + 1 === getNaturalNumber(animatedScore),
                    }"
                >
                    <!-- Highlight Circle -->
                    <div v-if="index + 1 === getNaturalNumber(animatedScore)" class="absolute w-2 h-2 ml-5 sm:ml-7 rounded-full border-2 border-[#E53282] animate-ping-once highlight-glow"></div>
                </div>
            </template>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        score: {
            type: Number,
            default: 3,
        },
        title: {
            type: String,
            default: "Decision making",
        },
        maxScore: {
            type: Number,
            default: 10,
        },
    },
    data() {
        return {
            animatedScore: 0,
            observer: null,
            hasAnimated: false,
        };
    },
    mounted() {
        this.createObserver();
    },
    beforeUnmount() {
        if (this.observer) {
            this.observer.disconnect();
        }
    },
    methods: {
        createObserver() {
            const options = {
                root: null,
                threshold: 0.4,
            };

            this.observer = new IntersectionObserver(([entry]) => {
                if (entry.isIntersecting && !this.hasAnimated) {
                    this.animateScore();
                    this.hasAnimated = true;
                }
            }, options);

            this.observer.observe(this.$el);
        },

        animateScore() {
            const duration = 800;
            const frameRate = 1000 / 60;
            const totalFrames = Math.round(duration / frameRate);
            let currentFrame = 0;

            const interval = setInterval(() => {
                currentFrame++;
                const progress = this.easeOutCubic(currentFrame / totalFrames);
                this.animatedScore = parseFloat((this.score * progress).toFixed(2));

                if (currentFrame >= totalFrames) {
                    this.animatedScore = this.score;
                    clearInterval(interval);
                }
            }, frameRate);
        },

        getNaturalNumber(value) {
            const decimalPart = value % 1;
            return decimalPart >= 0.5 ? Math.floor(value) : Math.floor(value);
        },

        easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        },
    },
};
</script>

<style scoped>
.greenbar-container {
    min-width: 0;
    gap: 0.5rem;
    border: 1px solid #f3f4f6;
}
.label,
.score {
    min-width: 0;
}
.progressbar {
    min-width: 0;
    flex: 1;
}
.bar {
    min-width: 1rem;
    box-shadow: 0 1px 4px rgba(52, 211, 153, 0.08);
    position: relative;
}
.bg-custom-green {
    background-color: #34d399;
}
.highlight-glow {
    box-shadow: 0 0 8px rgba(229, 50, 130, 0.6);
    background: #fff;
}
@keyframes ping-once {
    0% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.6);
        opacity: 0.2;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}
.animate-ping-once {
    animation: ping-once 1.2s ease-out;
}

/* Responsive styles */
@media (max-width: 640px) {
    .greenbar-container {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0.5rem;
    }
    .progressbar,
    .label,
    .score {
        width: 100% !important;
        margin-bottom: 0.5rem;
        text-align: left;
    }
    .bar {
        width: 1rem !important;
        margin-right: 0.5rem !important;
    }
}
</style>
