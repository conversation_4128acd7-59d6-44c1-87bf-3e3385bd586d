<template>
    <AssessementPreview :assessment="preview" :showPreview="showPreview" :togglePreview="togglePreview" :addAssessment="addAssessment" />
    <ScreenerQst v-if="showScreener" :toggleScreener="toggleScreener" />

    <div @scroll="handleScroll" class="libraryWrapper relative">
        <div class="relative flex mt-[4rem] mb-2 items-center gap-4">
            <!-- Search bar -->
            <div class="relative w-[30%]">
                <font-awesome-icon :icon="['fas', 'search']" class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 text-sm" />
                <input
                    placeholder="Search Question"
                    class="shadow-m text-sm py-1 pl-8 pr-3 border border-[1.5px] rounded-md h-[3rem] w-full border-[#e7e7e9] outline-none focus:border-2 focus:border-NeonBlue"
                    type="text"
                    id="searchInput"
                    name="searchInput"
                    v-model="searchText"
                    required
                />
            </div>


            <!-- Library Filter -->
            <div class="relative" ref="libraryFilter">
                <button @click="toggleLibrary"
                    :class="{ 'filter-btn': true, 'bg-NeonBlue text-white': selectedLibraries.length > 0 }">
                    {{ $t("Library") }}<span class="ml-2"><font-awesome-icon :icon="['fas', 'caret-down']" /></span>
                </button>
                <div v-if="isOpenLibrary" class="dropdown">

                    <label v-for="(library, index) in libraries" :key="index" class="checkbox-label">
                        <input type="checkbox" :value="library" v-model="selectedLibraries"
                            @change="debouncedApplyAllFilters" class="checkbox-input" />
                        <span class="checkbox-text">{{ library }}</span>
                    </label>
                </div>
            </div>

            <!-- Category Filter -->
            <div class="relative" ref="categoryFilter">
                <button @click="toggleCategory"
                    :class="{ 'filter-btn': true, 'bg-NeonBlue text-white': selectedCategories.length > 0 }">
                    {{ $t("Category") }}<span class="ml-2"><font-awesome-icon :icon="['fas', 'caret-down']" /></span>
                </button>
                <div v-if="isOpenCategory" class="dropdown">
                    <label v-for="(category, index) in categories" :key="index" class="checkbox-label">
                        <input type="checkbox" :value="category" v-model="selectedCategories"
                            @change="debouncedApplyAllFilters" class="checkbox-input" />
                        <span class="checkbox-text">{{ category }}</span>
                    </label>
                </div>
            </div>
        </div>

        <hr class="border border-[#e7e7e9] my-6 w-full" />
        <div v-if="this.Store.isLoadingQuestions" class="loader">
            <LoadingComponent />
        </div>
        <div class="library-loading" v-else>

            <div v-if="filteredAssessments && filteredAssessments.length > 0"
                class="library-grid-top flex flex-col gap-6 w-full">
                <QuestionCard v-for="assess in filteredAssessments" :key="assess._id" :assessement="assess"
                    @openPreview="OpenPreview(assess)" :addAssessment="addQst" :deleteSelectedQst="deleteSelectedQst"
                    :selectedQuestions="selectedQuestions" />
            </div>
            <div v-else-if="!this.Store.isLoadingQuestions" class="flex items-center justify-center w-full h-[300px]">
                <span class="text-center w-full span-no-cheater">No questions found matching your criteria.</span>

            </div>
        </div>
    </div>
</template>

<script>
import AssessementPreview from "@/components/dashboard/library/AssessementPreview.vue";
import QuestionCard from "@/components/QuestionCard2.vue";
import { useStore } from "@/store/index";
import LoadingComponent from "@/components/LoadingComponent.vue";
import ScreenerQst from "@/components/dashboard/createAssessment/ScreenerQst.vue";

// Debounce utility function
function debounce(func, delay) {
    let timeout;
    return function (...args) {
        const context = this;
        clearTimeout(timeout);
        timeout = setTimeout(() => func.apply(context, args), delay);
    };
}

export default {
    name: "CustomQuestionLib",
    components: {
        AssessementPreview,
        QuestionCard,
        LoadingComponent,
        ScreenerQst,
    },
    props: {
        addNewAssessment: Function,
        deleteSelectedQst: Function,
        addQst: Function,
        selectedQuestions: Array,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    data() {
        return {
            preview: {},
            isOpenCategory: false, // State for Category dropdown
            isOpenLibrary: false,  // State for Library dropdown
            showPreview: false,
            categories: ["Multiple-choice", "Essay"],
            libraries: ["Go Platform", "My library"],
            selectedCategories: [],
            selectedLibraries: [],
            searchText: "",
            showScreener: false,
            filteredAssessments: [], // This will hold the filtered results
            debouncedApplyAllFilters: null, // Debounced function holder
        };
    },
    computed: {
        onlyMyLibrary() {
            return this.selectedLibraries.length === 1 && this.selectedLibraries[0] === "My library";
        },
    },
    watch: {
        searchText() {
            if (this.debouncedApplyAllFilters) {
                this.debouncedApplyAllFilters();
            }
        },
        selectedCategories: {
            handler() { if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters(); },
            deep: true
        },
        selectedLibraries: {
            handler() { if (this.debouncedApplyAllFilters) this.debouncedApplyAllFilters(); },
            deep: true
        },
        // Watch for changes in the store's allCustomQuestions to re-filter
        'Store.allCustomQuestions': {
            handler() {
                if (this.debouncedApplyAllFilters) {
                    this.debouncedApplyAllFilters();
                }
            },
            immediate: true, // Run immediately on component mount
            deep: true,
        },
    },
    created() {
        this.debouncedApplyAllFilters = debounce(this.applyAllFilters, 300); // 300ms debounce delay
    },
    mounted() {
        this.id = this.$route.query.id;
        this.project_id = this.$route.query.id ? this.$route.query.id : "";

        // Initial data fetch from the store
        this.Store.fetchCustomGoPlatform();
        this.Store.fetchCustomCompany();
        this.Store.fetchProjects(); // Assuming this is also needed

        // Add global click listener when component is mounted
        document.addEventListener('click', this.handleClickOutside);
    },
    beforeUnmount() {
        // Remove global click listener when component is unmounted to prevent memory leaks
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        toggleScreener() {
            this.showScreener = !this.showScreener;
        },
        toggleCreateAssesment() {
            // This method seems unused in the provided template
            this.showCreateAssesment = !this.showCreateAssesment;
        },
        handleClickOutside(event) {
            const filters = [
                { ref: 'categoryFilter', isOpenState: 'isOpenCategory' },
                { ref: 'libraryFilter', isOpenState: 'isOpenLibrary' }
            ];

            let clickedInsideAnyFilter = false;
            for (const filter of filters) {
                const element = this.$refs[filter.ref];
                if (element && element.contains(event.target)) {
                    clickedInsideAnyFilter = true;
                    break;
                }
            }

            if (!clickedInsideAnyFilter) {
                this.closeAllDropdowns();
            }
        },
        applyAllFilters() {
            // Use Store.allCustomQuestions as the source for filtering
            let assessmentsToFilter = this.Store.allCustomQuestions || [];

            // Pre-calculate lowercased values and boolean flags once per filter run
            const lowerCaseSearchText = this.searchText.toLowerCase();
            const lowerCaseSelectedCategories = this.selectedCategories.map(cat => cat.toLowerCase());
            const lowerCaseSelectedLibraries = this.selectedLibraries.map(lib => lib.toLowerCase());

            this.filteredAssessments = assessmentsToFilter.filter((assessment) => {
                // Pre-calculate assessment properties to avoid repeated calls inside the loop
                const assessmentNameLowerCase = assessment.name ? assessment.name.toLowerCase() : '';
                const assessmentCategoryLowerCase = assessment.category ? assessment.category.toLowerCase() : '';
                const assessmentCompanyExists = !!assessment.company; // Converts to boolean

                const searchMatch = assessmentNameLowerCase.includes(lowerCaseSearchText);

                const categoryMatch = lowerCaseSelectedCategories.length === 0 ||
                    lowerCaseSelectedCategories.includes(assessmentCategoryLowerCase);

                const libraryMatch = lowerCaseSelectedLibraries.length === 0 ||
                    (lowerCaseSelectedLibraries.includes("go platform") && !assessmentCompanyExists) ||
                    (lowerCaseSelectedLibraries.includes("my library") && assessmentCompanyExists);

                return searchMatch && categoryMatch && libraryMatch;
            });
        },
        toggleCategory() {
            this.isOpenCategory = !this.isOpenCategory;
            this.isOpenLibrary = false; // Close other dropdowns
        },
        toggleLibrary() {
            this.isOpenLibrary = !this.isOpenLibrary;
            this.isOpenCategory = false; // Close other dropdowns
        },
        closeAllDropdowns() {
            this.isOpenCategory = false;
            this.isOpenLibrary = false;
        },
        handleScroll() {
            // This method seems to be for a fixed filter bar, which is not in the current template structure
            // If you reintroduce a fixed filter bar, ensure this.$refs.libraryWrapper is correctly set on its scrollable parent
            // and the filter bar itself has a ref.
            // For now, it's safe to keep it, but it won't have an effect without the corresponding HTML structure.
            if (this.$refs.libraryWrapper) {
                // The logic for filterContainerTop is not directly related to filtering speed,
                // but if this component is not meant to have a scrolling `libraryWrapper`
                // and a fixed filter bar, this logic might be unnecessary.
                // Keeping it as is per "don't touch anything else" instruction.
                if (this.$refs.libraryWrapper.scrollTop < this.scrollThreshold) {
                    this.filterContainerTop = this.$refs.libraryWrapper.scrollTop + 40;
                } else {
                    this.filterContainerTop = this.$refs.libraryWrapper.scrollTop + 10;
                }
            }
        },
        OpenPreview(test) {
            this.preview = test;
            this.togglePreview();
        },
        togglePreview() {
            this.showPreview = !this.showPreview;
        },
        addAssessment(assessment) {
            this.showPreview = false;
            this.addNewAssessment(assessment);
        },
    },
};
</script>

<style scoped lang="scss">


.library-grid-top {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    width: 100%;
}

.span-no-cheater {
    color: #2195f3cd;
    font-family: DM Sans;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
    /* 160% */
    letter-spacing: -0.4px;
}

.library-loading {
    padding: 0;
    height: auto;
}


.checkbox-label {
    display: flex;
    align-items: center;
    margin-right: 20px;
    margin-bottom: 10px;
}

.checkbox-input {
    margin-right: 5px;
    background-color: #2196f3;
}

.checkbox-text {
    margin-left: 5px;
    font-size: 15px;
    font-weight: 400;
}

.loader {
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.filter-btn {
    background-color: white;
    border: 1.5px solid #e7e7e9;
    padding: 13px 10px;
    border-radius: 50px;
    cursor: pointer;
    font-size: 14px;
    color: #4a5568;
    transition: background-color 0.2s, color 0.2s;

    display: inline-flex;
    /* Inline-flex to respect content size */
    align-items: center;
    /* Vertically center items */
    gap: 8px;
    /* Space between text and icon */

    max-width: 100%;
    /* Prevent overflow */
    white-space: nowrap;
    /* Avoid multi-line */
}
.filter-btn:hover {
    background-color: #2196f3;
    color: white;
}
.filter-btn.bg-NeonBlue {
    background-color: #2196f3;
    color: white;
}

.dropdown {
    position: absolute;
    top: 110%;
    z-index: 10;
    background: white;
    border: 1px solid #ccc;
    padding: 1rem;
    width: 220px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}
input {
    cursor: pointer;
}
</style>