<template>
    <div class="w-full bg-custom-gradient py-12">
        <div class="px-6 py-12 max-w-7xl mx-auto text-center text-white">
            <h2 class="text-2xl md:text-4xl font-bold mb-4">{{ $t("Backed by GO PLATFORM 30 Days Guarantee") }}</h2>
            <p class="text-lg md:text-xl">{{ $t("If you're not satisfied with your hire within the first 30 days, we'll find another talent at no additional cost") }}</p>
        </div>
    </div>
    <div class="w-full py-16 bg-white">
        <div class="px-6 max-w-7xl mx-auto">
            <div class="flex flex-col lg:flex-row gap-12">
                <!-- Left Content -->
                <div class="lg:w-1/2 text-left">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-8">
                        {{ $t("Recruit the Top 1% of") }}
                        <span class="relative inline-block">
                            <div class="absolute inset-0 bg-blue-100 -rotate-3"></div>
                            <span class="relative z-10">{{ $t("African") }}</span>
                        </span>
                        {{ $t("Talent") }}
                    </h2>
                    <p class="text-gray-600 mb-8">
                        {{ $t("Our collaborative approach guarantees that we fully grasp your company’s unique needs and concerns to find a perfect fit–in both skills and culture") }}
                    </p>

                    <div class="space-y-6">
                        <div v-for="(feature, index) in features" :key="index" class="flex items-center">
                            <font-awesome-icon :icon="['fas', 'check-circle']" class="text-NeonBlue mr-4 text-xl" />
                            <span class="text-gray-700">{{ $t(feature) }}</span>
                        </div>
                    </div>
                </div>

                <!-- Single Card -->
                <div class="lg:w-1/2">
                    <div class="bg-white px-6 py-8 rounded-xl shadow-lg border border-gray-200 hover:shadow-xl transition-shadow max-w-[500px] max-w-[400px] ml-auto">
                        <div class="flex items-center mb-4">
                            <div class="bg-blue-100 p-3 rounded-full mr-4 shrink-0 flex items-center justify-center">
                                <font-awesome-icon :icon="['fas', 'user']" class="text-NeonBlue w-6 h-6" />
                            </div>
                            <div class="text-left">
                                <h3 class="font-bold text-gray-800 text-lg">Jennifer Okafor</h3>
                                <p class="text-gray-600 text-sm">{{ $t("ML Engineer") }}</p>
                                <span class="inline-block mt-2 text-xs bg-NeonBlue text-white px-3 py-1 rounded-full"> 4 {{ $t("Years Experience") }} </span>
                            </div>
                        </div>

                        <div class="flex flex-wrap gap-2 mb-4">
                            <span class="bg-[#eff6ff] text-[#2c2f30] px-3 py-1 rounded-full text-sm"> Python </span>
                            <span class="bg-[#eff6ff] text-[#2c2f30] px-3 py-1 rounded-full text-sm"> Tensorflow </span>
                            <span class="bg-[#eff6ff] text-[#2c2f30] px-3 py-1 rounded-full text-sm"> Keras </span>
                            <span class="bg-[#eff6ff] text-[#2c2f30] px-3 py-1 rounded-full text-sm"> Pytorch </span>
                        </div>

                        <p class="w-full text-left text-[#6b6f71] text-[17px] font-light mb-6">{{ $t("Design and implement machine learning models for predictive analytics") }}</p>

                        <div class="flex justify-between items-center mt-auto">
                            <button class="bg-NeonBlue text-white px-5 py-2.5 rounded-md hover:opacity-85 transition-colors text-sm font-semibold">{{ $t("Hire Now") }}</button>
                            <span class="text-gray-800 font-semibold text-lg"
                                >$750<span class="text-sm">/{{ $t("month") }}</span></span
                            >
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "RecruitmentSection",
    data() {
        return {
            features: ["Tailored Talent Matching", "Competitive Costing", "Hassle-free Hiring"],
        };
    },
};
</script>
