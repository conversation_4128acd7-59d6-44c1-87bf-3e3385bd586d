<template>
    <section :class="{ 'overflow-y-hidden': isShown }" class="p-8 space-y-8">
        <div class="overflow-x-auto">
            <table class="min-w-full text-[12px] border border-gray-200 text-left">
                <thead class="bg-blue-600 text-white">
                    <tr>
                        <th class="p-3">{{ $t("Name") }}</th>
                        <th class="p-3">{{ $t("Industry") }}</th>
                        <th class="p-3">{{ $t("Website") }}</th>
                        <th class="p-3">{{ $t("Email") }}</th>
                        <th class="p-3">{{ $t("Date") }}</th>
                        <th class="p-3 text-center">{{ $t("Actions") }}</th>
                    </tr>
                </thead>
                <tbody class="bg-white text-gray-700">
                    <tr v-for="company in companies" :key="company._id" class="border-b">
                        <td class="p-3 truncate max-w-[200px] capitalize">{{ company.name }}</td>
                        <td class="p-3">{{ company.industry }}</td>
                        <td class="p-3">{{ company.website }}</td>
                        <td class="p-3">{{ company.users?.[0]?.email || company?.email || "N/A" }}</td>
                        <td class="p-3">{{ getDate(company.createdAt) }}</td>
                        <td class="p-3 text-center space-x-2">
                            <font-awesome-icon @click="selectcompany(company._id)" class="w-4 h-4 p-2 text-white bg-green-500 rounded hover:opacity-85" :icon="['fas', 'circle-info']" />
                            <font-awesome-icon class="w-4 h-4 p-2 text-white bg-blue-500 rounded hover:opacity-85" :icon="['fas', 'ban']" />
                            <font-awesome-icon @click="deleteCompany(company._id)" class="w-4 h-4 p-2 text-white bg-red-500 rounded hover:opacity-85" :icon="['fas', 'trash']" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- Detail Modal -->
        <div v-show="companydetail" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-45">
            <div class="w-11/12 max-w-6xl h-[80vh] bg-white overflow-y-auto rounded-md p-6 space-y-6 relative">
                <div class="flex justify-end space-x-2">
                    <font-awesome-icon class="w-4 h-4 p-2 text-white bg-blue-500 rounded hover:opacity-85" :icon="['fas', 'ban']" />
                    <font-awesome-icon @click="deleteCompany(selectedCompany._id)" class="w-4 h-4 p-2 text-white bg-red-500 rounded hover:opacity-85" :icon="['fas', 'trash']" />
                </div>
                <div class="flex flex-col lg:flex-row gap-6">
                    <img :src="getImage(selectedCompany.logo)" class="w-full max-w-xs border border-black" alt="logo" />
                    <div class="flex flex-col flex-1 space-y-5">
                        <h1 class="text-4xl font-bold text-slate-700">{{ selectedCompany.name }}</h1>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-lg text-slate-700">
                            <div>
                                <span class="font-semibold">{{ $t("Id:") }}</span> {{ selectedCompany._id || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Industry:") }}</span> {{ selectedCompany.industry || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Employees count:") }}</span> {{ selectedCompany.employees_count || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Registration date:") }}</span> {{ getDate(selectedCompany.createdAt) || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Location:") }}</span> {{ selectedCompany.location || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Credit:") }}</span> {{ selectedCompany.credit || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Recent Project:") }}</span> {{ getDate(selectedCompany.recentProjectDate) || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Total Projects:") }}</span> {{ selectedCompany.totalProjects || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Subscription plan:") }}</span> {{ selectedCompany.plan || "N/A" }}
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="font-semibold">{{ $t("Verified:") }}</span>
                                <span>{{ selectedCompany.isVerified }}</span>
                                <span @click="Verifycompany" v-show="!selectedCompany.isVerified" class="bg-gray-100 text-sm font-light border px-3 py-1 rounded border-gray-400 cursor-pointer">{{
                                    $t("Verify")
                                }}</span>
                            </div>
                        </div>
                        <div v-for="(user, index) in selectedCompany?.users" :key="index" class="text-lg text-slate-700 space-y-1 pt-4 border-t">
                            <div>
                                <span class="font-semibold">{{ $t("Contact:") }}</span>
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Name:") }}</span> {{ user.name || "N/A" }}
                            </div>
                            <div>
                                <span class="font-semibold">{{ $t("Email:") }}</span> {{ user.email || "N/A" }}
                            </div>
                        </div>
                        <div class="pt-4 border-t">
                            <span class="font-semibold">{{ $t("Discounts:") }}</span>
                            <div v-if="selectedCompany.discounts?.length" class="flex flex-wrap gap-2 mt-2">
                                <span v-for="tag in selectedCompany.discounts" :key="tag.name" class="px-3 py-1 border rounded-full bg-white text-sm border-gray-400">
                                    {{ tag.name }}
                                </span>
                            </div>
                            <p v-else class="text-slate-500">{{ $t("N/A") }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</template>
<script>
import axios from "axios";
import getDateAndTime from "@/helpers/getDateandTime";
import { BASE_URL } from "@/constants";
// import getImgUrl from "@/helpers/getImage";
export default {
    name: "companiesBoard",
    data() {
        return {
            companydetail: false,
            selectedCompany: {},
            isShown: false,
            companies: [],
        };
    },
    computed: {
        keys() {
            return Object.keys(this.selectedCompany);
        },
    },
    methods: {
        getDate(str) {
            return getDateAndTime(str);
        },
        Verifycompany() {
            this.selectedCompany.isVerified = true;
        },
        getImage(imgFileId) {
            if (imgFileId) {
                var image = `data:image/png;base64,${imgFileId}`;
                return image;
            }
            return require("@/assets/Images/candidate-image.png");
        },
        selectcompany(id) {
            this.selectedCompany = this.companies.find((company) => company._id === id);
            this.companydetail = true;
        },
        closeDetail() {
            this.selectedCompany = {};
            this.companydetail = false;
        },
        deleteCompany(id) {
            this.companydetail = false;
            let config = {
                method: "delete",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/company/deletecompany/${id}`,
                headers: {
                    "Content-Type": "application/json",
                },
                withCredentials: true,
            };
            axios
                .request(config)
                .then((response) => {
                    if (response.data.deleted) {
                        const companies = this.companies.filter((company) => company._id != id);
                        this.companies = companies;
                        // console.log('company was deleted')
                    } else console.log("company was not deleted");
                })
                .catch((err) => {
                    console.error(err);
                });
        },
    },
    mounted() {
        let config = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/company/fetchcompanies`,
            headers: {
                "Content-Type": "application/json",
            },
            withCredentials: true,
        };
        axios
            .request(config)
            .then((response) => {
                this.companies = response.data;
                // console.table(this.companies)
            })
            .catch((err) => {
                console.error(err);
            });
    },
};
</script>
