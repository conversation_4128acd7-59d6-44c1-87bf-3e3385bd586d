<template>
    <InviteCandidate :showEmail="showEmail" :assessmentsLength="project.assessments" :toggleEmail="toggleEmail" :projectId="project._id" />
    <AllRatings :viewAllRatings="viewAllRatings" :toggleRatings="toggleRatings" :candidatesRating="candidatesRating" />
    <div v-if="isLoading" class="loader">
        <LoadingComponent />
    </div>
    <div v-else class="mb-40">
        <!-- Header Stats -->
        <div class="grid grid-cols-4 gap-4 mb-4">
            <StatsCard title="Success Rate" :value="getSuccessRate() + '%'" :iconIndex="2" />

            <StatsCard title="Completion" :value="isNaN(compitionRate) ? 0 : compitionRate + '%'" :iconIndex="3" />

            <StatsCard title="Applicants" :value="this.candidates.length" :iconIndex="0" />

            <StatsCard title="Assessements" :value="this.project.assessments?.length" :iconIndex="1" />
        </div>
        <div>
            <div class="board-container">
                <div class="w-full flex flex-col-reverse lg:flex-row justify-between p-3 bg-white border-b rounded mb-5 shadow-card">
                    <div class="flex">
                        <Popper :content="$t('Back')" placement="top" :hover="true">
                            <button
                                class="menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 my-1 hidden lg:block"
                                @click="
                                    () => {
                                        this.$router.push('/dashboard');
                                    }
                                "
                            >
                                <font-awesome-icon :icon="['fas', 'angle-left']" class="pl-6" />
                            </button>
                        </Popper>
                        <div class="mx-4 flex flex-col">
                            <h2 class="projData bg-custom-gradient bg-clip-text text-transparent">
                                {{ project.name ? project.name : "Untitled" }}
                                {{ project.seniority && " - " + project.seniority }}
                                {{ project.jobTitle && " - " + project.jobTitle }}
                            </h2>
                            <div class="flex" style="color: #2196f3">
                                <div class="flex items-center">
                                    <font-awesome-icon :icon="['far', 'file-lines']" class="mx-2" />
                                    <p>{{ filteredAssessmentsLength }} tests</p>
                                </div>
                                <div class="flex mx-2 items-center">
                                    <font-awesome-icon :icon="['far', 'clock']" class="mx-2" />
                                    <p>{{ totalDuration }} minutes</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="flex gap-4 items-center justify-start relative">
                        <button
                            class="menuBtn bg-[#E0E4EC] text-gray-700 hover:bg-black/10 lg:hidden block"
                            @click="
                                () => {
                                    this.$router.go(-1);
                                }
                            "
                        >
                            <font-awesome-icon :icon="['fas', 'angle-left']" />
                        </button>
                        <Popper :content="$t('Edit, Duplicate and Delete')" placement="top" :hover="true">
                            <button @click="toggleProjectMenu" style="cursor: pointer" class="menuBtn text-gray-700 mt-1 border-[1.5px] border-gray-200 hover:bg-gray-50">
                                <font-awesome-icon :icon="['fas', 'ellipsis-vertical']" class="pl-7" />
                            </button>
                        </Popper>
                        <ProjectMenu
                            :showDetails="showMenu"
                            :toggleMenu="toggleProjectMenu"
                            :editable="invitations.length > 0 ? false : true"
                            :thisProject="project"
                            :hideSample="true"
                            class="absolute top-0 right-0"
                            style="position: absolute; right: 20%"
                        />
                        <Popper :content="$t('Preview')" placement="top" :hover="true">
                            <div
                                class="text-slate-700 px-4 font-light text-sm py-3 my-1 menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 flex items-center justify-center cursor-pointer"
                                @click.stop
                                @click="navigateToPreview"
                            >
                                <!-- <p>{{ $t("Preview") }}</p> -->
                                <font-awesome-icon :icon="['fas', 'eye']" class="w-5 h-5" />
                            </div>
                        </Popper>
                        <Popper :content="$t('Invite candidates')" placement="top" :hover="true">
                            <button ref="targetElement3" @click="toggleEmail" class="nextStep ml-auto">Invite</button>
                        </Popper>
                    </div>
                </div>

                <div class="navigation-tabs bg-white h-[62px] rounded shadow-card">
                    <div class="nav-links font-inter text-md pt-5 font-bold text-left decoration-skip-ink-none flex flex-row justify-between items-center gap-4 text-[#343637] h-full">
                        <router-link
                            to="#"
                            @click.prevent="navigateToDashBoard(project)"
                            class="text-gray-600 hover:text-blue-500 focus:text-blue-600 visited:text-purple-600 transition"
                            :class="`${this.$route.path == `/${locale}/boards` ? 'active' : ''}`"
                        >
                            <span>{{ $t("Summary") }}</span>
                        </router-link>

                        <router-link to="#" @click.prevent="navigateToDetailBoard(project)" :class="`${this.$route.path == `/${locale}/Details` ? 'active' : ''}`">
                            <span>{{ $t("Details") }}</span>
                        </router-link>

                        <router-link to="#" @click.prevent="navigateToCheatBoard(project)" :class="`${this.$route.path == `/${locale}/CheatTab` ? 'active ' : ''}`">
                            <span>{{ $t("Anti cheat") }}</span>
                        </router-link>

                        <router-link to="#" class="disabled" @click.prevent="navigateToVideoBoard(project)" :class="`${this.$route.path == `/${locale}/DashVideo` ? 'active ' : ''}`">
                            <div class="absolute top-[-15px] right-[-25px] rounded-[15px] text-[12px] px-2 text-[#fff] bg-[#2371b6]">{{ $t("Soon") }}</div>
                            <span class="hidden lg:block">{{ $t("Video Interview") }}</span>
                        </router-link>

                        <!-- <router-link to="/InvitedCoworkerTab" :class="`${this.$route.path == '/InvitedCoworkerTab' ? 'active ' : ''}`">
                    <font-awesome-icon :icon="['fas', 'chart-gantt']" class="nav-icon" />
                    <span>Invited Coworkers</span>
                </router-link> -->
                    </div>
                </div>
                <div class="min-h-screen">
                    <div class="flex flex-row-1 gap-4 mb-4">
                        <!-- Job Summary -->
                        <CardDetail width="w-3/5">
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Job Summary") }}</h2>
                            </template>
                            <template #body>
                                <p class="text-gray-700">
                                    {{
                                        $t(
                                            "A flexible and responsible team member focused on supporting tasks, improving processes, and contributing to overall success. Works well with others, adapts to change, and helps achieve shared goals.",
                                        )
                                    }}
                                    <b>{{ project.jobTitle && " " + project.jobTitle }} </b>
                                </p>
                            </template>
                        </CardDetail>

                        <!-- Top 3 Skills -->
                        <CardDetail width="w-2/5">
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Capability") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Top Skills") }}</h2>
                            </template>
                            <template #body>
                                <div class="flex justify-around">
                                    <SegmentedProgress
                                        v-for="(test, index) in this.Best3Tests.length"
                                        :key="index"
                                        :label="truncateSkill(this.Best3Tests[index]['testName'])"
                                        :total="this.Best3Tests[index]['totalPoints']"
                                        :maxTotal="this.Best3Tests[index]['maxPoints']"
                                        :average="this.getNaturalNumber((this.Best3Tests[index]['totalPoints'] * 10) / this.Best3Tests[index]['maxPoints'])"
                                    />
                                </div>
                                <div v-if="!this.Best3Tests || this.Best3Tests.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-Mapping-Yet.svg" alt="No Skills" class="w-[60%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Mapping Yet") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                            </template>
                        </CardDetail>
                    </div>
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <!-- Top Applicants -->
                        <CardDetail>
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Capability") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Top Applicants") }}</h2>
                            </template>
                            <template #body>
                                <div>
                                    <div
                                        v-for="(applicant, index) in sortedCandidates"
                                        :key="index"
                                        class="cursor-pointer transition-all duration-200 rounded hover:bg-blue-50"
                                        @click="selectApplicant(applicant)"
                                    >
                                        <GreenRecBar :title="truncateName(applicant.name)" :score="applicant.averageScore" />
                                    </div>
                                    <!-- Details Modal or Inline Details -->
                                    <div v-if="selectedApplicant" class="mt-4 p-4 bg-white border rounded shadow">
                                        <h3 class="text-lg font-bold text-blue-600">{{ selectedApplicant.name }}</h3>
                                        <p class="text-sm text-gray-700">Score: {{ selectedApplicant.averageScore }}</p>
                                        <!-- Add more details as needed -->
                                        <button class="mt-2 px-3 py-1 bg-blue-100 text-blue-700 rounded" @click="selectedApplicant = null">Close</button>
                                    </div>
                                </div>
                                <div v-if="!filtredCandidates || filtredCandidates.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-More-Candidates.svg" alt="No Technical Skills" class="w-[45%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No More Candidates") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                            </template>
                        </CardDetail>

                        <!-- Behavioral Competencies -->
                        <CardDetail>
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Capability") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Top Behavioral Competencies") }}</h2>
                            </template>
                            <template #body>
                                <SkillBar v-for="(applicant, index) in bestPsychSkills" :key="index" :label="applicant.assessmentName" :progress="applicant.percentage" max="100" />
                                <div v-if="!bestPsychSkills || bestPsychSkills.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-Behavioral-Skills.svg" alt="No Behavioral Skills" class="w-[60%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Behavioral Skills") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                                <!--GreenBar title="Decision Making"/!-->
                                <!--GreenBar title="Accountability"/!-->
                                <!--GreenBar title="Manage Complexity"/!-->
                            </template>
                        </CardDetail>

                        <!-- Technical Skills -->
                        <CardDetail>
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Capability") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Top Technical Skills") }}</h2>
                            </template>
                            <template #body>
                                <SkillBar v-for="(applicant, index) in bestTechSkills" :key="index" :label="applicant.assessmentName" :progress="applicant.percentage" />

                                <div v-if="!bestTechSkills || bestTechSkills.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-Technical-Skills.svg" alt="No Technical Skills" class="w-[45%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Technical Skills") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                            </template>
                        </CardDetail>

                        <!-- Interpersonal Skills -->
                        <CardDetail>
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Capability") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Top Interpersonal Skills") }}</h2>
                            </template>
                            <template #body>
                                <SkillBar v-for="(applicant, index) in bestSoftSkills" :key="index" :label="applicant.assessmentName" :progress="applicant.percentage" max="100" />
                                <div v-if="!bestSoftSkills || bestSoftSkills.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-Interpersonal-Skills.svg" alt="No Interpersonal Skills" class="w-[60%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Interpersonal Skills") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                            </template>
                        </CardDetail>
                    </div>
                    <div class="flex flex-row mt-4">
                        <!-- Top Treats -->
                        <CardDetail width="w-full">
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Identity") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Top Personality Treats") }}</h2>
                            </template>
                            <template #body>
                                <!--GreenRecBar title="Consciousness"/>
            <GreenRecBar title="Openness"/>
            <GreenRecBar title="Extroversion"/!-->
                                <SkillBar v-for="(applicant, index) in bestPersSkills" :key="index" :label="applicant.assessmentName" :progress="applicant.percentage" max="100" />

                                <div v-if="!bestPersSkills || bestPersSkills.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-Personality-Treats.svg" alt="No Treats Skills" class="w-[35%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Personality Treats") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                            </template>
                        </CardDetail>
                    </div>
                    <!-- Top countries 
                     
                    <SkillBar v-for="(applicant, index) in bestSoftSkills" :key="index" :label="applicant.assessmentName" :progress="applicant.percentage" max="100" />
                    -->

                    <div class="flex flex-row mt-4">
                        <CardDetail width="w-full">
                            <template #title>
                                <h4 class="text-sm text-[#8D98AA]">{{ $t("Location") }}</h4>
                            </template>
                            <template #header>
                                <h2 class="text-xl font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("Applicants Top Countries") }}</h2>
                            </template>
                            <template #body>
                                <!--SkillBar1></!--SkillBar1!-->
                                <SkillBar v-for="country in candidatesLocation" :key="country.name" :label="country.name" :progress="country.percentage" max="100" />

                                <div v-if="!candidatesLocation || candidatesLocation.length == 0" class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                                    <div class="flex justify-center items-center">
                                        <img src="../assets/No-Data-Available.svg" alt="No Treats Skills" class="w-[40%] h-auto" />
                                    </div>
                                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">{{ $t("No Data Available") }}</h2>
                                    <div class="flex flex-col justify-center items-center">
                                        <span class="text-center w-full">{{ $t("Looks like you haven't invite any candidates.") }}</span>
                                        <span class="text-center w-full">{{ $t("Try invite candidates.") }}</span>
                                    </div>
                                    <div></div>
                                </div>
                            </template>
                        </CardDetail>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { useStore } from "@/store/index";
import ProjectMenu from "@/components/dashboard/project/ProjectMenu.vue";
import { BASE_URL } from "@/constants";
import InviteCandidate from "@/components/dashboard/project-board/InviteCandidate.vue";
import AllRatings from "@/components/dashboard/project-board/AllRatings.vue";
import axios from "axios";
import introJs from "intro.js";
import "intro.js/introjs.css"; // Import Intro.js CSS
import LoadingComponent from "@/components/LoadingComponent.vue";
import CardDetail from "@/components/dashboard/project-board/comp/detailCard.vue";
import SkillBar from "@/components/dashboard/project-board/comp/SkillBar.vue";
import StatsCard from "@/components/dashboard/project-board/comp/StatsCard.vue";
//import SkillBar1 from "../components/dashboard/SkillProgressBar.vue";
//import CircularProgress from "../components/dashboard/CircularProgress1.vue";
//import GreenBar from "../components/dashboard/greenBar.vue";
import GreenRecBar from "@/components/dashboard/project-board/comp/greenBar1.vue";
import SegmentedProgress from "../components/dashboard/project-board/comp/SegmentedProgress.vue";
export default {
    name: "DashboardView",
    components: {
        AllRatings,
        StatsCard,
        ProjectMenu,
        LoadingComponent,
        InviteCandidate,
        CardDetail,
        SkillBar, //CircularProgress,
        //GreenBar,
        GreenRecBar,
        //,SkillBar1

        SegmentedProgress,
    },
    data() {
        return {
            selectedApplicant: null,
            SortedCandidates: [],
            allTests: [],
            bestAllSkills: [],
            bestTechSkills: [],
            bestSoftSkills: [],
            bestPsychSkills: [],
            bestPersSkills: [],
            talents: [
                {
                    name: "Riad Mahrez",
                    avatar: "https://via.placeholder.com/40", // Replace with actual avatar
                    category: "Technical Skills",
                    lastAssessment: "July 14, 2015",
                    score: "58%",
                },
                {
                    name: "Riad Mahrez",
                    avatar: "https://via.placeholder.com/40",
                    category: "Technical Skills",
                    lastAssessment: "November 28, 2015",
                    score: null,
                },
                // Add more data entries here...
            ],
            currentPage: 1,
            totalPages: 2,
            selectedRank: "Best Ranked",
            selectedStatus: "Passed",
            imagePath: require(`../assets/onBoardingGIFs/inviteCandidate.gif`),
            showEmail: false,
            windowWidth: 500,
            graphData: [],
            selected: [true, false, false],
            current: 0,
            BestTests: [],
            Best3Tests: [],

            BestThreeCandi: this.candidates,
            invitations: [],
            allCheaters: [],
            candidates: [],
            filtredCandidates: [],
            sortedCandidates: [],
            candidatesLocation: [],
            compitionRate: 0,
            project: {},
            candidatesScores: [],
            threeCandidates: [],
            candidatesRating: [],
            diskData: [],
            histogramData: [],
            histogramData2: [],
            ThreeRatings: [],
            behindSchedule: null,
            id: "",
            isCheatingCardOpen: false,
            candidateCard: {},
            showMenu: false,
            isLoading: true,
            viewAllRatings: false,
            viewAllCheaters: false,
            users: [
                { name: "User 1", email: "<EMAIL>" },
                { name: "User 2", email: "<EMAIL>" },
                { name: "User 3", email: "<EMAIL>" },
            ],
        };
    },
    methods: {
        /**
         * Get the nearest natural number based on custom logic.
         * @param {Number} value - The decimal number to round.
         * @returns {Number} The natural number.
         */
        getNaturalNumber(value) {
            const decimalPart = value % 1; // Extract decimal part
            if (decimalPart >= 0.5) {
                return Math.ceil(value); // Round up
            } else {
                return Math.floor(value); // Round down
            }
        },

        selectApplicant(applicant) {
            this.selectedApplicant = applicant;
        },
        /////////////////////////////////////FUNCTION TO GET TOP TESTS BASED ON POINTS FOR EACH CATEGORY/////////////////////////////////////////////////
        getTopTestsByCategoryWithPerc() {
            // Step 1: Check if results exist
            const candidate = this.filtredCandidates?.[0];
            const testsResults = candidate?.results;

            if (!Array.isArray(testsResults)) {
                console.warn("No test results found.");
                return {}; // Return empty result to avoid crashing
            }

            // Step 2: Merge data
            const mergedData = testsResults
                .map((result) => {
                    const matchedTest = this.allTests?.find((test) => test.name === result.assessmentName);
                    if (matchedTest) {
                        const maxScore = result.quesionsNbr || 1; // Avoid divide-by-zero
                        // const percentage = ((result.totalPoints / maxScore) * 100).toFixed(2);
                        const percentage = result.totalPoints !== undefined ? ((result.totalPoints / maxScore) * 100).toFixed(2) : result.totalRanges;
                        return {
                            ...result,
                            category: matchedTest.category,
                            totalQuestions: maxScore,
                            percentage,
                        };
                    }
                    return null;
                })
                .filter((item) => item !== null);

            // Step 3: Group by Category
            const testsByCategory = {};
            mergedData.forEach((test) => {
                if (!testsByCategory[test.category]) {
                    testsByCategory[test.category] = [];
                }
                testsByCategory[test.category].push(test);
            });

            // Step 4: Top tests per category
            const topTestsByCategory = {};
            Object.keys(testsByCategory).forEach((category) => {
                const sortedTests = testsByCategory[category].sort((a, b) => b.totalPoints - a.totalPoints);
                topTestsByCategory[category] = sortedTests.slice(0, 3);
            });

            return topTestsByCategory;
        },

        ////////////////////FUNCTION TO GET THE TOP 3 TESTS///////////////////////////////
        getTop3Tests() {
            // Step 1: Flatten all results
            const allResults = this.filtredCandidates?.flatMap((candidate) => candidate.results);

            // Step 2: Group points and maxPoints by test name
            const testStats = {};
            if (allResults != null) {
                allResults.forEach((result) => {
                    const { assessmentName, totalPoints, quesionsNbr } = result;
                    if (!testStats[assessmentName] && result.totalPoints) {
                        testStats[assessmentName] = { totalPoints: 0, maxPoints: 0 };
                        testStats[assessmentName].totalPoints += totalPoints ? result.totalPoints : (result.totalRanges * result.result.quesionsNbr) / 100;
                        testStats[assessmentName].maxPoints += quesionsNbr; // Add maximum possible points for each candidate
                    } else if (!testStats[assessmentName] && result.totalRanges) {
                        testStats[assessmentName] = { totalPoints: 0, maxPoints: 0 };
                        testStats[assessmentName].totalPoints += totalPoints ? result.totalPoints : (result.totalRanges * result.result.quesionsNbr) / 100;
                        testStats[assessmentName].maxPoints += quesionsNbr; // Add maximum possible points for each candidate
                    }
                });

                // Step 3: Convert grouped data into an array and sort by totalPoints
                const sortedTests = Object.entries(testStats)
                    .map(([testName, stats]) => ({
                        testName,
                        totalPoints: stats.totalPoints,
                        maxPoints: stats.maxPoints,
                    }))
                    .sort((a, b) => b.totalPoints - a.totalPoints);

                // Step 4: Select the top 3 tests
                this.Best3Tests = sortedTests.slice(0, 3);
                // Debugging: Output the top 3 tests
                console.log("Best3Tests", this.Best3Tests);
            }
        },
        /////////////////////////////////////////////////////////////////////////////////////////////

        ///////////////////////FUNCTION TO CALCULATE THE BEST 3 CANDIDATE BASED ON TOTAL POINTS///////////////////////////////////////////////////
        calculateBestTests() {
            const testCount = {};

            // Count each test
            this.filtredCandidates?.forEach((candidate) => {
                candidate.results.forEach((result) => {
                    if (result.category != "Custom") {
                        const testName = result.assessmentName;
                        testCount[testName] = (testCount[testName] || 0) + 1;
                    }
                });
            });

            const sortedTests = Object.entries(testCount)
                .sort((a, b) => b[1] - a[1]) // Sort by participation count
                .slice(0, 3);
            this.BestTests = sortedTests;
        },

        calculateTotalPoints(candidate) {
            let totalPoints = 0;
            let bestPoints = 0;
            candidate.results.forEach((result) => {
                if (result.totalPoints) {
                    totalPoints += result.totalPoints;
                    bestPoints += result.quesionsNbr;
                }
            });
            if (candidate.results.length) {
                totalPoints = totalPoints / candidate.results.length;
                bestPoints = bestPoints / candidate.results.length;
            }
            totalPoints = (totalPoints * 10) / bestPoints;
            return totalPoints.toFixed(2);
        },

        calculateTotalPoints1(candidate) {
            return this.calculateTotalPoints(candidate);
        },

        truncateName(name) {
            return name.length > 15 ? name.slice(0, 15) + "..." : name;
        },

        truncateSkill(name) {
            return name.length > 15 ? name.slice(0, 15) + "..." : name;
        },

        getCandidatesWithAvgSorted(candidatesArray) {
            return candidatesArray
                .map((candidate) => {
                    const avg = this.calculateTotalPoints1(candidate || []);
                    return {
                        name: candidate.name,
                        averageScore: avg,
                    };
                })
                .sort((a, b) => b.averageScore - a.averageScore);
        },

        navigateToDashBoard(project) {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/boards`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToDetailBoard(project) {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/Details`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToCheatBoard(project) {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/CheatTab`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToVideoBoard(project) {
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/DashVideo`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        async getCandidateInv() {
            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/candidates/candidateInfo`,
                headers: {
                    "Content-Type": "application/json",
                },
                params: {
                    email: this.candidateEmail,
                    projectId: this.projectId,
                },
                withCredentials: true,
            };

            axios
                .request(config)
                .then((response) => {
                    this.candidateInv = response.data.CandidateInvitation;
                    this.Store.candidateInfoAct = response.data.candidateInfo;
                    this.Store.candidateActivity = response.data.candidateScore;
                    this.candidateInfo = response.data.candidateInfo;
                    this.candidate = response.data.candidateScore;
                    this.candidateInterpretations = response.data.interpretations;
                    this.candidateRating = response.data.candidateRating;
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        startIntro() {
            // Initialize Intro.js
            const intro = introJs();

            // Set Intro.js options
            intro.setOptions({
                steps: [
                    {
                        element: this.$refs.targetElement3,
                        // <img src="${this.imagePath}" alt="Image Description">
                        intro: `<h3>Invite candidate</h3>
                        <p>Invite all candidates you want to assess their skills.</p>`,
                        position: "bottom",
                    },

                    // Add more steps as needed
                ],
            });

            // Start the introduction
            intro.start();
        },

        toggleProjectMenu() {
            this.showMenu = !this.showMenu;
        },

        toggleRatings() {
            this.viewAllRatings = !this.viewAllRatings;
        },

        toggleEmail() {
            this.showEmail = !this.showEmail;
        },

        getCandidatesPassed() {
            let candidatesPassed = this.candidates.filter((candidate) => {
                let score = 0;
                candidate.results.forEach((element) => {
                    score += (element.totalPoints * 100) / element.quesionsNbr;
                });
                const averageScore = score / candidate.results.length;
                // const roundedScore = (averageScore * 100).toFixed(2); // Convert to percentage
                return averageScore > this.project.min_score; // Filter candidates with scores over 80%
            });
            return candidatesPassed.length > 0 ? candidatesPassed.length : 0;
        },
        getSuccessRate() {
            if (this.candidates.length > 0) {
                const candidatesAbove80Percent = this.candidates.filter((candidate) => {
                    let score = 0;
                    candidate.results.forEach((element) => {
                        score += (element.totalPoints * 100) / element.quesionsNbr;
                    });
                    const averageScore = score / candidate.results.length;
                    // const roundedScore = (averageScore * 100).toFixed(2); // Convert to percentage

                    return averageScore > this.project.min_score; // Filter candidates with scores over 80%
                });

                const numberOfCandidatesAbove80Percent = candidatesAbove80Percent.length;
                const totalCandidates = this.candidates.length;

                const percentageAbove80Percent = (numberOfCandidatesAbove80Percent / totalCandidates) * 100;
                return percentageAbove80Percent.toFixed(2);
            } else return 0;
        },

        navigateToPreview() {
            const url = this.$router.resolve({
                path: "/preview-project",
                query: { id: this.project._id },
            }).href;

            // Open the route in a new tab
            window.open(url, "_blank");
        },
    },
    computed: {
        filteredCandidates() {
            // Example filtering logic based on selectedRank and selectedStatus
            return this.candidates.filter((candidate) => {
                if (this.selectedStatus === "Passed" && candidate.rating < 1) {
                    return false;
                }
                if (this.selectedStatus === "Not Passed" && candidate.rating >= 3) {
                    return false;
                }
                return true;
            });
        },
        filteredAssessmentsLength() {
            if (!this.project || !this.project.assessments) {
                return 0;
            }

            const hasCustomAssessment = this.project.assessments.some((assessment) => assessment.category === "Custom");

            return hasCustomAssessment ? this.project?.assessments?.length - 1 : this.project?.assessments?.length;
        },
        filteredAssessments() {
            return this.project.assessments.filter((assessment) => assessment.category !== "Custom");
        },

        customAssessments() {
            return this.project.assessments.find((assessment) => assessment.category == "Custom");
        },
        totalDuration: {
            get() {
                if (this.project?.assessments?.length > 0) {
                    const totalSeconds = this.filteredAssessments.reduce((acc, assessment) => {
                        if (assessment?.questions_nbr > 25) {
                            return acc + 20 * 35;
                        } else {
                            return acc + assessment?.questions_nbr * 35;
                        }
                    }, 0);

                    const minutes = Math.floor(totalSeconds / 60);
                    const seconds = totalSeconds % 60;
                    return `${minutes}:${seconds}`;
                } else {
                    return { minutes: 0, seconds: 0 };
                }
            },
        },
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    async mounted() {
        // this.getCandidateInv();
        const headers = {
            "Content-Type": "application/json",
            withCredentials: true,
        };

        await axios
            .get(`${BASE_URL}/tooltip/get`, {
                headers,
                withCredentials: true,
            })
            .then((res) => {
                //alert(res.data);
                if (res.data != 2) {
                    setTimeout(() => {
                        this.startIntro();
                    }, 1500);

                    axios.get(`${BASE_URL}/tooltip/post2`, {
                        headers,
                        withCredentials: true,
                    });
                }
            })
            .catch((e) => {
                console.log(e);
            });

        this.id = this.$route.query.id;

        await this.Store.fetchProjects();
        // this.Store.fetchCandidates();
        this.Store.getCompanyAssessments();
        this.Store.fetchInvitations();
        this.project = this.Store.projects[0]?._id;

        let config = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/projects/projectData`,
            headers: {
                "Content-Type": "application/json",
            },
            params: {
                id: this.id,
            },
            withCredentials: true,
        };

        await axios
            .request(config)
            .then((response) => {
                console.log("response");
                console.log(response.data.project.assessments);
                this.allTests = response.data.project.assessments;
                console.log("response");
                this.invitations = response.data.invited;
                this.candidates = response.data.candidates;
                this.candidatesLocation = response.data.countryCounts;
                /*this.candidatesLocation = JSON.parse(JSON.stringify(this.candidatesLocation));
                this.candidatesLocation = Object.entries(this.candidatesLocation).map(([name, value]) => ({
  name,
  value
}));*/
                this.filtredCandidates = response.data.candidates;
                console.log("this.filtredCandidates");
                console.log(this.filtredCandidates);
                console.log("this.filtredCandidates");

                this.sortedCandidates = this.getCandidatesWithAvgSorted(this.filtredCandidates);
                console.log("this.sortedCandidates");
                console.log(this.sortedCandidates);
                console.log("this.sortedCandidates");
                this.candidatesLocation = JSON.parse(JSON.stringify(this.candidatesLocation));

                // Calculate total
                const total = Object.values(this.candidatesLocation).reduce((sum, val) => sum + val, 0);

                // Build final list with percentage
                this.candidatesLocation = Object.entries(this.candidatesLocation).map(([name, value]) => ({
                    name,
                    value,
                    percentage: ((value * 100) / total).toFixed(2),
                }));

                console.log("this.filtredCandidates");
                console.log(this.candidatesLocation);
                console.log("this.filtredCandidates");

                this.filtredCandidates?.sort((a, b) => this.calculateTotalPoints(a) - this.calculateTotalPoints(b));
                this.behindSchedule = this.invitations.length - this.candidates.length;
                this.threeCandidates = this.candidates.slice(0, 3);
                this.project = response.data.project;
                this.compitionRate = (this.candidates.length / this.invitations.length).toFixed(2);
                this.graphData = response.data.chartData;
                this.histogramData = [
                    {
                        label: "Invited",
                        value: this.invitations.length,
                    },
                    {
                        label: "Attempted",
                        value: this.candidates.length,
                    },
                    {
                        label: "Passed",
                        value: this.getCandidatesPassed(),
                    },
                ];
                this.isLoading = false;
                // this.yourAssessment = response.data.assessments;
                // this.score = response.data.score;
            })
            .catch((error) => {
                console.log(error);
            });
        this.diskData = this.Store.createDistributionData(this.id);

        let config2 = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/AssessmentTest/candidateRating/${this.id}`,
            headers: {},
            withCredentials: true,
        };
        axios
            .request(config2)
            .then((response) => {
                this.candidatesRating = response.data;
                this.ThreeRatings = this.candidatesRating.slice(0, 3);
            })
            .catch((error) => {
                console.log({ error });
            });
        this.calculateBestTests();
        this.getTop3Tests();

        this.bestAllSkills = this.getTopTestsByCategoryWithPerc();
        console.log("this.bestAllSkills");
        console.log(this.bestAllSkills);
        console.log("this.bestAllSkills");

        this.bestTechSkills = this.bestAllSkills["Hard Skills"];
        this.bestSoftSkills = this.bestAllSkills["Soft Skills"]; //?.filter((test) => test.totalPoints);
        this.bestPersSkills = this.bestAllSkills["Personality"]; //?.filter((test) => test.totalPoints);
        this.bestPsychSkills = this.bestAllSkills["Psychometrics"]; //?.filter((test) => test.totalPoints);
        this.SortedCandidates = this.filtredCandidates[0]?.results.filter((test) => test.category);
    },
};
</script>

<style scoped lang="scss">
@import "tailwindcss/tailwind.css";

.cursor-pointer {
    cursor: pointer;
}
.hover\:bg-blue-50:hover {
    background-color: #eff6ff;
}

.board-container {
    display: flex;
    flex-direction: column;
    padding-top: 20px;
}

.menuBtn {
    width: 60px;
    height: 50px;

    border-radius: 6px;
}

.projData {
    font-size: 20px;
    font-weight: 700;
}

/*----------------------------------------------------------------*/

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    /* margin-top: 48px; */
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 2px solid #edeff2;
}

.nav-links span {
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: black;
}

.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-family: "Roboto";
    font-weight: 700;
    font-size: 14px;
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links .nav-icon {
    font-size: 20px;
}

.nav-links img {
    margin-right: 12px;
}

.nextStep {
    width: 100px;
    height: 50px;
    color: white;
    font-weight: 500;
    background: #2196f3;
    border-radius: 6px;

    &:hover {
        opacity: 0.85;
    }
}

.loader {
    width: 100%;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 2px solid #edeff2;
    margin-bottom: 25px;
}
.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-family: "Roboto";
    font-weight: 700;
    font-size: 14px;
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links .nav-icon {
    font-size: 20px;
}

.nav-links img {
    margin-right: 12px;
}

.nav-links a::after {
    content: "";
    background: #2196f3;
    position: absolute;
    bottom: 2.4px;
    left: 0;
    width: 0;
    height: 2px;
    transition: width 0.4s ease-in-out;
    border-radius: 25px;
}

.nav-links > a:hover::after {
    width: 100%;
    color: #00aef0;
}

.nav-links a.active::after {
    background: #00aef0;
    width: 100%;
}

.nav-links a.active * {
    color: #00aef0;
}
.disabled {
    pointer-events: none;

    span {
        opacity: 0.5;
    }
}
.nav-links span:hover {
    color: #00aef0;
}
</style>
