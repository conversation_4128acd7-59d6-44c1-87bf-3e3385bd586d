<template>
    <ToastNotification :message="message" :isVisible="isVisible" :bgColor="bgc" />
    <div class="email-modal-overlay fixed inset-0 z-50 flex flex-col items-center justify-center bg-[#0000003f]" v-if="showEmail">
        <div class="fixed inset-0" @click="toggleEmail"></div>

        <div class="email-modal-content relative rounded-md bg-white w-[700px] p-8 space-y-6 shadow-xl">
            <div class="flex justify-between items-center w-full mb-8">
                <h2 class="text-2xl font-semibold text-gray-800">
                    {{ $t("Invite Co-worker") }}
                </h2>

                <button class="rounded-full" @click="toggleEmail">
                    <font-awesome-icon :icon="['fas', 'xmark']" class="h-5 w-5 text-gray-400 flex-shrink-0" />
                </button>
            </div>

            <div class="flex gap-4 items-center justify-center">
                <div class="email-input-container w-96 flex items-center gap-3 border rounded-md px-4 py-3">
                    <font-awesome-icon :icon="['far', 'envelope']" class="h-5 w-5 text-gray-400 flex-shrink-0" />
                    <input type="email" v-model="email" :placeholder="$t('Co-worker email')" class="w-full outline-none placeholder-gray-400" />
                </div>

                <Popper :content="$t('Send Email Invitation')" placement="top" :hover="true">
                    <button class="send-button px-6 py-3 bg-NeonBlue text-white font-medium rounded-md hover:bg-opacity-85 transition-opacity" @click="sendInvitationEmail">
                        {{ $t("Send") }}
                    </button>
                </Popper>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { BASE_URL } from "@/constants";
import ToastNotification from "@/components/ToastNotification";
import { useStore } from "../store/index";
export default {
    name: "InviteTeammate",
    props: ["showEmail", "toggleEmail"],
    components: { ToastNotification },
    data() {
        return {
            email: "",
            isVisible: false,
            message: "",
            bgc: "",
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    async mounted() {},
    methods: {
        sendInvitationEmail() {
            if (this.email) {
                let data = JSON.stringify({
                    email: this.email,
                    company_name: this.Store.company_name,
                    company_ID: this.Store.company_id,
                });

                let config = {
                    method: "post",
                    maxBodyLength: Infinity,
                    url: `${BASE_URL}/company/inviteTeammate`,
                    headers: {
                        "Content-Type": "application/json",
                    },
                    data: data,
                    withCredentials: true,
                };

                axios
                    .request(config)
                    .then((response) => {
                        this.toggleEmail();
                        this.message = response.data.message;
                        this.bgc = "success";
                        this.email = "";
                        this.isVisible = true;
                        setTimeout(() => {
                            this.isVisible = false;
                        }, 5000);
                    })
                    .catch((error) => {
                        this.message = "please enter a valid email address";
                        this.bgc = "red";
                        this.email = "";
                        this.isVisible = true;
                        setTimeout(() => {
                            this.isVisible = false;
                        }, 5000);
                        console.log(error);
                    });
            } else {
                alert("Enter a valid email");
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.email-modal-overlay {
    .email-modal-content {
        box-shadow: 0px 0px 10px 5px rgba(0, 0, 0, 0.04);

        .email-input-container {
            border-color: #e5e5ef;
            transition: border-color 0.2s ease;

            &:focus-within {
                border-color: #3b82f6;
                border-width: 2px;
            }
        }

        .send-button {
            min-width: 7rem;
        }
    }
}
</style>
