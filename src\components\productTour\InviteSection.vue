<template>
    <section
        class="invite flex flex-col gap-5 lg:flex-row justify-between py-8 px-4 lg:px-20 items-center my-4"
        style="
            background-color: hsla(0, 0%, 100%, 1);
            background-image: radial-gradient(at 0% 0%, hsla(242, 100%, 70%, 1) 0px, transparent 50%), radial-gradient(at 100% 100%, hsla(206, 89%, 54%, 1) 0px, transparent 50%);
        "
    >
        <div class="w-full screen lg:w-[45%]">
            <img loading="lazy" decoding="async" src="../../assets/Images/invite_screen.png" alt="" class="w-full aspect-auto" />
        </div>
        <div class="w-full lg:w-1/2">
            <h1 class="text-2xl lg:text-5xl font-bold text-slate-700 text-center lg:text-left w-full px-4">{{ $t("2. Invite candidates you way.") }}</h1>
            <p class="text-sm lg:text-lg font-light w-full text-left text-slate-700 my-4 px-4">
                {{ $t("invite.subtitle") }}
            </p>
        </div>
    </section>
</template>

<script>
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
gsap.registerPlugin(ScrollTrigger);
export default {
    name: "InviteSection",
    mounted() {
        gsap.utils.toArray(".inviteLetter").forEach((letter, i) => {
            gsap.to(letter, {
                x: -(i * 200),
                y: -(i * 150),
                rotate: -45,
                stagger: 3,
                duration: 2,
                ease: "ease",
                scrollTrigger: {
                    trigger: ".invite",
                    start: "top 50%",
                    scrub: true,
                },
            });
        });
    },
};
</script>

<style lang="scss" scoped>
.screen {
    @media (max-width: 1023px) {
        display: none;
    }
}
</style>
