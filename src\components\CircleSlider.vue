<template>
    <div ref="observerRef" class="circular-slider" :class="{ 'in-view': inView }">
        <svg viewBox="0 0 36 36">
            <!-- Background -->
            <path
                class="circle-bg"
                d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
            />

            <!-- Progress -->
            <path
                class="circle"
                :stroke="currentColor"
                :style="{
                    strokeDasharray: `${animatedValue}, 100`,
                }"
                d="M18 2.0845
            a 15.9155 15.9155 0 0 1 0 31.831
            a 15.9155 15.9155 0 0 1 0 -31.831"
            />

            <!-- Count Up Text -->
            <text x="18" y="20.35" class="percentage">{{ displayValue }}%</text>
        </svg>
    </div>
</template>

<script>
export default {
    name: "CircleSlider",
    props: {
        value: { type: Number, required: true },
    },
    data() {
        return {
            animatedValue: 0,
            displayValue: 0,
            inView: false,
        };
    },
    computed: {
        currentColor() {
            if (this.animatedValue < 33) return "#EA4745";
            if (this.animatedValue < 66) return "#FFA500";
            return "#46A997";
        },
    },
    mounted() {
        this.observeVisibility();
    },
    methods: {
        observeVisibility() {
            const observer = new IntersectionObserver(
                ([entry]) => {
                    if (entry.isIntersecting && !this.inView) {
                        this.inView = true;
                        this.animateValue();
                    }
                },
                { threshold: 0.6 },
            );
            observer.observe(this.$refs.observerRef);
        },
        animateValue() {
            const duration = 1500;
            const frameRate = 1000 / 60;
            const totalFrames = Math.round(duration / frameRate);
            let frame = 0;

            const counter = setInterval(() => {
                frame++;
                const progress = frame / totalFrames;
                this.animatedValue = parseFloat((this.value * progress).toFixed(2));
                this.displayValue = Math.round(this.animatedValue);

                if (frame >= totalFrames) {
                    clearInterval(counter);
                    this.animatedValue = this.value;
                    this.displayValue = Math.round(this.value);
                }
            }, frameRate);
        },
    },
};
</script>

<style scoped>
.circular-slider {
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.6s ease;
}
.circular-slider.in-view {
    opacity: 1;
    transform: scale(1);
}
.circular-slider svg {
    width: 100%;
    height: auto;
}
.circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}
.circle {
    fill: none;
    stroke-width: 2.8;
    stroke-linecap: round;
    transition:
        stroke-dasharray 0.5s,
        stroke 0.5s;
}
.percentage {
    fill: #000;
    font-family: "Arial", sans-serif;
    font-size: 9px;
    font-weight: bold;
    text-anchor: middle;
}
</style>
