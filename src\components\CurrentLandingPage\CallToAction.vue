<template>
    <section class="bg-gray-50 py-8 md:py-12 lg:py-20 border-t border-b border-gray-200 w-full">
        <div class="text-center px-2 sm:px-0 lg:px-6">
            <!-- Animated Logo -->
            <div class="mb-12">
                <!--<img src="@/assets/go_logo_blue.svg" alt="" class="h-16 w-auto mx-auto" />-->
                <img :src="logoSrc" class="w-auto mx-auto" :class="geolocationStore.country === 'DZ' ? 'h-24' : 'h-16'" :alt="logoAlt" />
            </div>

            <!-- Content -->
            <h2 class="text-xl md:text-4xl font-bold text-gray-900 mb-6 leading-tight">{{ $t("Unlock the full potential of your workforce") }}</h2>

            <p class="text-lg md:text-xl text-gray-600 max-w-3xl mx-auto mb-8 leading-relaxed">
                {{ $t("Learn how GO PLATFORM can power digital transformation and produce measurable results across your enterprise.") }}
            </p>

            <!-- CTA Button -->
            <router-link
                to="/register"
                class="inline-flex items-center justify-center w-auto sm:w-full md:w-full lg:w-auto px-8 py-3 text-lg font-bold text-white transition-all duration-200 bg-[#2196f3] border-2 border-transparent rounded-md font-pj hover:bg-blue-500"
                role="button"
            >
                {{ $t("Start for free") }}
            </router-link>
        </div>
    </section>
</template>

<script setup>
import { computed } from "vue";
import { useGeolocationStore } from "@/store/geolocation";

const geolocationStore = useGeolocationStore();

const logoSrc = computed(() => {
    return geolocationStore.country === "DZ" ? require("@/assets/GoProfiling-logo.svg") : require("@/assets/logo.svg");
});

const logoAlt = computed(() => {
    return geolocationStore.country === "DZ" ? "Go Profiling & Testing Logo" : "Go Platform Logo";
});
</script>
