<template>
    <div ref="menuContainer" class="flex flex-col justify-center relative items-center">
        <button @click="toggleShow" class="anchor">
            <font-awesome-icon :icon="['fas', 'language']" class="text-base text-slate-700" />
            {{ this.$i18n.locale.toUpperCase() }}
        </button>
        <div v-if="showMenu" class="menu">
            <div class="menu-item" v-for="language in languages" :key="language.local" @click="SwitchLocale(language.local)">
                <img loading="lazy" decoding="async" :src="require(`@/assets/Images/${language.flag}`)" :alt="language.local + ' flag'" class="w-8 h-6" />
                {{ language.language }}
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "SwitchLanguage",
    data() {
        return {
            showMenu: false,
            languages: [
                {
                    language: "English",
                    local: "en",
                    flag: "united-kingdom_flag.svg",
                },
                {
                    language: "French",
                    local: "fr",
                    flag: "france_flag.svg",
                },
                {
                    language: "Arabic",
                    local: "ar",
                    flag: "algeria_svg.svg",
                },
            ],
        };
    },
    props: {
        onClick: { type: Function },
    },
    methods: {
        toggleShow: function () {
            this.showMenu = !this.showMenu;
            if (this.showMenu) {
                document.addEventListener("click", this.outsideClick);
            } else {
                document.removeEventListener("click", this.outsideClick);
            }
        },
        outsideClick(event) {
            if (!this.$refs.menuContainer.contains(event.target)) {
                this.showMenu = false;
                document.removeEventListener("click", this.outsideClick);
            }
        },
        SwitchLocale(lang) {
            if (this.$i18n.locale !== lang) {
                this.$i18n.locale = lang;
                localStorage.setItem("locale", lang);
                const newPath = `/${lang}${window.location.pathname.replace(/^\/(en|fr|ar)/, "")}`;
                window.location.pathname = newPath;
            }
            this.toggleShow();
        },
    },
};
</script>

<style lang="scss" scoped>
.anchor {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    transition:
        color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
}

.anchor:hover {
    cursor: pointer;
}

.menu {
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 0.25rem;
    color: #263238;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    list-style: none;
    margin: 0.125rem 0;
    margin-right: 1rem;
    padding: 0.5rem 0rem;
    position: absolute;
    top: 0;
    right: 100%;
    transform: translateX(50%);
    text-align: left;
    z-index: 5;
    box-shadow: 0 0 4px 2px rgba(0, 0, 0, 0.05);
}

.menu-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    color: #263238;
    padding: 0.25rem 1.5rem;
    font-size: 14px;
    transition:
        color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
}

.menu-item:hover {
    background-color: #f4f6f6;
    cursor: pointer;
}

span {
    font-weight: bold;
    font-size: 1.25rem;
}
</style>
