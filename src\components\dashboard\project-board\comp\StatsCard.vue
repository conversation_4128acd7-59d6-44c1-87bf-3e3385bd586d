<template>
    <div ref="card" class="bg-custom text-center px-6 py-4 rounded-lg opacity-0 translate-y-2 transition-all duration-700 ease-out" :class="{ 'opacity-100 translate-y-0': isVisible }">
        <div class="flex items-center justify-center gap-6">
            <div>
                <img v-if="iconIndex == 0" src="../../../../assets/applicant.svg" alt="icon" class="w-11 h-11" />
                <img v-else-if="iconIndex == 1" src="../../../../assets/assess.svg" alt="icon" class="w-11 h-11" />
                <img v-else-if="iconIndex == 2" src="../../../../assets/successRate.svg" alt="icon" class="w-11 h-11" />
                <img v-else-if="iconIndex == 3" src="../../../../assets/completion.svg" alt="icon" class="w-11 h-11" />
            </div>
            <div class="flex flex-col justify-between gap-2 items-center">
                <h1 class="text-[#a3aed0] font-semibold text-xl">{{ $t(title) }}</h1>
                <h2 v-if="isLoading" class="text-3xl font-bold text-gray-400">-</h2>
                <h2 v-else-if="typeof formattedDisplayCount === 'number'" class="text-3xl font-bold text-black">
                    {{ formattedDisplayCount }}{{ title === "Success Rate" || title === "Completion" ? "%" : "" }}
                </h2>
                <h2 v-else class="text-3xl font-bold text-black">{{ formattedDisplayCount }}</h2>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: "StatsCard",
    props: {
        title: {
            type: String,
            required: true,
        },
        iconIndex: {
            type: Number,
            default: 0,
        },

        value: {
            type: String,
            default: "0",
        },
    },
    data() {
        return {
            displayedCount: null,
            isVisible: false,
            isLoading: true,
            observer: null,
            icons: [
                "../../../../assets/Images/icons/applicant.png",
                "../../../../assets/Images/icons/applicant.png",
                "../../../../assets/Images/icons/applicant.png",
                "../../../../assets/Images/icons/applicant.png",
            ],
        };
    },
    computed: {
        iconSrc() {
            return this.icons[this.iconIndex] || this.icons[0];
        },
        formattedDisplayCount() {
            if (typeof this.displayedCount === "number" && isNaN(this.displayedCount)) {
                return "0";
            }
            return this.displayedCount;
        },
    },
    mounted() {
        this.createObserver();
    },
    beforeUnmount() {
        if (this.observer) {
            this.observer.disconnect();
        }
    },
    watch: {
        value: {
            immediate: true,
            handler(newVal) {
                if (newVal === undefined || newVal === null) {
                    this.isLoading = true;
                    return;
                }
                this.isLoading = false;
                this.animateCount();
            },
        },
    },
    methods: {
        createObserver() {
            this.observer = new IntersectionObserver(
                ([entry]) => {
                    if (entry.isIntersecting && !this.isVisible) {
                        this.isVisible = true;
                        this.animateCount();
                    }
                },
                { threshold: 0.4 },
            );
            this.observer.observe(this.$refs.card);
        },

        animateCount() {
            // If value is not a valid number, just set it directly
            if (isNaN(parseFloat(this.value))) {
                this.displayedCount = this.value;
                return;
            }

            const duration = 1000;
            const frameRate = 1000 / 60;
            const totalFrames = Math.round(duration / frameRate);
            let currentFrame = 0;
            const targetValue = parseFloat(this.value);
            const startValue = 0;

            const counter = setInterval(() => {
                currentFrame++;
                const progress = this.easeOutCubic(currentFrame / totalFrames);
                this.displayedCount = Math.floor(startValue + (targetValue - startValue) * progress);

                if (currentFrame >= totalFrames) {
                    this.displayedCount = targetValue;
                    clearInterval(counter);
                }
            }, frameRate);
        },

        easeOutCubic(t) {
            return 1 - Math.pow(1 - t, 3);
        },
    },
};
</script>
<style scoped>
.bg-custom {
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease;
}
.bg-custom:hover {
    transform: scale(1.02);
}
</style>
