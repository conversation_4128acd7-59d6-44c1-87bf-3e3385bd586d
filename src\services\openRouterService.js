import axios from 'axios';
import { OPENROUTER_CONFIG } from '@/constants';

/**
 * OpenRouter API Service for Llama integration
 * Handles job description processing and skill extraction
 */
class OpenRouterService {
    constructor() {
        this.baseURL = OPENROUTER_CONFIG.BASE_URL;
        this.model = OPENROUTER_CONFIG.MODEL;
        // Using a dummy API key for demonstration - in production, this should be from environment variables
        this.apiKey = process.env.VUE_APP_OPENROUTER_API_KEY || 'sk-or-v1-dummy-key-for-demo';
        this.siteName = OPENROUTER_CONFIG.SITE_NAME;
        this.siteUrl = OPENROUTER_CONFIG.SITE_URL;
    }

    /**
     * Extract skills from job description using <PERSON>lama via OpenRouter
     * @param {string} jobDescription - The job description text
     * @returns {Promise<Array>} - Array of extracted skills
     */
    async extractSkillsFromJobDescription(jobDescription) {
        try {
            const prompt = this.createSkillExtractionPrompt(jobDescription);
            
            const response = await axios.post(
                `${this.baseURL}/chat/completions`,
                {
                    model: this.model,
                    messages: [
                        {
                            role: 'user',
                            content: prompt
                        }
                    ],
                    max_tokens: 1000,
                    temperature: 0.3,
                    top_p: 0.9,
                    stream: false
                },
                {
                    headers: {
                        'Authorization': `Bearer ${this.apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': this.siteUrl,
                        'X-Title': this.siteName
                    },
                    timeout: 30000 // 30 second timeout
                }
            );

            return this.parseSkillsResponse(response.data);
        } catch (error) {
            console.error('Error extracting skills from job description:', error);
            throw this.handleApiError(error);
        }
    }

    /**
     * Create a structured prompt for skill extraction
     * @param {string} jobDescription - The job description text
     * @returns {string} - Formatted prompt for the AI model
     */
    createSkillExtractionPrompt(jobDescription) {
        return `
            You are a professional recruiter and skills analyst. Analyze the following job description and extract relevant technical and soft skills that would be important for this role.

            Job Description:
            """
            ${jobDescription}
            """

            Please extract and categorize skills into the following format. Return ONLY a valid JSON object with no additional text:

            {
                "technical_skills": [
                    "skill1",
                    "skill2",
                    "skill3"
                ],
                "soft_skills": [
                    "skill1",
                    "skill2",
                    "skill3"
                ],
                "tools_and_technologies": [
                    "tool1",
                    "tool2",
                    "tool3"
                ],
                "certifications": [
                    "cert1",
                    "cert2"
                ]
            }

            Guidelines:
            - Extract 5-15 skills per category (if applicable)
            - Focus on skills explicitly mentioned or strongly implied
            - Use standard industry terminology
            - Avoid duplicates across categories
            - If a category has no relevant skills, use an empty array
            - Return only the JSON object, no explanations or additional text
        `.trim();
    }

    /**
     * Parse the API response and extract skills
     * @param {Object} responseData - Response from OpenRouter API
     * @returns {Array} - Formatted skills array
     */
    parseSkillsResponse(responseData) {
        try {
            if (!responseData.choices || !responseData.choices[0] || !responseData.choices[0].message) {
                throw new Error('Invalid response format from OpenRouter API');
            }

            const content = responseData.choices[0].message.content.trim();
            
            // Try to parse JSON response
            let skillsData;
            try {
                skillsData = JSON.parse(content);
            } catch (parseError) {
                // If JSON parsing fails, try to extract JSON from the response
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    skillsData = JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('Could not parse skills from AI response');
                }
            }

            // Format skills for the application
            return this.formatSkillsForApplication(skillsData);
        } catch (error) {
            console.error('Error parsing skills response:', error);
            throw new Error('Failed to parse skills from AI response');
        }
    }

    /**
     * Format extracted skills for application use
     * @param {Object} skillsData - Parsed skills data from AI
     * @returns {Array} - Formatted skills array
     */
    formatSkillsForApplication(skillsData) {
        const formattedSkills = [];

        // Add technical skills
        if (skillsData.technical_skills && Array.isArray(skillsData.technical_skills)) {
            skillsData.technical_skills.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Technical Skills',
                    type: 'technical',
                    selected: false
                });
            });
        }

        // Add soft skills
        if (skillsData.soft_skills && Array.isArray(skillsData.soft_skills)) {
            skillsData.soft_skills.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Soft Skills',
                    type: 'soft',
                    selected: false
                });
            });
        }

        // Add tools and technologies
        if (skillsData.tools_and_technologies && Array.isArray(skillsData.tools_and_technologies)) {
            skillsData.tools_and_technologies.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Tools & Technologies',
                    type: 'tools',
                    selected: false
                });
            });
        }

        // Add certifications
        if (skillsData.certifications && Array.isArray(skillsData.certifications)) {
            skillsData.certifications.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Certifications',
                    type: 'certification',
                    selected: false
                });
            });
        }

        return formattedSkills;
    }

    /**
     * Handle API errors and provide user-friendly messages
     * @param {Error} error - The error object
     * @returns {Error} - Formatted error
     */
    handleApiError(error) {
        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;

            switch (status) {
                case 401:
                    return new Error('Invalid API key. Please check your OpenRouter configuration.');
                case 402:
                    return new Error('Insufficient credits. Please check your OpenRouter account balance.');
                case 429:
                    return new Error('Rate limit exceeded. Please try again in a few minutes.');
                case 500:
                    return new Error('OpenRouter service is temporarily unavailable. Please try again later.');
                default:
                    return new Error(data?.error?.message || `API request failed with status ${status}`);
            }
        } else if (error.request) {
            return new Error('Network error. Please check your internet connection and try again.');
        } else {
            return new Error(error.message || 'An unexpected error occurred while processing the job description.');
        }
    }

    /**
     * Validate job description before processing
     * @param {string} jobDescription - The job description text
     * @returns {boolean} - Whether the job description is valid
     */
    validateJobDescription(jobDescription) {
        if (!jobDescription || typeof jobDescription !== 'string') {
            return false;
        }

        const trimmed = jobDescription.trim();
        
        // Check minimum length (at least 50 characters)
        if (trimmed.length < 50) {
            return false;
        }

        // Check maximum length (10,000 characters to avoid token limits)
        if (trimmed.length > 10000) {
            return false;
        }

        return true;
    }

    /**
     * Get validation error message for job description
     * @param {string} jobDescription - The job description text
     * @returns {string|null} - Error message or null if valid
     */
    getValidationError(jobDescription) {
        if (!jobDescription || typeof jobDescription !== 'string') {
            return 'Job description is required.';
        }

        const trimmed = jobDescription.trim();
        
        if (trimmed.length < 50) {
            return 'Job description must be at least 50 characters long.';
        }

        if (trimmed.length > 10000) {
            return 'Job description must be less than 10,000 characters.';
        }

        return null;
    }
}

// Export singleton instance
export default new OpenRouterService();
