import axios from 'axios';
import { OPENROUTER_CONFIG } from '@/constants';

/**
 * OpenRouter API Service for Llama integration
 * Handles job description processing and skill extraction
 */
class OpenRouterService {
    constructor() {
        this.baseURL = OPENROUTER_CONFIG.BASE_URL;
        this.model = OPENROUTER_CONFIG.MODEL;
        // Load API key from environment variables
        this.apiKey = process.env.VUE_APP_OPENROUTER_API_KEY;
        this.siteName = OPENROUTER_CONFIG.SITE_NAME;
        this.siteUrl = OPENROUTER_CONFIG.SITE_URL;

        // Debug logging for environment variables
        console.log('Environment check:');
        console.log('VUE_APP_OPENROUTER_API_KEY exists:', !!process.env.VUE_APP_OPENROUTER_API_KEY);
        console.log('API Key loaded (first 20 chars):', this.apiKey ? this.apiKey.substring(0, 20) + '...' : 'NOT LOADED');

        if (!this.apiKey) {
            console.error('OpenRouter API key not found in environment variables!');
        }
    }

    /**
     * Extract skills from job description using Llama via OpenRouter
     * @param {string} jobDescription - The job description text
     * @returns {Promise<Array>} - Array of extracted skills
     */
    async extractSkillsFromJobDescription(jobDescription) {
        // Validate job description
        if (!this.validateJobDescription(jobDescription)) {
            throw new Error('Invalid job description. Please provide a job description with at least 50 characters.');
        }

        // Validate API key
        if (!this.apiKey) {
            throw new Error('OpenRouter API key is missing. Please check your environment configuration.');
        }

        if (!this.apiKey.startsWith('sk-or-v1-')) {
            throw new Error('OpenRouter API key format is invalid. It should start with "sk-or-v1-".');
        }

        try {
            const prompt = this.createSkillExtractionPrompt(jobDescription);

            // Debug logging
            console.log('API Key (first 20 chars):', this.apiKey?.substring(0, 20) + '...');
            console.log('Base URL:', this.baseURL);
            console.log('Model:', this.model);
            console.log('Site Name:', this.siteName);
            console.log('Site URL:', this.siteUrl);

            const requestData = {
                model: this.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                max_tokens: 1000,
                temperature: 0.3,
                top_p: 0.9,
                stream: false
            };

            const requestConfig = {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': this.siteUrl,
                    'X-Title': this.siteName
                },
                timeout: 30000 // 30 second timeout
            };

            console.log('Request URL:', `${this.baseURL}/chat/completions`);
            console.log('Request headers:', requestConfig.headers);

            const response = await axios.post(
                `${this.baseURL}/chat/completions`,
                requestData,
                requestConfig
            );

            return this.parseSkillsResponse(response.data);
        } catch (error) {
            console.error('Error extracting skills from job description:', error);
            throw this.handleApiError(error);
        }
    }

    /**
     * Create a structured prompt for skill extraction
     * @param {string} jobDescription - The job description text
     * @returns {string} - Formatted prompt for the AI model
     */
    createSkillExtractionPrompt(jobDescription) {
        return `
            You are an expert talent acquisition specialist. Your task is to extract SPECIFIC, ROLE-RELEVANT skills from job descriptions that would actually differentiate candidates. Avoid generic "good employee" traits that apply to every job.

            Job Description:
            """
            ${jobDescription}
            """

            EXTRACTION METHODOLOGY:
            1. SCAN for explicit mentions of technologies, tools, software, methodologies, and domain-specific knowledge
            2. IDENTIFY action verbs and key responsibilities that imply specific skills
            3. INFER industry-standard tools/skills that would be required for mentioned tasks
            4. EXCLUDE generic soft skills unless they are specialized or explicitly emphasized

            CATEGORY DEFINITIONS & RULES:

            HARD SKILLS (Technical proficiencies, tools, software, certifications):
            - Extract SPECIFIC technologies: "Python", "React.js", "Salesforce CRM", "Adobe Photoshop"
            - NOT generic terms: "Programming", "Software proficiency", "Computer skills"
            - Include certifications, methodologies, domain knowledge: "PMP Certification", "Agile/Scrum", "Financial modeling"
            - Infer standard tools for the role: Customer Support → "Zendesk/Freshdesk", Marketing → "Google Analytics"

            INTERPERSONAL SKILLS (People-focused abilities):
            - ONLY extract if explicitly mentioned or strongly implied by role responsibilities
            - Focus on specialized variants: "Client relationship management", "Cross-functional team leadership", "Stakeholder negotiation"
            - AVOID: Generic "Communication", "Teamwork" unless the role specifically emphasizes these

            BEHAVIORAL SKILLS (Cognitive approaches and work methodologies):
            - Extract work-style approaches mentioned in the description: "Data-driven decision making", "Strategic planning", "Process optimization"
            - Focus on HOW work is performed, not personality traits
            - Examples: "Analytical thinking", "Results-oriented approach", "Continuous improvement mindset"

            PERSONALITY (Inherent traits and dispositions - USE SPARINGLY):
            - ONLY include traits explicitly mentioned or strongly emphasized in the job description
            - Focus on traits that are critical for role success: "Detail-oriented", "Customer-focused", "Innovation-minded"
            - AVOID: Standard traits like "Motivated", "Reliable", "Professional"

            CRITICAL RULES:
            ✓ Each skill appears in ONLY ONE category (no duplicates)
            ✓ Prioritize specificity over quantity (3-7 skills per category)
            ✓ Use exact industry terminology when mentioned
            ✓ Empty arrays are acceptable if no relevant skills exist
            ✓ Focus on skills that would appear on a resume or in interviews

            Return ONLY this JSON structure:
            {
                "hard_skills": [],
                "interpersonal_skills": [],
                "behavioral_skills": [],
                "personality": []
            }
        `.trim();
    }

    /**
     * Parse the API response and extract skills
     * @param {Object} responseData - Response from OpenRouter API
     * @returns {Array} - Formatted skills array
     */
    parseSkillsResponse(responseData) {
        try {
            if (!responseData.choices || !responseData.choices[0] || !responseData.choices[0].message) {
                throw new Error('Invalid response format from OpenRouter API');
            }

            const content = responseData.choices[0].message.content.trim();
            
            // Try to parse JSON response
            let skillsData;
            try {
                skillsData = JSON.parse(content);
            } catch (parseError) {
                // If JSON parsing fails, try to extract JSON from the response
                const jsonMatch = content.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                    skillsData = JSON.parse(jsonMatch[0]);
                } else {
                    throw new Error('Could not parse skills from AI response');
                }
            }

            // Format skills for the application
            return this.formatSkillsForApplication(skillsData);
        } catch (error) {
            console.error('Error parsing skills response:', error);
            throw new Error('Failed to parse skills from AI response');
        }
    }

    /**
     * Format extracted skills for application use
     * @param {Object} skillsData - Parsed skills data from AI
     * @returns {Array} - Formatted skills array
     */
    formatSkillsForApplication(skillsData) {
        const formattedSkills = [];

        // Add hard skills
        if (skillsData.hard_skills && Array.isArray(skillsData.hard_skills)) {
            skillsData.hard_skills.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Hard Skills',
                    type: 'hard',
                    selected: false
                });
            });
        }

        // Add interpersonal skills
        if (skillsData.interpersonal_skills && Array.isArray(skillsData.interpersonal_skills)) {
            skillsData.interpersonal_skills.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Interpersonal Skills',
                    type: 'interpersonal',
                    selected: false
                });
            });
        }

        // Add behavioral skills
        if (skillsData.behavioral_skills && Array.isArray(skillsData.behavioral_skills)) {
            skillsData.behavioral_skills.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Behavioral Skills',
                    type: 'behavioral',
                    selected: false
                });
            });
        }

        // Add personality traits
        if (skillsData.personality && Array.isArray(skillsData.personality)) {
            skillsData.personality.forEach(skill => {
                formattedSkills.push({
                    name: skill,
                    category: 'Personality',
                    type: 'personality',
                    selected: false
                });
            });
        }

        return formattedSkills;
    }

    /**
     * Handle API errors and provide user-friendly messages
     * @param {Error} error - The error object
     * @returns {Error} - Formatted error
     */
    handleApiError(error) {
        // Add detailed logging for debugging
        console.error('Full error object:', error);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
            console.error('Response headers:', error.response.headers);
        }

        if (error.response) {
            const status = error.response.status;
            const data = error.response.data;

            switch (status) {
                case 401:
                    return new Error('Invalid API key. Please check your OpenRouter configuration.');
                case 402:
                    return new Error('Insufficient credits. Please check your OpenRouter account balance.');
                case 429:
                    return new Error('Rate limit exceeded. Please try again in a few minutes.');
                case 500:
                    return new Error('OpenRouter service is temporarily unavailable. Please try again later.');
                default:
                    return new Error(data?.error?.message || `API request failed with status ${status}`);
            }
        } else if (error.request) {
            return new Error('Network error. Please check your internet connection and try again.');
        } else {
            return new Error(error.message || 'An unexpected error occurred while processing the job description.');
        }
    }

    /**
     * Validate job description before processing
     * @param {string} jobDescription - The job description text
     * @returns {boolean} - Whether the job description is valid
     */
    validateJobDescription(jobDescription) {
        if (!jobDescription || typeof jobDescription !== 'string') {
            return false;
        }

        const trimmed = jobDescription.trim();
        
        // Check minimum length (at least 50 characters)
        if (trimmed.length < 50) {
            return false;
        }

        // Check maximum length (10,000 characters to avoid token limits)
        if (trimmed.length > 10000) {
            return false;
        }

        return true;
    }

    /**
     * Get validation error message for job description
     * @param {string} jobDescription - The job description text
     * @returns {string|null} - Error message or null if valid
     */
    getValidationError(jobDescription) {
        if (!jobDescription || typeof jobDescription !== 'string') {
            return 'Job description is required.';
        }

        const trimmed = jobDescription.trim();
        
        if (trimmed.length < 50) {
            return 'Job description must be at least 50 characters long.';
        }

        if (trimmed.length > 10000) {
            return 'Job description must be less than 10,000 characters.';
        }

        return null;
    }
}

// Export singleton instance
export default new OpenRouterService();
