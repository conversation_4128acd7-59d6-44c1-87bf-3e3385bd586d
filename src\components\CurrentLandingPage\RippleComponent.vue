<template>
    <div :class="['absolute inset-0 bg-white/5 mask-image-gradient', props.class]" style="--mask-image: linear-gradient(to_bottom, white, transparent)">
        <div v-for="(_, i) in Array.from({ length: props.numCircles })" :key="i">
            <div
                class="animate-ripple absolute rounded-full bg-foreground/25 shadow-xl border"
                :style="{
                    width: `${props.mainCircleSize + i * 70}px`,
                    height: `${props.mainCircleSize + i * 70}px`,
                    opacity: props.mainCircleOpacity - i * 0.03,
                    animationDelay: `${i * 0.06}s`,
                    borderStyle: i === props.numCircles - 1 ? 'dashed' : 'solid',
                    borderWidth: '1px',
                    borderColor: `hsla(var(--foreground), ${5 + (i * 5) / 100})`,
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%) scale(1)',
                }"
            ></div>
        </div>
    </div>
</template>

<script setup>
const props = defineProps({
    mainCircleSize: {
        type: Number,
        default: 210,
    },
    mainCircleOpacity: {
        type: Number,
        default: 0.24,
    },
    numCircles: {
        type: Number,
        default: 8,
    },
    class: {
        type: String,
        default: "",
    },
});
</script>

<style>
@keyframes active-ripple {
    0%,
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0.2;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1) rotate(4deg);
        opacity: 0.25;
    }
}

.animate-ripple {
    animation: active-ripple 1.5s ease-in-out infinite;
}
</style>
