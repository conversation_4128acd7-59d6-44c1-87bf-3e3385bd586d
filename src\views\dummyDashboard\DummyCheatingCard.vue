<template>
    <AntiCheatingPolicy :show="showAntiCheat" :toggleModal="toggleAntiCheat" />

    <div class="cheating-container">
        <div class="w-full flex justify-between p-3 bg-white border-b items-center rounded mb-5 shadow-card">
            <button class="w-[60px] h-[50px] rounded-[10px] bg-black/5 text-gray-700 hover:bg-black/10 block" @click="goBackFunction()">
                <font-awesome-icon :icon="['fas', 'angle-left']" />
            </button>
            <!-- <div class="flex items-center justify-end gap-5">
                <p>Based on the analyse you did below, do you consider this candidate a cheater ?</p>
                <div class="flex items-center justify-center gap-2">
                    <button @click="deleteCheater()" class="w-[80px] h-[50px] rounded-[10px] bg-black/5 text-gray-700 hover:bg-black/10 menuBtn block">No</button>
                    <button @click="validateCheater()" class="nextStep shadow ml-auto">Yes</button>
                </div>
            </div> -->
        </div>
        <div v-if="isLoading" class="loading">
            <LoaderComponentBlue />
        </div>
        <div v-else class="flex flex-col w-full gap-5">
            <div class="flex lg:flex-row gap-5 w-full">
                <div class="w-full lg:w-[50%] p-2 rounded-md shadow-card bg-white">
                    <span class="recentapp flex items-center justify-between w-full">
                        {{ $t("Candidate Card") }}
                        <font-awesome-icon @click="showAntiCheat = true" class="font-light text-xs w-3 h-3 p-1 ml-3 rounded-full border border-slate-700" :icon="['fas', 'question']"
                    /></span>
                    <div class="flex flex-col justify-start items-start gap-3 py-6 px-1">
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-light">{{ $t("Device used") }}</h2>
                            <span class="font-bold text-sm">{{ $t("Desktop") }}</span>
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-light">{{ $t("Location") }}</h2>
                            <span class="font-bold text-sm">{{ cheater?.Location }}</span>
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Mouse always in assessment window?") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': cheater?.mouseExited, 'bg-green-800': !cheater?.mouseExited }"
                            >
                                {{ !cheater?.mouseExited ? "Yes" : "No" }}</span
                            >
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Full-screen mode always active?") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': cheater?.fullScreenExited, 'bg-green-800': !cheater?.fullScreenExited }"
                            >
                                {{ !cheater?.fullScreenExited ? "Yes" : "No" }}</span
                            >
                        </div>
                        <div class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Stayed within assessment window?") }}</h2>
                            <span class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white" :class="{ 'bg-red-800': cheater?.exitedTab, 'bg-green-800': !cheater?.exitedTab }">
                                {{ !cheater?.exitedTab ? "Yes" : "No" }}</span
                            >
                        </div>
                        <div v-if="cheater?.exitedTab" class="w-full flex flex-row justify-between items-center">
                            <h2 class="text-sm font-thin">{{ $t("Number of times window has changed :") }}</h2>
                            <span
                                class="font-normal text-sm rounded-sm w-[15%] px-1 text-center mr-1 text-white"
                                :class="{ 'bg-red-800': cheater?.exitCount > 0, 'bg-green-800': cheater?.exitCount === 0 }"
                            >
                                {{ cheater?.exitCount }}</span
                            >
                        </div>
                    </div>
                </div>
                <div class="w-full lg:w-[50%] p-2 rounded-md shadow-card bg-white">
                    <span class="recentapp flex items-center justify-between w-full"> {{ $t("Personal Informations") }}</span>
                    <div class="flex items-start gap-5 py-[2rem] px-1">
                        <img loading="lazy" class="w-[30%] h-[12rem] rounded border border-[#D9D9D9]" decoding="async" :src="'data:image/png;base64,' + this.cheater.avatar" alt="" />

                        <div class="flex w-[70%] flex-col gap-5 items-start">
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-light">{{ $t("First Name") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.First_name }}</span>
                            </div>
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-light">{{ $t("Last Name") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.Last_name }}</span>
                            </div>
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-thin">Email</h2>
                                <span class="font-bold text-sm">{{ cheater?.email }}</span>
                            </div>
                            <div class="w-full flex flex-row justify-between items-center">
                                <h2 class="text-sm font-thin">{{ $t("Phone Number") }}</h2>
                                <span class="font-bold text-sm">{{ cheater?.phone }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="w-full p-2 rounded-md shadow-card bg-[#fff]">
                <span class="recentapp"> {{ $t("Captured Photos") }}</span>

                <div class="flex flex-col content-center items-center p-4 bg-white w-full justify-between gap-3">
                    <div class="flex justify-center items-center">
                        <img src="../../assets/No-Picture-Captured.svg" alt="No captured pic" class="w-[40%] h-auto" />
                    </div>
                    <h2 class="text-xl text-center w-full font-semibold bg-custom-gradient bg-clip-text text-transparent">No Picture Captured</h2>
                    <div class="flex flex-col justify-center items-center">
                        <span class="text-center w-full">It seems we did not detect any cheating attempt by this</span>
                        <span class="text-center w-full">Candidate (An alternate individual, external support,)</span>

                        <span class="text-center w-full">Mobile device detection ...etc</span>
                    </div>
                    <div></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LoaderComponentBlue from "@/components/LoaderComponentBlue";
import AntiCheatingPolicy from "@/components/AntiCheatingPolicy.vue";
import { useStore } from "@/store/index";
import dummyData from "../../dummyData/dummyData.js";
import dummyCheaters from "../../dummyData/cheaters.js";

export default {
    name: "DummyCheatingCard",
    components: {
        LoaderComponentBlue,
        AntiCheatingPolicy,
        //   ButtonComponent
    },
    data() {
        return {
            currentIndex: 1,
            cheater: {},
            candidate: {},
            captured_photos: [],
            candidateEmail: "",
            project_id: "",
            isLoading: false,
            showAntiCheat: false,
            candidateInfo: this.Store.candidateInfoAct,
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        goBackFunction() {
            this.$router.go(-1);
        },
    },
    mounted() {
        // this.getCheaterId();
        // this.getCapturedPhotos();
        let dummy = dummyData.data;
        this.candidate = dummy.candidates.find((c) => c._id == this.$route.params.id);
        this.cheater = dummyCheaters.find((c) => c._id == this.$route.params.id);
        console.log({ DUMMYCHEATTER: this.cheater });
    },
};
</script>
<style scoped>
.nextStep {
    width: 80px;
    height: 50px;
    color: white;
    font-weight: 500;
    background: #2196f3;
    border-radius: 10px;

    &:hover {
        opacity: 0.85;
    }
}

.cheating-container {
    height: 100%;
    width: 100%;
    display: flex;
    padding-top: 40px;
    margin: 0;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.recentapp {
    color: #1b2559;
    font-family: DM Sans;
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
    /* 160% */
    letter-spacing: -0.4px;
}

.loading {
    width: 100%;
    height: 600px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
