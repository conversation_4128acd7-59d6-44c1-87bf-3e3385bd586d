<svg width="158" height="200" viewBox="0 0 158 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_3_1069)">
<rect y="0.806152" width="158" height="158" rx="50" fill="white" fill-opacity="0.05"/>
</g>
<rect x="1" y="1.80615" width="156" height="156" rx="49" stroke="url(#paint0_linear_3_1069)" stroke-opacity="0.2" stroke-width="2"/>
<g filter="url(#filter1_f_3_1069)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M111.98 159.107H49C49.637 142.265 63.4919 128.806 80.4902 128.806C97.4886 128.806 111.344 142.265 111.98 159.107Z" fill="#7F76FF" fill-opacity="0.31"/>
</g>
<g filter="url(#filter2_d_3_1069)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M119 79.8062C119 101.898 101.091 119.806 79 119.806C56.9086 119.806 39 101.898 39 79.8062C39 57.7148 56.9086 39.8062 79 39.8062C101.091 39.8062 119 57.7148 119 79.8062Z" fill="url(#paint1_linear_3_1069)"/>
<path d="M82 55.8062C82 54.1493 80.6569 52.8062 79 52.8062C77.3431 52.8062 76 54.1493 76 55.8062V57.0731C69.4783 58.2408 64 63.1406 64 69.8062C64 77.4748 71.2511 82.8062 79 82.8062C84.5059 82.8062 88 86.4291 88 89.8062C88 93.1832 84.5059 96.8062 79 96.8062C73.4941 96.8062 70 93.1832 70 89.8062C70 88.1493 68.6569 86.8062 67 86.8062C65.3431 86.8062 64 88.1493 64 89.8062C64 96.4717 69.4783 101.371 76 102.539V103.806C76 105.463 77.3431 106.806 79 106.806C80.6569 106.806 82 105.463 82 103.806V102.539C88.5217 101.371 94 96.4717 94 89.8062C94 82.1375 86.7489 76.8062 79 76.8062C73.4941 76.8062 70 73.1832 70 69.8062C70 66.4291 73.4941 62.8062 79 62.8062C84.5059 62.8062 88 66.4291 88 69.8062C88 71.463 89.3431 72.8062 91 72.8062C92.6569 72.8062 94 71.463 94 69.8062C94 63.1406 88.5217 58.2408 82 57.0731V55.8062Z" fill="white"/>
</g>
<defs>
<filter id="filter0_i_3_1069" x="0" y="0.806152" width="158" height="163" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5"/>
<feGaussianBlur stdDeviation="7.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_3_1069"/>
</filter>
<filter id="filter1_f_3_1069" x="9" y="88.8062" width="142.98" height="110.301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="20" result="effect1_foregroundBlur_3_1069"/>
</filter>
<filter id="filter2_d_3_1069" x="35" y="39.8062" width="88" height="88" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_3_1069"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_3_1069" result="shape"/>
</filter>
<linearGradient id="paint0_linear_3_1069" x1="79" y1="0.806152" x2="79" y2="158.806" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="white" stop-opacity="0.3"/>
</linearGradient>
<linearGradient id="paint1_linear_3_1069" x1="51" y1="48.3062" x2="102" y2="110.306" gradientUnits="userSpaceOnUse">
<stop stop-color="#8C16E4"/>
<stop offset="0.765625" stop-color="#FF5DE5"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
