import { createRouter, createWebHistory } from "vue-router";

import TalentProfile from "@/views/unused/marketplace/TalentProfile.vue";

const routes = [
    // admin routes
    {
        path: "/:locale/assessments/:id",
        name: "AssessmentsDetail",
        component: () => import("@/views/adminDashboard/assessments/AssessmentsDetail.vue"),
        meta: {
            title: "AssessmentsDetail",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/assessments/edit/:id",
        name: "EditAssessment",
        component: () => import("@/views/adminDashboard/assessments/EditAssessment.vue"),
        meta: {
            title: "EditAssessment",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/assessments/add",
        name: "AddAssessments",
        component: () => import("@/views/adminDashboard/assessments/AddAssessment.vue"),
        meta: {
            title: "AddAssessments",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/assessments",
        name: "AssessmentDashboard",
        component: () => import("@/views/adminDashboard/assessments/AssessmentDashboard.vue"),
        meta: {
            title: "AssessmentDashboard",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/admin",
        name: "admin",
        component: () => import("@/views/adminDashboard/AdminDashboard.vue"),
        meta: {
            title: "admin",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/Job-Positions",
        name: "Job Positions",
        component: () => import("@/views/adminDashboard/jobPositions/JobPositionsAdmin.vue"),
        meta: {
            title: "Job Positions",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/adminBlogs",
        name: "adminBlogs",
        component: () => import("@/views/adminDashboard/blogs/AdminBlogs.vue"),
        meta: {
            title: "adminBlogs",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/companies",
        name: "companies",
        component: () => import("@/views/adminDashboard/CompaniesBoard.vue"),
        meta: {
            title: "Manage companies",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/addBlogs",
        name: "addBlogs",
        component: () => import("@/views/adminDashboard/blogs/AddBlog.vue"),
        meta: {
            title: "addBlogs",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/updateBlog/:id",
        name: "updateBlog",
        component: () => import("@/views/adminDashboard/blogs/UpdateBlog.vue"),
        meta: {
            title: "updateBlog",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/categories",
        name: "categories",
        component: () => import("@/views/adminDashboard/categories/AdminCategories.vue"),
        meta: {
            title: "adminCategories",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/adminBlogs",
        name: "adminBlogs",
        component: () => import("@/views/adminDashboard/blogs/AdminBlogs.vue"),
        meta: {
            title: "adminBlogs",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/updateBlog/:id",
        name: "updateBlog",
        component: () => import("@/views/adminDashboard/blogs/UpdateBlog.vue"),
        meta: {
            title: "updateBlog",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/categories",
        name: "categories",
        component: () => import("@/views/adminDashboard/categories/AdminCategories.vue"),
        meta: {
            title: "adminCategories",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/addCategory",
        name: "addCategory",
        component: () => import("@/views/adminDashboard/categories/AddCategory.vue"),
        meta: {
            title: "addCategory",
            requiresAdmin: true,
        },
    },
    {
        path: "/:locale/updateCategory/:id",
        name: "updateCategory",
        component: () => import("@/views/adminDashboard/categories/UpdateCategory.vue"),
        meta: {
            title: "updateCategory",
            requiresAdmin: true,
        },
    },

    // recruiter dashboard routes
    {
        path: "/:locale/dashboard",
        name: "dashboard",
        component: () => import("@/views/dashboard/PartialLibraryView.vue"),
        meta: {
            title: "Dashboard",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/boards",
        name: "boards",
        component: () => import("@/views/dashboard/project-board/DashboardView"),
        meta: {
            title: "Boards",
            requiresAuth: true,
        },
    },

    {
        path: "/:locale/dummy-dashboard",
        name: "dummy board",
        component: () => import("@/views/dummyDashboard/DummyDashboard"),
        meta: {
            title: "Dummy Board",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/dummy-anti-cheat/:id",
        name: "dummy-anti-cheat",
        component: () => import("@/views/dummyDashboard/DummyCheatingCard.vue"),
        meta: {
            title: "anti-cheat",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/anti-cheat/:id",
        name: "anticheat",
        component: () => import("@/views/dashboard/project-board/CheatingCard.vue"),
        meta: {
            title: "anti-cheat",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/library",
        name: "library",
        component: () => import("@/views/dashboard/LibraryView.vue"),
        meta: {
            title: "Assessements Library",
            requiresAuth: true,
            // requiresAuth: true,
        },
    },
    {
        path: "/:locale/refferal",
        name: "RefferalView",
        component: () => import("@/views/unused/RefferalView.vue"),
        meta: {
            title: "Thank you!",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/newAssessment",
        name: "NewAssessement",
        component: () => import("@/views/dashboard/NewAssessment.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "new assessment",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/candidate",
        name: "CandidateActivity",
        component: () => import("@/components/dashboard/candidate-data/CandidateActivity.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "candidate",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/companyprofile",
        name: "companyProfile",
        component: () => import("@/views/dashboard/profile/CompanyProfile.vue"),
        meta: {
            title: "Company",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/invoice",
        name: "invoice",
        component: () => import("@/views/unused/InvoiceView.vue"),
        meta: {
            title: "Invoice",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/manage",
        name: "manage",
        component: () => import("@/views/unused/ManageView.vue"),
        meta: {
            title: "Manage",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/ChangePassword",
        name: "ChangePassword",
        component: () => import("@/views/public/ChangePassword.vue"),
        meta: {
            title: "Change password",
        },
    },
    {
        path: "/:locale/previewAssessment",
        name: "Sample-Questions",
        component: () => import("@/views/dashboard/QuestionsSample.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "preview assessment",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/preview-project",
        name: "QuestionsSample",
        component: () => import("@/views/dashboard/PreviewProject.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "preview-project",
            requiresAuth: false,
        },
    },
    {
        path: "/:locale/settings",
        name: "profile settings",
        component: () => import("@/views/dashboard/profile/SettingsView.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "settings",
            requiresAuth: true,
        },
    },

    // talent routes
    {
        path: "/:locale/profile",
        name: "MarketPlaceProfile",
        component: () => import("@/views/unused/marketplace/MarketPlaceProfile.vue"),
        meta: {
            title: "MarketPlaceProfile",
            requiresAuth: false,
        },
    },

    // home page routes
    {
        path: "/:locale/",
        name: "CurrentLandingPage",
        component: () => import("@/views/public/CurrentLandingPage.vue"),
        meta: {
            title: "Home",
            requiresAuth: false,
        },
    },
    {
        path: "/:locale/Tour",
        name: "ProductTour",
        component: () => import("@/views/public/ProductTour.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "Product Tour",
            requiresAuth: false,
        },
    },

    {
        path: "/:locale/BPO-and-outsourcing",
        name: "BPO-and-outsourcing",
        component: () => import("@/views/public/OutSourcingView.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "BPO-and-outsourcing",
            requiresAuth: false,
        },
    },
    {
        path: "/:locale/privacy-policy",
        name: "privacy policy",
        component: () => import("@/views/public/PrivacyPolicy.vue"),
        meta: {
            title: "privacy policy",
        },
    },
    {
        path: "/:locale/request-service",
        name: "request service",
        component: () => import("@/views/public/RequestServiceView.vue"),
        meta: {
            title: "request service",
            requiresAdmin: false,
        },
    },
    {
        path: "/:locale/blogs",
        name: "BlogsPage",
        component: () => import("@/views/public/BlogsView.vue"),
        meta: {
            title: "blogs",
        },
    },
    {
        path: "/:locale/job-position-preview",
        name: "JobPosPreview",
        component: () => import("@/views/public/assessments-library/JobPosPreview.vue"),
        meta: {
            title: "preview job position",
        },
    },
    {
        path: "/:locale/Details",
        name: "Details",
        component: () => import("@/views/detailsBoard.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "Dashboard Details",
            requiresAuth: true,
        },
    },
    {
        path: "/:locale/blog/:id",
        name: "DetailBlog",
        component: () => import("@/views/unused/DetailBlog.vue"),
        meta: {
            title: "blog",
        },
    },

    {
        path: "/:locale/talent/:id",
        name: "Talent",
        component: TalentProfile,
        meta: {
            title: "Talent profile",
        },
    },
    {
        path: "/:locale/assessments-library/:cat",
        name: "assessments-library",
        component: () => import("@/views/public/AssessmentLibrary.vue"),
        meta: {
            title: "Assessment Tests",
            requiresAuth: false,
        },
    },

    {
        path: "/:locale/SciencePage",
        name: "SciencePage",
        component: () => import("@/views/public/SciencePage.vue"),
        meta: {
            title: "Science Page",
            requiresAuth: false,
        },
    },
    {
        path: "/:locale/planning",
        name: "PlanningPage",
        component: () => import("@/views/public/PlanningView.vue"),
        meta: {
            title: "Planing",
            requiresAuth: false,
        },
    },

    {
        path: "/:locale/contact",
        name: "contactUs",
        component: () => import("@/views/public/contactUs.vue"),
        meta: {
            title: "Get in touch",
        },
    },
    {
        path: "/:locale/register",
        name: "RegisterPage",
        //component: () => import("@/views/public/auth/RegisterPage.vue"),
        component: () => import("@/views/public/auth/OnboardingNew.vue"),

        meta: {
            title: "Create Account",
            requiresAuth: false,
        },
    },

    {
        path: "/:locale/completeRegister",
        name: "completeRegister",
        component: () => import("@/views/public/auth/CompleteRegister.vue"),
        meta: {
            title: "Onboarding register complete",
            requiresAuth: false,
        },
    },

    // recruiter dashboard routes
    {
        path: "/:locale/home",
        name: "home",
        component: () => import("@/views/dashboard/HomeView.vue"),
        meta: {
            title: "Home",
            requiresAuth: true,
        },
    },

    {
        path: "/:locale/registerBoard",
        name: "RegisterOnboard",
        component: () => import("@/views/public/auth/OnboardingNew.vue"),
        meta: {
            title: "Onboarding register",
            requiresAuth: false,
        },
    },
    {
        path: "/:locale/login",
        name: "LogIn",
        component: () => import("@/views/public/auth/LogIn.vue"),
        meta: {
            title: "Login",
        },
    },
    {
        path: "/:locale/websummit-special",
        name: "Web Summit",
        component: () => import("@/views/websummit/WebSummitView.vue"),
        meta: {
            title: "Web Summit",
        },
    },

    {
        path: "/:locale/payement",
        name: "payement",
        component: () => import("@/views/unused/payment/PayementView2.vue"),
        meta: {
            title: "Payement",
            requiresAuth: true,
        },
    },

    // {
    //     path: "/", // Default route without locale
    //     redirect: () => {
    //         // Dynamically redirect to the default locale
    //         const defaultLocale = "en"; // Set your default locale here
    //         return `/${defaultLocale}/`;
    //     },
    // },

    // unknown routes
    {
        path: "/:locale/DashVed",
        name: "DashVed",
        component: () => import("@/views/DashVed.vue"),
        meta: {
            title: "Dashboard ved",
        },
    },

    // duplicated route
    {
        path: "/:locale/Terms-of-use",
        name: "TermsPage",
        component: () => import("@/views/public/TermsPage.vue"),
        meta: {
            title: "Term of use Page",
            requiresAuth: false,
        },
    },
    {
        path: "/:locale/Terms",
        name: "Terms",
        component: () => import("@/views/public/PolicyTerms.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "Policy & terms of use",
            requiresAuth: false,
        },
    },

    // talents
    /*
    {
        path: "/:locale/Talents",
        name: "NewLandingPage",
        component: () => import("@/views/public/NewLandingPage.vue"),
        meta: {
            title: "Landing",
        },
    },
    */
    {
        path: "/:locale/market",
        name: "market",
        component: () => import("@/views/unused/marketplace/MarketPlace.vue"),
        meta: {
            title: "market",
            //requiresAuth: true,
            // requiresAuth: true,
        },
    },

    // to check
    // {
    //     path: "/:locale/security",
    //     name: "security",
    //     component: () => import("@/views/SecurityView.vue"),
    //     meta: {
    //         title: "Security",
    //         requiresAuth: true,
    //     },
    // },
    // {
    //     path: "/:locale/personalitytest",
    //     name: "PersonalityTest",
    //     component: () => import("@/views/PersonalityTest_copy.vue"),
    //     meta: {
    //         title: "Profile",
    //         requiresAuth: true,
    //     },
    // },
    // {
    //     path: "/:locale/payementForm",
    //     name: "PayementForm",
    //     component: () => import("@/views/PayementForm.vue"),
    //     meta: {
    //         title: "Payement",
    //         requiresAuth: true,
    //     },
    // },
    // {
    //     path: "/:locale/CompareTalent",
    //     name: "CompareTalent",
    //     component: () => import("@/views/compareView.vue"),
    //     props: true, // Enable passing props to the component
    //     meta: {
    //         title: "Compare",
    //         requiresAuth: true,
    //     },
    // },
    // {
    //     path: "/:locale/activity",
    //     name: "activity",
    //     component: () => import(@/views/ActivityView.vue"),
    //     props: true, // Enable passing props to the component
    //     meta: {
    //         title: "activity",
    //         requiresAuth: true,
    //     },
    // },

    // reusable components
    {
        path: "/:locale/buttons",
        name: "buttons",
        component: () => import("@/views/TestButtons.vue"),
        meta: {
            title: "buttons",
            requiresAuth: false,
        },
    },

    {
        path: "/:locale/InvitedCoworker",
        name: "InvitedCoworker",
        component: () => import("@/views/public/auth/InvitedCoworker.vue"),
        props: true, // Enable passing props to the component
    },

    // new dashboard routes "probably"
    {
        path: "/:locale/DashVideo",
        name: "DashVideo",
        component: () => import("@/views/DashVideo.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "Dashboard Video",
            requiresAuth: true,
        },
    },

    {
        path: "/:locale/CheatTab",
        name: "CheatTab",
        component: () => import("@/views/AntiCheatBoard.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "Dashboard Anti-Cheat",
            requiresAuth: true,
        },
    },

    {
        path: "/:locale/InvitedCoworkerTab",
        name: "InvitedCoworkerTab",
        component: () => import("@/views/dashboard/profile/InvitedCoworkerTable.vue"),
        props: true, // Enable passing props to the component
        meta: {
            title: "Company",
            requiresAuth: true,
        },
    },

    {
        path: "/invite",
        name: "InviteC",
        component: () => import("@/views/Invite.vue"),
        meta: {
            title: "Invite",
        },
    },

    {
        path: "/:locale/:pathMatch(.*)*",
        name: "NotFound",
        component: () => import("@/views/NotFoundView.vue"),
        meta: {
            title: "Not Found",
        },
    },
];

const router = createRouter({
    history: createWebHistory(process.env.BASE_URL),
    routes,
});

export default router;
/*const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router*/
