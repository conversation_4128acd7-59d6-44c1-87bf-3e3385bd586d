<template>
    <div class="w-full newAssessWrapper">
        <div class="w-full flex justify-between">
            <div class="flex">
                <button class="backBtn">
                    <font-awesome-icon :icon="['fas', 'angle-left']" />
                </button>
                <div class="mx-4 flex flex-col">
                    <h2 class="projData">{{ project.name ? project.name : "Untitled" }} {{ project.seniority && " - " + project.seniority }} {{ project.jobTitle && " - " + project.jobTitle }}</h2>
                    <div class="flex" style="color: #2196f3">
                        <div class="flex items-center">
                            <font-awesome-icon :icon="['far', 'file-lines']" class="mx-2" />
                            <p>{{ placeholders.filter((assessment) => assessment.assessment !== null).length }} tests</p>
                        </div>
                        <div class="flex mx-2 items-center">
                            <font-awesome-icon :icon="['far', 'clock']" class="mx-2" />
                            <p>{{ totalDration }} minutes</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex items-center">
                <button class="backBtn mx-3" @click="previousStep"><font-awesome-icon :icon="['fas', 'angle-left']" /></button>
                <button class="nextStep" @click="nextStep" v-if="this.step < 3">{{ $t("Next step") }}</button>
                <button class="nextStep" @click="nextStep" v-else>{{ $t("Finish") }}</button>
            </div>
        </div>
        <div class="w-full flex justify-between steps">
            <div class="">
                <div
                    class="circle"
                    @click="
                        () => {
                            step = 1;
                        }
                    "
                    :style="{ backgroundColor: step < 4 ? '#2b3674' : '#2b36745e' }"
                >
                    <span v-if="this.project.name !== '' && step !== 1">
                        <font-awesome-icon :icon="['fas', 'check']" />
                    </span>
                    <span v-else>1</span>
                </div>
                <span class="absolute top-[130%] left-[0] w-max" style="transform: translateX(-30%)">{{ $t("Create project") }}</span>
            </div>
            <div class="">
                <div
                    class="circle"
                    @click="
                        () => {
                            if (this.validateForm()) {
                                step = 2;
                            }
                        }
                    "
                    :style="{ backgroundColor: step === 2 || step === 3 ? '#2b3674' : '#2b36745e' }"
                >
                    <span v-if="this.placeholders[0]?.assessment !== null && step !== 2">
                        <font-awesome-icon :icon="['fas', 'check']" />
                    </span>
                    <span v-else>2</span>
                </div>
                <span class="absolute top-[130%] left-[50%] translate-[-50%] w-max" style="transform: translateX(-50%)">{{ $t("Select tests") }}</span>
            </div>
            <div class="">
                <div
                    class="circle"
                    @click="
                        () => {
                            if (this.validateForm() && this.placeholders[0]?.assessment !== null) {
                                step = 3;
                            }
                        }
                    "
                    :style="{ backgroundColor: step === 3 ? '#2b3674' : '#2b36745e' }"
                >
                    <span>3</span>
                </div>
                <span class="absolute top-[130%] left-[100%] w-max" style="transform: translateX(-60%)">{{ $t("Review and configure") }}</span>
            </div>
        </div>
        <div class="w-full">
            <div v-show="step === 1">
                <form action="POST" @submit.prevent="onSubmit" ref="form1" class="w-full flex flex-col items-center">
                    <!-- ... your form content for section 1 ... -->
                    <div class="input_group">
                        <input type="text" id="project_name" name="project_name" v-model="project.name" @focus="isInputFocused = true" required /><label
                            class="required floating_label"
                            :class="{ active: project.name }"
                            for="project_name"
                        >
                            <img src="../assets/Images/icons/job_icon.svg" alt="" />
                            {{ $t("Project name") }}</label
                        >
                        <span v-if="requiredFields.project_name" class="err_msg">{{ requiredFields.project_name }} </span>
                    </div>

                    <div class="input_group">
                        <input type="text" id="job_title" name="job_title" v-model="project.jobTitle" @focus="isInputFocused = true" required /><label
                            class="required floating_label"
                            :class="{ active: project.jobTitle }"
                            for="project_name"
                        >
                            <img src="../assets/Images/icons/Archeive.svg" alt="" />
                            {{ $t("Job Title") }}</label
                        >
                        <span v-if="requiredFields.job_title" class="err_msg">{{ requiredFields.job_title }} </span>
                    </div>
                    <div class="input_group select">
                        <label class="required select_label" for="job_seniority"><img src="../assets/Images/icons/seniority_icon.svg" alt="" /></label>
                        <select name="Seniority" id="" class="selectStatus" v-model="project.seniority">
                            <option value="">{{ $t("Seniority") }}</option>
                            <option v-for="(seniority, index) in seniorities" :key="index" :value="seniority">{{ seniority }}</option>
                        </select>
                        <span v-if="requiredFields.job_seniority" class="err_msg" style="left: -2%">{{ requiredFields.job_seniority }}</span>
                    </div>
                </form>
            </div>
            <div v-show="step === 2">
                <div class="myTests">
                    <EmptyTest
                        v-for="(placeholder, index) in placeholders"
                        :key="index"
                        :index="index"
                        :assessment="placeholder.assessment"
                        :handleDrop="handleDrop"
                        :removeAssessment="removeAssessment"
                    />
                </div>
                <div class="my-5">
                    <NewAssessLib :addNewAssessment="addAssessment" :filteredAssessments="filteredAssessments" />
                </div>
                <div class="mySideBar">
                    <Sidebar
                        :categories="categories"
                        :companies="companies"
                        :types="types"
                        @filters-applied="applyFilters"
                        @filterAssessmentsCatégory="filterAssessmentsCategory"
                        @filterAssessmentsCompanies="filterAssessmentsCompanies"
                        :watch="watch"
                        :addCategory="addCategory"
                        :addType="addType"
                    />
                </div>
            </div>
            <div v-show="step === 3">
                <ConfirmNewAssessment :moveTestUp="moveTestUp" :moveDownObject="moveDownObject" :removeAssessment="removeAssessment" :assessments="placeholders" />
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
import { useStore } from "../store/index";
import { BASE_URL } from "@/constants";
import EmptyTest from "@/components/EmptyTest.vue";
import NewAssessLib from "@/components/dashboard/createAssessment/NewAssessLib.vue";
import ConfirmNewAssessment from "@/components/dashboard/createAssessment/ConfirmNewAssessment.vue";
import Sidebar from "@/components/sideBar.vue";
export default {
    name: "NewAssessement",
    components: {
        EmptyTest,
        Sidebar,
        NewAssessLib,
        ConfirmNewAssessment,
    },
    data() {
        return {
            step: 2,

            TopAssessement: [],
            allAssessments: [],
            hardSkills: [],
            softSkills: [],
            psychometrics: [],

            project: {
                name: "",
                jobTitle: "",
                seniority: "",
                assessments: [],
            },
            requiredFields: {
                project_name: "",
                job_title: "",
                job_seniority: "",
            },
            placeholders: [
                { id: 1, assessment: null },
                { id: 2, assessment: null },
                { id: 3, assessment: null },
                { id: 4, assessment: null },
                { id: 5, assessment: null },
            ],
            seniorities: ["Internship", "Entry Level", "Junior", "Senior", "VP", "Executive"],
            assessments: [], // Array of assessments

            filterdCategories: [],
            filterdType: [],
            selectedCategories: [],
            selectedTypes: [],
            selectedCompanies: [],
        };
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    methods: {
        validateForm() {
            // Reset requiredFields object
            this.requiredFields = {};

            // Check for required fields
            if (!this.project.name) this.requiredFields.project_name = "Project name is required";
            if (!this.project.jobTitle) this.requiredFields.job_title = "Job title is required";
            if (!this.project.seniority) this.requiredFields.job_seniority = "Job seniority is required";

            // Check if all required fields are filled
            return Object.keys(this.requiredFields).length === 0;
        },
        filterAssessement() {
            // return this.Store.allAssessments.filter((assessment) => {
            //   this.filterdCategories?.length === 0 || this.filterdCategories?.includes(assessment.category) || this.filterdType.length === 0 || this.filterdType.includes(assessment.type);
            //});
            let filtered = this.Store.allAssessments;
            let hard = this.hardSkills;
            let soft = this.softSkills;
            console.log("ALL Assessements:");
            console.log(this.filtered);

            console.log("Filtered Assessements:");
            console.log(this.filterdCategories);

            return this.filterdCategories?.length != 0 ? (this.filterdCategories == "hard skills" ? hard : soft) : filtered;
        },

        nextStep() {
            if (this.step < 3) {
                if (this.step === 1) {
                    if (this.validateForm()) {
                        this.step++;
                    }
                } else if (this.step === 2) {
                    if (this.placeholders[0].assessment !== null) {
                        this.step++;
                    }
                }
            }
        },
        previousStep() {
            if (this.step > 1) {
                this.step--;
            }
        },
        addCategory(event) {
            if (this.filterdCategories.includes(event.target.value)) {
                this.filterdCategories.splice(this.filterdCategories.indexOf(event.target.value), 1);
                return console.log(this.filterdCategories);
            }
            this.filterdCategories.push(event.target.value);
            return console.log(this.filterdCategories);
        },
        addType(event) {
            if (this.filterdType.includes(event.target.value)) {
                this.filterdType.splice(this.filterdType.indexOf(event.target.value), 1);
                return console.log(this.filterdType);
            }
            this.filterdType.push(event.target.value);
            return console.log(this.filterdType);
        },

        handleDrop(index, event) {
            // Prevent default behavior to allow drop
            event.preventDefault();

            // Get the index of the dragged item from the drag data
            const draggedIndex = event.dataTransfer.getData("text/plain");
            console.log({ draggedIndex });
            // Swap the assessments in the array
            const draggedAssessment = this.placeholders[draggedIndex];
            this.placeholders[draggedIndex] = this.placeholders[index];
            this.placeholders[index] = draggedAssessment;
        },
        addAssessment(assessment) {
            const placeholder = this.placeholders.find((placeholder) => placeholder.assessment === assessment);
            if (placeholder) {
                alert("Test already added,");
                return;
            }

            const emptyPlaceholder = this.placeholders.find((placeholder) => placeholder.assessment === null);
            if (emptyPlaceholder) {
                emptyPlaceholder.assessment = assessment;
            } else alert("You can't add more than 5 Tests");
        },

        removeAssessment(assessment) {
            const index = this.placeholders.findIndex((placeholder) => placeholder.assessment === assessment);
            if (index !== -1) {
                // Set the assessment at the current index to null
                this.placeholders[index].assessment = null;

                // Shift assessments after the removed one to the left
                for (let i = index + 1; i < this.placeholders.length; i++) {
                    // Move the assessment one position to the left
                    this.placeholders[i - 1].assessment = this.placeholders[i].assessment;
                    // Clear the current assessment's position
                    this.placeholders[i].assessment = null;
                }
            }
            console.log({ placeholders: this.placeholders });
        },
        moveTestUp(test) {
            const index = this.placeholders.findIndex((placeholder) => placeholder.assessment === test);

            if (index > 0 && index < this.placeholders.length) {
                // Swap the object with the one above it
                [this.placeholders[index - 1], this.placeholders[index]] = [this.placeholders[index], this.placeholders[index - 1]];
            }
        },
        moveDownObject(test) {
            const index = this.placeholders.findIndex((placeholder) => placeholder.assessment === test);

            // Check if the index is valid and not the last element
            if (index >= 0 && index < this.placeholders.length - 1) {
                // Swap the object with the one below it
                [this.placeholders[index], this.placeholders[index + 1]] = [this.placeholders[index + 1], this.placeholders[index]];
            }
        },
    },
    watch: {
        placeholders: {
            handler(newVal) {
                console.log("----------===============-------------------------------------", { newVal });
            },
            immediate: true, // Trigger the watcher immediately when the component is created
        },
    },
    computed: {
        filteredAssessments() {
            console.log("/////////", this.filterdAssessements);
            return this.filterAssessement();
        },
        totalDration: {
            get() {
                return this.placeholders.reduce((acc, placeholder) => {
                    if (placeholder.assessment !== null) {
                        if (placeholder?.assessment?.questions_nbr > 25) {
                            console.log("25+ questions", placeholder?.assessment?.questions_nbr, parseInt((20 * 35) / 60));
                            return acc + parseInt((20 * 35) / 60);
                        } else {
                            return acc + parseInt((placeholder?.assessment?.questions_nbr * 35) / 60);
                        }
                    }
                    return acc;
                }, 0);
            },
        },
    },
    mounted() {
        axios
            .get(`${BASE_URL}/AssessmentTest/hardSkills`, {
                withCredentials: true,
            })
            .then((res) => {
                this.hardSkills = res.data.hardSkills;
                this.Store.setPremium(res.data.premium);
                this.allAssessments = [...this.allAssessments, ...this.hardSkills];
                this.filterAssessements = this.allAssessments;
                this.Store.allAssessments = [...this.allAssessments, ...this.hardSkills];
                console.log({ length: this.hardSkills.length });
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/psychometrics`, {
                withCredentials: true,
            })
            .then((res) => {
                this.psychometrics = res.data;
                this.allAssessments = [...this.allAssessments, ...this.psychometrics];
                this.filterAssessements = this.allAssessments;
                this.Store.allAssessments = [...this.allAssessments, ...this.psychometrics];
                console.log({ psychometrics: this.psychometrics });
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/softSkills`, {
                withCredentials: true,
            })
            .then((res) => {
                this.softSkills = res.data;
                this.allAssessments = [...this.allAssessments, ...this.softSkills];
                this.filterAssessements = this.allAssessments;
                this.Store.allAssessments = [...this.allAssessments, ...this.softSkills];
                console.log({ psychometrics: this.psychometrics });
            });
        axios
            .get(`${BASE_URL}/AssessmentTest/topAssessments`, {
                withCredentials: true,
            })
            .then((res) => {
                this.TopAssessement = res.data;
                this.allAssessments = [...this.allAssessments, ...this.TopAssessement];
                this.filterAssessements = this.allAssessments;
                this.Store.allAssessments = [...this.allAssessments, ...this.TopAssessement];
                console.log({ psychometrics: this.psychometrics });
            });
    },
};
</script>

<style scoped lang="scss">
.newAssessWrapper {
    width: 90%;
    margin-left: -8%;
    margin-right: 5%;
    margin-top: 1%;

    .backBtn {
        width: 60px;
        height: 50px;
        background-color: #ededed;
        border: 1px solid #ededed;
        border-radius: 10px;
    }
    .my-5 {
        margin-left: 70px;
    }

    .projData {
        font-size: 20px;
        font-weight: 700;
    }

    .nextStep {
        width: 100px;
        height: 50px;
        color: white;
        font-weight: 500;
        background-color: #2196f3;
        border: 1px solid #2196f3;
        border-radius: 10px;
    }

    .previousStep {
        width: 150px;
        height: 50px;
        background-color: #ededed;
        border: 1px solid #ededed;
        border-radius: 10px;
    }

    .steps {
        margin-top: 6%;
        margin-bottom: 8%;
        position: relative;

        .circle {
            width: 40px;
            height: 40px;
            border-radius: 50px;
            background-color: #2b36745e;
            color: #fff;
            display: flex;
            justify-content: center;
            align-items: center;

            span {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                border: 3px solid #fff;
            }

            &:hover {
                background-color: #2b3674;
            }
        }

        > :first-child {
            &::before {
                content: "";
                position: absolute;
                top: 50%;
                left: 10%;
                width: 32%;
                height: 2px;
                background-color: #2b36745b;
                z-index: -1;
            }
        }

        > :nth-child(2) {
            &::before {
                content: "";
                position: absolute;
                top: 50%;
                left: 60%;
                width: 32%;
                height: 2px;
                background-color: #2b36745b;
                z-index: -1;
            }
        }
    }
}

.input_group {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
    margin: 2rem 0;
    width: 35%;

    label {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 18px;
        line-height: 32px;
        color: #05152e;

        img {
            margin-right: 5px;
        }
    }

    .mySideBar {
        background-color: #2196f3;
        margin-right: 20px;
    }

    .required {
        &::after {
            content: "*";
            color: #ff6969;
        }
    }

    .floating_label {
        position: absolute;
        top: 50%;
        left: 0;
        margin-left: 25px;
        padding: 0 1% 0 0;
        transform: translateY(-50%);
        pointer-events: none;
        background: #fff;
        transition: all 0.3s ease;
    }

    .floating_label.active {
        box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);
        border-radius: 50px;
        // width: 10%;
        background: #fff;

        transform: translateY(-150%);
    }

    input:focus + .floating_label {
        box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);
        border-radius: 50px;
        // width: 10%;
        background: #fff;

        transform: translateY(-150%);
    }

    input,
    textarea {
        border: 1px solid #ccd7ec;
        box-shadow: 0px 4px 10px rgba(21, 60, 245, 0.04);
        border-radius: 20px;
        width: 100%;
        padding: 8px 1.5rem;
    }

    input {
        height: 3rem;
    }

    textarea {
        resize: none;
    }

    .select_label {
        // position: absolute;
        // top: 50%;
        // left: 1%;
        // z-index: 30;
        // margin-left: 5px;
        padding: 0 1% 0 0;
        // transform: translateY(-50%);
        pointer-events: none;
        background: #fff;
    }
}

.select {
    height: 100%;
    width: 33%;
    margin: 1%;
    background-color: #fff;
    padding: 0.6rem;
    border-radius: 20px;
    border: 1px solid #ccd7ec;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;

    .selectStatus {
        height: 100%;
        width: 98%;
        background-color: #fff;
        font-size: 18px;

        &:focus {
            outline: none;
        }

        option {
            margin: 0.5rem 2rem;

            &:hover {
                color: #fff;
                background-color: #2196f3;
            }
        }
    }
}

.myTests {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1.5rem;
}

#app {
    display: flex;
}

.err_msg {
    color: #ff6969;
    font-size: 16px;
    font-weight: 300;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 125%;
    left: 0;
    z-index: 30;
    padding: 0 1% 0 0;
    margin-left: 25px;
    transform: translateY(-50%);
    pointer-events: none;
}
</style>
