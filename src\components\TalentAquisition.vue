<template>
    <section class="py-0">
        <div class="max-w-6xl mx-auto px-6 grid md:grid-cols-2 gap-10 items-center">
            <!-- Features List (Left) -->
            <div class="feature items-start">
                <h2 class="text-2xl font-bold text-[#21caf3] mb-6 text-center">{{ $t("Why Choose Us?") }}</h2>
                <ul class="space-y-8 items-start">
                    <li class="flex items-start gap-4">
                        <span class="bg-blue-100 text-blue-600 rounded-full p-3 flex items-center shrink-0">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3" />
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" />
                            </svg>
                        </span>
                        <div class="w-full text-left">
                            <h3 class="font-semibold text-lg mb-1 text-left">{{ $t("Lightning-Fast Matching") }}</h3>
                            <p class="text-gray-600 text-sm text-left">
                                {{ $t("We connect you with the right talent in as little as 72 hours.") }}
                            </p>
                        </div>
                    </li>
                    <li class="flex items-start gap-4">
                        <span class="bg-green-100 text-green-600 rounded-full p-3 flex items-center shrink-0">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </span>
                        <div class="w-full text-left">
                            <h3 class="font-semibold text-lg mb-1 text-left">{{ $t("Verified Professionals") }}</h3>
                            <p class="text-gray-600 text-sm text-left">
                                {{ $t("Every candidate is thoroughly screened for skills and experience.") }}
                            </p>
                        </div>
                    </li>
                    <li class="flex items-start gap-4">
                        <span class="bg-purple-100 text-purple-600 rounded-full p-3 flex items-center shrink-0">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h8" />
                            </svg>
                        </span>
                        <div class="w-full text-left">
                            <h3 class="font-semibold text-lg mb-1 text-left">{{ $t("Transparent Process") }}</h3>
                            <p class="text-gray-600 text-sm text-left">
                                {{ $t("No hidden fees, no surprises just clear communication.") }}
                            </p>
                        </div>
                    </li>
                    <li class="flex items-start gap-4">
                        <span class="bg-amber-100 text-amber-600 rounded-full p-3 flex items-center shrink-0">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </span>
                        <div class="w-full text-left">
                            <h3 class="font-semibold text-lg mb-1 text-left">{{ $t("Ongoing Support") }}</h3>
                            <p class="text-gray-600 text-sm text-left">
                                {{ $t("We're here for you and your hires, every step of the way.") }}
                            </p>
                        </div>
                    </li>
                </ul>
            </div>
            <!-- Editorial Message (Right) -->
            <div>
                <h1 class="text-5xl sm:text-6xl md:text-5xl text-gray-900 font-bold leading-tight mb-12">
                    {{ $t("Talent Acquisition") }}, <span class="text-[#21caf3]">{{ $t("Redefined") }}</span>
                </h1>
                <p class="text-lg text-gray-700 text-start">
                    {{ $t("Discover a smarter way to build your team. Our platform blends technology and human expertise to deliver the best candidates, faster and more reliably than ever before.") }}
                </p>
                <div class="flex flex-wrap gap-4 mt-8">
                    <span class="bg-blue-50 text-blue-700 px-4 py-1 rounded-full text-xs font-semibold">95% {{ $t("Satisfaction Rate") }} </span>
                    <span class="bg-green-50 text-green-700 px-4 py-1 rounded-full text-xs font-semibold"> {{ $t("Global Talent Pool") }}</span>
                    <span class="bg-purple-50 text-purple-700 px-4 py-1 rounded-full text-xs font-semibold">{{ $t("Trusted by 100+ Companies") }}</span>
                </div>
            </div>
        </div>
    </section>
</template>

<style>
/* Add subtle animation to feature cards */
.feature-card {
    transition:
        transform 0.3s ease,
        box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
}

/* Fade-in-up animation for CTA section */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.7s ease-out forwards;
}
</style>
<script>
export default {
    data() {
        return {
            features: ["No upfront costs", "Flexible terms", "Vetted experts", "30-day trial"],
            badges: ["Zero interest", "24/7 support", "95% satisfaction"],
        };
    },
};
</script>
