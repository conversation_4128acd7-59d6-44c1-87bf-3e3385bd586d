<template>
    <transition name="toast-slide">
        <div class="toast" v-if="isVisible">
            <div class="toast-content" :style="{ backgroundColor: bgColor === 'success' ? '#53B9AB' : '#E5484D', color: textColor }">
                {{ message }}
            </div>
        </div>
    </transition>
</template>

<script>
export default {
    name: "ToastNotificationCustomQuestions",
    props: {
        message: {
            type: String,
            default: ""
        },
        isVisible: {
            type: Boolean,
            default: false
        },
        bgColor: {
            type: String,
            default: "success"
        },
        textColor: {
            type: String,
            default: "white"
        }
    },
    emits: ['update:isVisible'],
    mounted() {
        if (this.isVisible) {
            this.startAutoHideTimer();
        }
    },
    watch: {
        isVisible(newVal) {
            if (newVal) {
                this.startAutoHideTimer();
            }
        }
    },
    methods: {
        startAutoHideTimer() {
            setTimeout(() => {
                this.$emit('update:isVisible', false);
            }, 3000);
        }
    },
    beforeUnmount() {
        // Clean up any pending timeouts if needed
        if (this.timeout) {
            clearTimeout(this.timeout);
        }
    }
};
</script>

<style scoped>
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;
    box-shadow: 0 0 8px 2px rgba(0, 0, 0, 0.2);
}

.toast-content {
    padding: 12px 24px;
    border-radius: 6px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    min-width: 200px;
    text-align: center;
}

.toast-slide-enter-active {
    transition: all 0.3s ease-out;
}

.toast-slide-leave-active {
    transition: all 0.3s ease-in;
}

.toast-slide-enter-from,
.toast-slide-leave-to {
    transform: translateX(100%);
    opacity: 0;
}

.toast-slide-enter-to,
.toast-slide-leave-from {
    transform: translateX(0);
    opacity: 1;
}
</style>