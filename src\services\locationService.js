export async function checkIPLocation() {
    try {
        // First get the current IP
        const ipResponse = await fetch("https://api.ipify.org?format=json");
        const ipData = await ipResponse.json();
        const currentIP = ipData.ip;

        // Call ipinfo API with the current IP
        const response = await fetch(`https://api.ipinfo.io/lite/${currentIP}?token=bd1353f3aa9304`);
        const data = await response.json();

        return {
            country: data.country_code,
            shouldRedirect: data.country_code === "DZ" && window.location.hostname.includes("go-platform.com"),
        };
    } catch (error) {
        console.error("Error checking location:", error);
        return {
            country: null,
            shouldRedirect: false,
        };
    }
}

export function handleRedirect() {
    // Construct new URL preserving path and query parameters
    const newUrl = new URL(window.location.pathname + window.location.search, "https://go-profiling.dz");
    window.location.href = newUrl.toString();
}
