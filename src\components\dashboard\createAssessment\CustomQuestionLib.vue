<template>
    <AssessementPreview :assessment="preview" :showPreview="showPreview" :togglePreview="togglePreview" :addAssessment="addAssessment" />
    <ScreenerQst v-if="showScreener" :toggleScreener="toggleScreener" />

    <div @scroll="handleScroll" class="libraryWrapper relative">
        <div class="relative flex mt-[4rem] mb-2 items-center justify-between">
            <div class="fixed top-0 left-0 w-full h-full" v-if="isOpen" @click="toggleFilter"></div>

            <div class="relative w-[30%]">
                <font-awesome-icon :icon="['fas', 'search']" class="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 text-sm" />
                <input
                    :placeholder="$t('Search Question')"
                    class="shadow-m text-sm py-1 pl-8 pr-3 border border-[1.5px] rounded-md h-[3rem] w-full border-[#e7e7e9] outline-none focus:border-2 focus:border-NeonBlue"
                    type="text"
                    id="searchInput"
                    name="searchInput"
                    v-model="searchText"
                    required
                />
            </div>
            <div class="flex items-center gap-4">
                <button
                    @click="toggleFilter()"
                    class="bg-white border-[#e7e7e9] !border !border-[1.5px] text-gray-700 inline-flex items-center justify-center text-m rounded min-h-[30px] px-4 py-[10px] font-m outline-none focus:border-2 focus:border-NeonBlue"
                >
                    <font-awesome-icon :icon="['fas', 'sliders-h']" class="mr-2" />
                    Filters <font-awesome-icon class="ml-2" :icon="['fas', 'caret-down']" />
                </button>
            </div>

            <div v-if="isOpen" ref="filterDropdown" class="p-4 absolute border border-gray-300 w-[25%] shadow-xl bg-[#fff] overflow-y-scroll z-10 top-[103%] right-0">
                <div>
                    <h3 class="text-base text-CustomBlue mb-2 font-semibold">{{ $t("Per library") }}:</h3>
                    <label v-for="(library, index) in libraries" :key="index" class="checkbox-label">
                        <input type="checkbox" :value="library" v-model="selectedLibraries" class="checkbox-input" />
                        <span class="checkbox-text">{{ $t(library) }}</span>
                    </label>
                </div>
                <div>
                    <h3 class="text-base text-CustomBlue mb-2 font-semibold">{{ $t("Per category") }}:</h3>
                    <label v-for="(category, index) in categories" :key="index" class="checkbox-label">
                        <input type="checkbox" :value="category" v-model="selectedCategories" class="checkbox-input" />
                        <span class="checkbox-text">{{ $t(category) }}</span>
                    </label>
                </div>
            </div>
        </div>
        <hr class="border border-[#e7e7e9] my-6 w-full" />
        <div v-if="this.Store.isLoadingQuestions" class="loader">
            <LoadingComponent />
        </div>
        <div class="library-loading" v-else>
            <div v-if="filteredAssessments" class="library-grid-top">
                <QuestionCard
                    v-for="(assess, index) in filteredAssessments"
                    :key="index"
                    :assessement="assess"
                    @openPreview="OpenPreview(assess)"
                    :addAssessment="addQst"
                    :deleteSelectedQst="deleteSelectedQst"
                    :selectedQuestions="selectedQuestions"
                />
            </div>
            <div v-if="!this.Store.customQuestionsCompany && onlyMyLibrary" class="flex items-center justify-center w-full h-[300px]">
                <span class="text-center w-full span-no-cheater">{{ $t("Your custom library is currently empty") }}</span>
            </div>
        </div>
    </div>
</template>

<script>
// import AssessementTab from "@/components/assessementTab.vue";
// import AssessementTabs from "@/components/assessementTabs.vue";
// import CardsLoader from "@/components/cardsLoader.vue";

import AssessementPreview from "@/components/dashboard/library/AssessementPreview.vue";
import QuestionCard from "@/components/QuestionCard.vue";
import { useStore } from "@/store/index";
import LoadingComponent from "@/components/LoadingComponent.vue";
import ScreenerQst from "@/components/dashboard/createAssessment/ScreenerQst.vue";

export default {
    name: "CustomQuestionLib",
    components: {
        AssessementPreview,
        QuestionCard,
        LoadingComponent,
        ScreenerQst,
        // AssessementTab,
        // AssessementTabs,
        // CardsLoader,
    },
    props: {
        addNewAssessment: Function,
        deleteSelectedQst: Function,
        addQst: Function,
        selectedQuestions: Array,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    data() {
        return {
            // showTab: { hard: false, soft: false, psy: false, top: false },
            isLoading: true,
            isOpen: false,
            preview: {},
            showPreview: false,
            showPreviewWindow: false,
            isVisible: false,
            TopAssessement: [],
            yourAssessment: [],
            filterContainerTop: 11,

            searchPayload: "",

            psychometrics: [],
            selected: [true, false],
            current: 0,
            score: 0,
            premium: false,
            imagePath: "",
            project_id: "",
            message: "",
            isFixed: false,
            scrollThreshold: 350,
            categories: ["Multiple-choice", "Essay"],
            libraries: ["Go Platform", "My library"],
            selectedCategories: [],
            selectedLibraries: [], // By default select both libraries
            searchText: "",
            showScreener: false,
        };
    },
    computed: {
        onlyMyLibrary() {
            return this.selectedLibraries.length === 1 && this.selectedLibraries[0] === "My library";
        },
        filteredAssessments() {
            return this.Store.allCustomQuestions.filter((assessment) => {
                const matchesSearch = assessment.name.toLowerCase().includes(this.searchText.toLowerCase());
                const mappedCategories = this.selectedCategories.map((category) => {
                    switch (category) {
                        case "Multiple-choice":
                            return "Multiple-choice";
                        case "Essay":
                            return "Essay";
                        default:
                            return category;
                    }
                });
                const mappedLibraries = this.selectedLibraries.map((library) => {
                    switch (library) {
                        case "Go Platform":
                            return "Go Platform";
                        case "My library":
                            return "My library";

                        default:
                            return library;
                    }
                });
                const matchesCategory = mappedCategories.length === 0 || mappedCategories.includes(assessment.category);

                const matchesLibrary =
                    mappedLibraries.length === 0 || (mappedLibraries.includes("Go Platform") && !assessment.company) || (mappedLibraries.includes("My library") && !!assessment.company);

                return matchesSearch && matchesCategory && matchesLibrary;
            });
        },
    },
    methods: {
        toggleScreener() {
            this.showScreener = !this.showScreener;
        },
        toggleCreateAssesment() {
            this.showCreateAssesment = !this.showCreateAssesment;
        },

        toggleFilter() {
            this.isOpen = !this.isOpen;
        },
        handleScroll() {
            if (this.$refs.libraryWrapper.scrollTop < this.scrollThreshold) {
                this.filterContainerTop = this.$refs.libraryWrapper.scrollTop + 40;
            } else {
                this.filterContainerTop = this.$refs.libraryWrapper.scrollTop + 10;
            }
        },
        OpenPreview(test) {
            this.preview = test;
            this.togglePreview();
        },
        togglePreview() {
            this.showPreview = !this.showPreview;
        },
        addAssessment(assessment) {
            this.showPreview = false;
            this.addNewAssessment(assessment);
        },
    },

    async mounted() {
        this.Store.fetchProjects();
        this.id = this.$route.query.id;
        this.project_id = this.$route.query.id ? this.$route.query.id : "";
        this.Store.fetchCustomGoPlatform();
        this.Store.fetchCustomCompany();
    },
};
</script>

<style scoped lang="scss">
.library-grid-top {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    // padding: 1rem 0;
    gap: 2rem;
    width: 100%;
}

.span-no-cheater {
    color: #2195f3cd;
    font-family: DM Sans;
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 32px;
    /* 160% */
    letter-spacing: -0.4px;
}

.libraryWrapper {
    // display: grid;

    .library-loading {
        // margin-left: 20%;
        padding: 0;
        height: auto;
    }
}

.checkbox-label {
    display: flex;
    align-items: center;
    margin-right: 20px;
    /* Adjust spacing between checkboxes */
    margin-bottom: 10px;
}

.checkbox-input {
    margin-right: 5px;
    /* Adjust spacing between checkbox and label */
    background-color: #2196f3;
}

.checkbox-text {
    margin-left: 5px;
    /* Adjust spacing between checkbox and label */
    font-size: 15px;
    font-weight: 400;
}

.loader {
    width: 100%;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
