<template>
    <UpdateAssessment :isShown="isShown" :closePanel="closePanel" />
    <ConfirmDeleteAssessment :closeConfirm="closeConfirm" :isShown="isShownDelete" :id="assessement._id" />
    <div class="top-assessement">
        <div class="relative flex flex-col justify-between h-full w-full px-3 py-3 bg-white">
            <div class="w-full flex-grow-0 flex-shrink-0 flex flex-col">
                <div class="flex relative justify-between items-start">
                    <div class="flex gap-0 pb-1 flex-col justify-start items-start w-full">
                        <h2 class="title-question w-full text-lg font-semibold text-left relative pb-2 mb-2 flex items-start"
                            style="min-height: 56px; max-height: 56px; overflow: hidden; display: flex; align-items: flex-start; justify-content: space-between;">
                            <span class="flex-grow-1 flex-shrink-1"
                                style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; line-clamp: 2; overflow: hidden; margin-right: 5px;">
                                {{ $t(assessement?.name) }}
                            </span>
                            <span
                                class="inline-block w-[110px] px-5 py-1 rounded-full bg-[#ebf5fa] backdrop-blur-sm text-xs font-medium shadow-sm flex-shrink-0"
                                style="white-space: normal; height: auto; min-height: 28px; display: flex; align-items: center; justify-content: center;">
                                {{ $t(assessement?.category) }}
                            </span>
                        </h2>
                    </div>
                    <div class="absolute top-0 right-0">
                    </div>
                    <div v-if="isOpen" ref="container"
                        class="absolute -right-12 bottom-[10%] rounded-md w-25 flex bg-white border border-gray-200 flex-col z-10">
                        <font-awesome-icon @click="openUpdate(assessement._id)"
                            class="cursor-pointer hover:bg-[#94A3B8] rounded-md p-1" :icon="['far', 'pen-to-square']" />
                        <font-awesome-icon @click="isShownDelete = true"
                            class="cursor-pointer rounded-md hover:bg[94A3B8] p-1" :icon="['far', 'trash-can']" />
                    </div>
                </div>

                <p class="text-sm font-light mt-5 text-left assessment-description">
                    {{ getFirstPhrase($t(assessement?.description_test)) }}
                </p>
            </div>

            <div class="w-full flex justify-center flex-shrink-0 mt-auto">
                <div class="flex items-center gap-8 px-2 py-3 relative text-[#94A3B8] rounded-lg w-fit">
                    <span class="flex items-center gap-2">
                        <font-awesome-icon :icon="['fas', 'stopwatch']" class="text-base" />
                        <span>{{ assessement?.questions_nbr > 25 ? parseInt((20 * 35) / 60) :
                            parseInt((assessement?.questions_nbr * 35) / 60) }}'</span>
                    </span>
                    <span class="flex items-center gap-2">
                        <font-awesome-icon
                            :icon="assessement?.category === 'Essay' ? ['fas', 'file-alt'] : ['fal', 'folder']"
                            class="text-base" />
                        <font-awesome-icon :icon="['fas', 'list-check']" class="text-base" />
                        <span>{{ (assessement?.questions_nbr) ? assessement.questions_nbr : 5 }}</span>
                    </span>
                    <span class="flex items-center gap-2">
                        <font-awesome-icon :icon="['fas', 'server']" class="text-base" />
                        <span>{{ !assessement?.company ? "Go Platform" : "My library" }}</span>
                    </span>
                </div>
            </div>
            <div class="w-full flex justify-between gap-4 items-center mt-4 flex-shrink-0">
                <button
                    class="flex-1 text-gray-900 border border-gray-300 hover:border-CustomBlue hover:text-CustomBlue text-m rounded min-h-[30px] px-4 py-[10px] font-semibold text-center"
                    @click="$emit('openPreview')">
                    {{ $t("Details") }}
                </button>
                <div v-if="!verifyAddedQuestion"
                    class="flex-1 bg-CustomBlue text-white hover:opacity-85 text-m rounded min-h-[30px] px-4 py-[10px] font-semibold text-center relative cursor-pointer flex items-center justify-center gap-2"
                    @click="addAssessment(assessement)">
                    <div v-if="assessement?.rating !== 'essential' && !this.Store.premium"
                        class="absolute right-[-10px] top-[-15px] text-[#FFC300] w-[30px] h-[30px] rounded-[50%] text-xl flex justify-center items-center bg-white"
                        style="border: 3px solid #ffc300">
                        <font-awesome-icon :icon="['far', 'gem']" />
                    </div>
                    <font-awesome-icon :icon="['fas', 'plus-circle']" class="text-white text-lg" />
                    <span>{{ $t("Add now") }}</span>
                </div>
                <button v-else @click="removeAssessment(assessement)"
                    class="flex-1 bg-red-600 text-white hover:opacity-85 text-m rounded min-h-[30px] px-4 py-[10px] font-semibold text-center">
                    <font-awesome-icon class="text-white" :icon="['fas', 'xmark']" />
                </button>
            </div>
        </div>
    </div>
</template>





<script>
import { useStore } from "@/store";
import UpdateAssessment from "@/components/UpdateAssessment";
import ConfirmDeleteAssessment from "@/components/ConfirmDeleteAssessment";

export default {
    name: "TopAssessement",
    props: ["assessement", "addAssessment", "placeholders", "removeAssessment"],
    data() {
        return {
            imagePath: "",
            isOpen: false,
            isShown: false,
            isShownDelete: false,
        };
    },
    components: {
        UpdateAssessment,
        ConfirmDeleteAssessment,
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    computed: {
        verifyAddedQuestion() {
            return !!this.placeholders.find((placeholder) => placeholder?.assessment?.name === this.assessement?.name);
        },
    },
    methods: {
        closeConfirm() {
            this.isShownDelete = false;
        },
        openUpdate(id) {
            this.isShown = true;
            this.Store.assessmentId = id;
        },
        closePanel() {
            this.isShown = false;
        },
        getFirstPhrase(description) {
            if (!description) return '';
            // Replace all whitespace (including line breaks, tabs) with a single space, then trim
            let cleanDesc = description.replace(/\s+/g, ' ').trim();
            let words = cleanDesc.split(' ').filter(Boolean);
            if (words.length <= 10) {
                return cleanDesc;
            } else {
                return words.slice(0, 15).join(" ") + '...';
            }
        },
       
        formatName(name) {
            let newName = name.replace("-", " ");
            return newName;
        },
    },
};
</script>

<style lang="scss" scoped>
.top-assessement {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    background: #fff;
    border-radius: 10px;
    border: 1px solid #e5e7eb;
    padding: 0.2rem;
    gap: 2rem;
    padding-bottom: 1rem;
    background: #ffffff;
    transition: all 0.3s ease;
    height: 300px;
    /* This is already making the overall card rigid */
    width: 100%;
}

.assessment-description {
    /* Set a max-height for the description and hide overflow */
    max-height: 60px;
    /* Adjust this value as needed to fit your desired number of lines (e.g., 3-4 lines of text-sm) */
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    /* Limit to 3 lines for Webkit browsers */
    -webkit-box-orient: vertical;
    line-clamp: 3;
    /* Standard property for future compatibility */
    text-overflow: ellipsis;
    /* Fallback for older browsers if line-clamp isn't supported */
    margin-top: 5px;
    /* Adjust margin if needed after fixing height */
}
</style>
