# https://github.com/kentcdodds/kentcdodds.com/blob/main/.github/workflows/deployment.yml
name: Code Check
on:
    push:
        branches:
            - main
            - master
            - develop
    pull_request: {}

jobs:
    lint:
        name: ⬣ ESLint
        runs-on: ubuntu-latest
        steps:
            - name: ⬇️ Checkout repo
              uses: actions/checkout@v2

            - name: ⎔ Setup node
              uses: actions/setup-node@v2
              with:
                  node-version: 16

            - name: 📥 Download deps
              uses: bahmutov/npm-install@v1

            - name: 🔬 Lint
              run: yarn lint:strict

    prettier:
        name: 💅 Prettier
        runs-on: ubuntu-latest
        steps:
            - name: ⬇️ Checkout repo
              uses: actions/checkout@v2

            - name: ⎔ Setup node
              uses: actions/setup-node@v2
              with:
                  node-version: 16

            - name: 📥 Download deps
              uses: bahmutov/npm-install@v1

            - name: 🔎 Type check
              run: yarn format:check
