<template>
    <div class="flex justify-between flex-col lg:flex-row px-6 py-[5%] overflow-hidden">
        <div class="w-full lg:w-[40%] text-center lg:text-left mt-[5%]">
            <h1 class="uppercase font-[700] text-[46px] text-[#343637]">
                {{ $t("How to get") }}
                <span class="relative w-[fit-content] inline-block">
                    <span class="absolute top-0 left-0 w-full h-full -rotate-[6deg]"></span>
                    <span class="relative z-5">{{ $t("Started?") }}</span>
                </span>
            </h1>
            <p class="text-[16px] text-[#343637] mt-[5%]">{{ $t(" We handle all the complexities of managing a global team, so you can focus on growth!") }}</p>
            <hr class="h-[5px] bg-[#F5F8FF] my-[5%]" />
            <h1 class="text-[16px] text-[#343637]">{{ $t("GO PLATFORM help you find") }}</h1>
            <div class="flex flex-col gap-3 mt-[3%]">
                <h3 class="text-[16px] text-[#343637] flex items-center">
                    <font-awesome-icon :icon="['fas', 'check-circle']" class="text-NeonBlue mr-4 text-xl" />
                    {{ $t("The Right Person") }}
                </h3>
                <h3 class="text-[16px] text-[#343637] flex items-center">
                    <font-awesome-icon :icon="['fas', 'check-circle']" class="text-NeonBlue mr-4 text-xl" />
                    {{ $t("The Right Skills") }}
                </h3>
                <h3 class="text-[16px] text-[#343637] flex items-center">
                    <font-awesome-icon :icon="['fas', 'check-circle']" class="text-NeonBlue mr-4 text-xl" />
                    {{ $t("At the Right Cost") }}
                </h3>
            </div>
        </div>
        <div class="w-[fit-content] hidden md:block">
            <div style="width: 692.15px; height: 646.01px; position: relative">
                <div style="width: 537.75px; height: 0px; left: 346.07px; top: 108.26px; position: absolute; transform: rotate(90deg); transform-origin: 0 0; border: 0.89px #cbe1ef dotted"></div>
                <div style="width: 373.58px; height: 161.5px; left: 0px; top: 484.51px; position: absolute">
                    <div
                        class="bg-[#fff] shadow-[-10px_-8px_4px_-3px_#f5f8ff] hover:shadow-none transition-all duration-300 hover:scale-105 absolute left-24 top-0 w-72 h-40 rounded-xl overflow-hidden"
                        style="width: 274.2px; height: 161.5px; left: 0px; top: 0px; position: absolute; border-radius: 13.57px; overflow: hidden; border: 0.15px #f5f8ff solid"
                    >
                        <div
                            style="
                                width: 256.37px;
                                height: 106.32px;
                                left: 9.76px;
                                top: 35.51px;
                                position: absolute;
                                flex-direction: column;
                                justify-content: center;
                                align-items: center;
                                gap: 7.54px;
                                display: inline-flex;
                            "
                        >
                            <div class="text-left" style="width: 209.62px; height: 106.32px">
                                <span class="font-bold leading-5 text-base break-words">{{ $t("Pay us Later") }}</span>
                                <span class="text-[13px] text-[#343637] break-words block mt-1">
                                    {{ $t(" You can make a payment plan or pay all at once in 6 months.") }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-[318.57px] top-[53.24px] w-[55.02px] h-[55.02px] bg-white rounded-full"></div>
                    <div class="w-[17.75px] h-[21.3px] left-[337.2px] top-[65.1px] absolute text-center text-[#2196f3] text-[21.3px] font-bold leading-[34.08px] break-words">4</div>
                </div>
                <div style="width: 373.58px; height: 161.5px; left: 318.56px; top: 323px; position: absolute">
                    <div
                        class="bg-[#fff] shadow-[-10px_-8px_4px_-3px_#f5f8ff] hover:shadow-none transition-all duration-300 hover:scale-105 absolute left-24 top-0 w-72 h-40 rounded-xl overflow-hidden"
                    >
                        <div
                            style="
                                width: 256.37px;
                                height: 106.32px;
                                left: 9.76px;
                                top: 10.51px;
                                position: absolute;
                                flex-direction: column;
                                justify-content: flex-start;
                                align-items: center;
                                gap: 7.54px;
                                display: inline-flex;
                            "
                        >
                            <div class="text-left" style="width: 209.62px; height: 106.32px">
                                <span class="font-bold leading-5 text-base break-words">{{ $t("Deployment and Support") }}</span>
                                <span class="text-[13px] text-[#343637] break-words block mt-1">
                                    {{ $t(" We handle interview scheduling, legalities, onboarding, and talent management to ensure a stress - free recruitment process") }}</span
                                >
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 top-[53.24px] w-[55.02px] h-[55.02px] bg-white rounded-full"></div>
                    <div class="w-[17.75px] h-[21.3px] left-[18.63px] top-[65.1px] absolute text-center text-[#2196f3] text-[21.3px] font-bold leading-[34.08px] break-words text-left">3</div>
                </div>
                <div style="width: 373.58px; height: 161.5px; left: 0px; top: 161.5px; position: absolute">
                    <div
                        class="bg-[#fff] shadow-[-10px_-8px_4px_-3px_#f5f8ff] hover:shadow-none transition-all duration-300 hover:scale-105 absolute left-0 top-0 w-72 h-40 rounded-xl overflow-hidden"
                    >
                        <div
                            style="
                                width: 256.37px;
                                height: 106.32px;
                                left: 9.76px;
                                top: 10.51px;
                                position: absolute;
                                flex-direction: column;
                                justify-content: flex-start;
                                align-items: center;
                                gap: 7.54px;
                                display: inline-flex;
                            "
                        >
                            <div class="Text text-left" style="width: 209.62px; height: 106.32px">
                                <span class="font-bold leading-5 text-base break-words">{{ $t("Tailored Solutions") }}</span>
                                <span class="text-[13px] text-[#343637] break-words block mt-1">{{
                                    $t(
                                        "Our team will filter and vet through a talent pool of skilled experts based on our discovery call to match you with the best-fit profile/s within 72 business hours",
                                    )
                                }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-[318.57px] top-[53.24px] w-[55.02px] h-[55.02px] bg-white rounded-full"></div>
                    <div class="w-[17.75px] h-[21.3px] left-[337.2px] top-[65.1px] absolute text-center text-[#2196f3] text-[21.3px] font-bold leading-[34.08px] break-words text-left">2</div>
                </div>
                <div style="width: 373.58px; height: 161.5px; left: 318.56px; top: 0px; position: absolute">
                    <div
                        class="bg-[#fff] shadow-[-10px_-8px_4px_-3px_#f5f8ff] hover:shadow-none transition-all duration-300 hover:scale-105 absolute left-24 top-0 w-72 h-40 rounded-xl overflow-hidden"
                    >
                        <div
                            style="
                                width: 256.37px;
                                height: 106.32px;
                                left: 9.76px;
                                top: 10.51px;
                                position: absolute;
                                flex-direction: column;
                                justify-content: flex-start;
                                align-items: center;
                                gap: 7.54px;
                                display: inline-flex;
                            "
                        >
                            <div class="text-left" style="width: 209.62px; height: 106.32px">
                                <span class="font-bold leading-5 text-base break-words">{{ $t("Discovery and Analysis") }}</span>
                                <span class="text-[13px] text-[#343637] break-words block mt-1">{{
                                    $t("Understanding business needs to get a good grasp on the roles, experience and must-have skills you are looking for.")
                                }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="absolute left-0 top-[53.24px] w-[55.02px] h-[55.02px] bg-white rounded-full"></div>
                    <div class="w-[17.75px] h-[22.3px] left-[18.63px] top-[65.1px] absolute text-center text-[#2196f3] text-[21.3px] font-bold leading-[34.08px] break-words font-lexend text-left">
                        1
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "HowToGetStarted",
};
</script>
