<template>
    <div class="blogs-page">
        <section id="" class="search-container">
            <div class="center-side">
                <h1>{{ $t("Insights from our team") }}</h1>
                <p>{{ $t("Powerful Trading Tools and Features for Experienced Investors") }}</p>

                <input class="srchbtn" type="text" :placeholder="$t('Search Resources...')" v-model="searchQuery" />

                <div class="trending-section">
                    <div class="trending-header">
                        <span class="title">{{ $t("Popular searches:") }}</span>
                        <div class="topics-container">
                            <span
                                class="topics"
                                v-for="category in trendingCategoriesList"
                                :class="{ 'trending-actif': $route.query.category === category.category }"
                                @click="
                                    getByCategory(category._id);
                                    getCategoryName(category.category);
                                "
                                :key="category._id"
                                >{{ $t(category.category) }}</span
                            >
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Problem start here -->

        <section class="blogs-container">
            <div>
                <div class="blog-cards">
                    <div class="article-card-wrapper-main">
                        <!-- Featured Article (First Card) -->

                        <div v-if="$route.query.category === 'All' && filteredArticles.length > 0" class="featured-article-card">
                            <div class="featured-image-container">
                                <div class="featured-image-container">
                                    <img loading="lazy" decoding="async" :src="getImgUrl(filteredArticles[0].image)" :alt="filteredArticles[0].title" />
                                </div>
                            </div>
                            <router-link :to="`/blog/${filteredArticles[0]._id}`">
                                <div class="featured-content">
                                    <h2>{{ filteredArticles[0].title }}</h2>
                                    <p>{{ $t("Our top read article this week") }}</p>
                                    <!-- <p>{{ filteredArticles[0].description }}</p>-->
                                    <div class="featured-read-more">{{ $t("Continue reading") }}</div>
                                </div>
                            </router-link>
                        </div>
                    </div>
                    <div class="article-card-wrapper-main">
                        <!-- Regular Articles -->
                        <div class="regular-articles-grid">
                            <ArticleCardBlog
                                v-for="article in $route.query.category === 'All' ? filteredArticles.slice(1) : filteredArticles"
                                :key="article._id"
                                :id="article._id"
                                :author="article.author"
                                :image="article.image"
                                :description="article.description"
                                :title="article.title"
                                :category="article.category.category"
                                :time="article.reading_time"
                                :text="article.text"
                                :date="getDate(article.date)"
                            />
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <section class="cta-container">
        <div class="flex flex-col justify-start items-start mx-auto">
            <h1 class="font-bold text-5xl text-slate-700 text-start w-full mb-6">{{ $t("Re-invent your hiring process now.") }}</h1>

            <div class="flex flex-col lg:flex-row justify-start items-start gap-4 w-full">
                <a
                    href="https://calendly.com/aouf-abdellah/20min"
                    target="_blank"
                    class="whitespace-nowrap text-gray-900 font-semibold border-2 border-gray-200 rounded-md hover:border-CustomBlue hover:text-CustomBlue transition-colors px-4 py-2 flex items-center justify-center bg-white"
                >
                    {{ $t("Schedule a demo") }}
                </a>
            </div>
        </div>
    </section>
</template>

<script>
import "vue3-carousel/dist/carousel.css";
//import { Carousel, Slide } from "vue3-carousel";
//import articleCard from "@/components/articleCard.vue";
import ArticleCardBlog from "@/components/ArticleCardBlog.vue";
import axios from "axios";
import { BASE_URL } from "@/constants";
// import cardsLoader from "@/components/cardsLoader.vue"
import { useStore } from "@/store/index";
//import LoaderComponent from "@/components/LoaderComponent.vue";
// import axios from "axios";
export default {
    name: "BlogsPage",
    components: {
        // articleCard,
        // Carousel,
        //Slide,
        ArticleCardBlog,
        // LoaderComponent
    },
    data() {
        return {
            grid: false,

            loading: false,
            title: "",

            articlesList: [],
            searchQuery: "",

            trendingCategoriesList: [],
            loadingCategories: true,
        };
    },
    computed: {
        articles() {
            return this.Store.getArticles;
        },
        loadingArticles() {
            return this.Store.getLoadingArticles;
        },

        filteredArticles() {
            return this.Store.articles.filter((article) => article.title.toLowerCase().includes(this.searchQuery.toLowerCase()));
        },
    },
    methods: {
        getImgUrl(imgFileId) {
            if (imgFileId) {
                var image = `data:image/png;base64,${imgFileId}`;
                return image;
            }
            return require("@/assets/Images/user.png");
        },
        getDate(str) {
            const date = new Date(str);

            const options = {
                year: "numeric",
                month: "long",
                day: "numeric",
            };

            const formattedDate = date.toLocaleDateString("en-US", options);
            return formattedDate;
        },
        getCategoryName(categoryName) {
            this.$router.push({ query: { category: categoryName } });
        },
        getByCategory(id) {
            this.loading = true;
            this.category = id;
            axios
                .post(
                    `${BASE_URL}/articles/data-category`,
                    { category: this.category },
                    {
                        withCredentials: true,
                    },
                )
                .then((response) => (this.Store.articles = response.data))
                .catch((err) => {
                    console.log(err);
                })
                .finally(() => {
                    this.loading = false;
                });
        },
        async getAllBlogs() {
            this.$router.push({ query: { category: "All" } });
            try {
                await this.Store.fetchArticles();
            } catch (error) {
                console.error("Error fetching articles:", error);
            }
        },
        getCategories() {
            axios
                .get(`${BASE_URL}/categories/`, {
                    withCredentials: true,
                })
                .then((response) => (this.categoriesList = response.data.categories))
                .catch((err) => {
                    console.log(err);
                });
        },

        getTrendingCategories() {
            axios
                .get(`${BASE_URL}/categories/`, {
                    withCredentials: true,
                })
                .then((response) => (this.trendingCategoriesList = response.data.categories.slice(0, 3)))
                .catch((err) => {
                    console.log(err);
                });
        },
        sendContactUs() {
            this.loading = true;
            let contactForm = {
                email: this.email,
                name: this.name,
                title: this.title,
                message: this.message,
            };
            axios
                .post(`${BASE_URL}/contact-us`, contactForm, {
                    withCredentials: true,
                })
                .catch((err) => {
                    console.log(err);
                })
                .finally(() => {
                    this.loading = false;
                    this.email = "";
                    this.name = "";
                    this.title = "Report a bug";
                    this.message = "";
                });
        },
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    mounted() {
        this.Store.fetchArticles();
        this.$router.push({ query: { category: "All" } });
        this.getCategories();
        this.getTrendingCategories();
        this.Store.observeIntersection();
    },
};
</script>

<style scoped>
.srchbtn {
    margin-top: 15px;
    /* or whatever */
    margin-bottom: 30px;
    width: 40%;
    border-radius: 6px;
    outline: none;

    &:focus {
        border: 1.5px solid #2196f3;
    }
}

.blogs-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 45px;
    width: 100%;
    background: #fff;
}

.search-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8rem 0rem 0rem 0rem;
    width: 100%;
    flex-direction: column;
    height: auto;
}

.search-container p {
    color: #231f3e;
    font-family: Roboto;
    font-size: 15px;
    font-weight: 300;
    margin-bottom: 15px;
    line-height: 10px;
    text-align: justify;
}

.search-container h1 {
    color: #1e1e1e;
    font-family: Roboto;
    font-size: 55px;
    font-weight: 700;
    margin-bottom: 15px;
}

.center-side {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    width: 100%;
    height: 100%;
}

input {
    width: 70%;
    border-radius: 60px;
    border: 1px solid #dadada;
    background: #fff;
    padding: 0.5rem;
}

.blogs-container {
    display: flex;
    justify-content: center;
    width: 80%;
    row-gap: 20px;
    border-bottom: 1px solid #c9cbd1;
    padding: 2rem 2rem;
    /* Explicitly set alignment */
}

.cta-container {
    width: 100%;
    background: linear-gradient(to right, #f8fafc 0%, #f1f5f9 100%);
    padding: 3rem 3rem;
    border-top: 1px solid #e2e8f0;
    border-bottom: 1px solid #e2e8f0;
}

.cta-container > div {
    width: 100%;

    /* Adjust side padding as needed */
}

.blogs-filtering {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.topics-list {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    width: 100%;
    margin-bottom: 3rem;
}

.trending-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.trending-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px;
}

.title {
    color: #7d7d7d;
    font-family: Roboto;
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
}

.topics-container {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 15px;
}

.topics {
    border-radius: 6px;
    background: #f3f4f6;
    color: #797979;
    font-family: Roboto;
    font-size: 13px;
    font-weight: 300;
    padding: 3px 10px;
    cursor: pointer;
    white-space: nowrap;
}

.topics:hover {
    background: rgb(229 231 235);
}

.trending-actif {
    background: rgb(229 231 235);
}

.topics-list > a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    padding: 8px 16px;
    font-size: 14px;
    border: 2px solid #e3e3e3;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
}

.blog-cards {
    display: flex;
    flex-direction: column;
    width: 100%;
    padding-bottom: 50px;
}

.article-card-wrapper {
    display: flex;
    width: 50%;
    flex-direction: column;
}

.article-card-wrapper-main {
    display: flex;
    width: 100%;
    flex-direction: column;
    row-gap: 50px;
    align-items: stretch;
    margin-top: 50px;
    /* Makes children fill full width */
}

/* Desktop styles */

/* Mobile styles */
@media (max-width: 768px) {
    .blogs-container {
        width: 90%;
        flex-direction: column;
    }

    .article-card-wrapper-main {
        margin-top: 0px;
    }
    .featured-image-container img {
        width: 120%;
    }
}

/* For the grid layout when category is All */
.article-card-wrapper.all-category {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.featured-article {
    grid-column-start: 1;
    grid-column-end: 4;
    grid-row-start: 1;
    grid-row-end: 3;
    border-radius: 1.5rem;
}

/* Regular article card styling */
.article-card {
    /* Your existing card styles */
    height: 100%;
}

/* Make sure the featured article has different styling */
.featured-article .article-card {
    height: auto;
    min-height: 400px;
    /* Adjust as needed */
}

/* Responsive adjustments */
@media (max-width: 1024px) {
    .article-card-wrapper.all-category {
        grid-template-columns: repeat(2, 1fr);
    }

    .featured-article {
        grid-column-end: 3;
    }
}

@media (max-width: 768px) {
    .article-card-wrapper.all-category {
        grid-template-columns: 1fr;
    }

    .featured-article {
        grid-column-end: 2;
        grid-row-end: 2;
    }
}

/* Featured Article Styling */
.featured-article-card {
    display: flex;
    flex-direction: row-reverse;
    margin-bottom: 40px;
    border-radius: 10px;
    overflow: hidden;
    background-color: #ffffff;
    width: 89%;
    align-self: center;
    box-shadow: #eaecee 0.3px 1.5px 1.3px 0.3px;
}

.featured-image-container {
    width: 95%;
}

.featured-image-container img {
    width: 90%;
    height: auto;
    max-height: 16rem;
    object-fit: cover;
    border-radius: 1.5rem;
    border-style: solid;
    border-width: 10px 10px;
    border-color: #f9f9f9;
    box-shadow:
        0 10px 15px -3px rgba(0, 0, 0, 0.1),
        0 4px 6px -2px rgba(0, 0, 0, 0.05);
    margin-top: 40px;
}

.featured-content {
    padding: 30px;
    text-align: start;
}

.featured-content h2 {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1e1e1e;
    /* Remove text-align: start since parent has it now */
}

.featured-content p {
    font-size: 18px;
    line-height: 1.6;
    color: #7d7d7d;
    margin-bottom: 24px;
    /* Remove text-align: start since parent has it now */
}

.featured-read-more {
    display: inline-block;
    /* Change to inline-block for proper alignment */
    margin-top: 50px;
    color: #3789ff;
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    /* Remove align-items: start as it's not needed with inline-block */
    /* Remove text-align: start since parent has it now */
    position: relative;
    /* For arrow positioning */
    padding-right: 24px;
    /* Space for arrow */
}

.featured-read-more::after {
    content: "→";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    margin-left: 8px;
    transition: transform 0.3s ease;
}

.featured-read-more:hover::after {
    transform: translateY(-50%) translateX(4px);
}

/* Regular Articles Grid */
.regular-articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    align-items: start;

    /* Align items at the start */
}

@media (max-width: 1024px) {
    .featured-content h2 {
        font-size: 28px;
    }
}

@media (max-width: 768px) {
    .featured-article-card {
        margin-bottom: 30px;
    }

    .featured-content {
        padding: 20px;
    }

    .featured-content h2 {
        font-size: 24px;
    }

    .featured-content p {
        font-size: 16px;
    }

    .regular-articles-grid {
        grid-template-columns: 1fr;
    }

    .featured-article-card {
        flex-direction: column;
    }

    .center-side p {
        text-align: center;
        line-height: 15px;
        margin-left: 5px;
        margin-right: 5px;
    }

    .srchbtn {
        display: none;
    }
}
</style>
