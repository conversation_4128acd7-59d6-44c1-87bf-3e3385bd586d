<template>
    <section class="px-8 sm:px-8 lg:px-16 py-24 sm:py-32 w-full">
        <div class="mx-auto max-w-7xl">
            <div class="flex flex-col items-center">
                <div class="text-center">
                    <h2 class="mt-4 text-3xl font-bold text-gray-900 sm:text-4xl xl:text-5xl font-pj">{{ $t("Our happy clients say about us") }}</h2>
                </div>

                <div class="relative mt-10 md:mt-24 md:order-2">
                    <div class="relative grid max-w-lg grid-cols-1 gap-6 mx-auto md:max-w-none lg:gap-10 md:grid-cols-3">
                        <div v-for="(testimonial, index) in visibleTestimonials" :key="index" class="flex flex-col overflow-hidden rounded-3xl ring-1 ring-gray-200">
                            <div class="flex flex-col justify-between flex-1 p-6 bg-white lg:py-8 lg:px-7">
                                <div class="flex-1">
                                    <div class="flex items-center">
                                        <svg v-for="(star, starIndex) in 5" :key="starIndex" class="w-5 h-5 text-[#FDB241]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path
                                                d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"
                                            />
                                        </svg>
                                    </div>

                                    <blockquote class="flex-1 mt-8">
                                        <p class="text-lg leading-relaxed text-gray-600 font-pj">{{ $t(testimonial.text) }}</p>
                                    </blockquote>
                                </div>

                                <div class="flex items-center mt-8">
                                    <div class="flex-shrink-0 flex items-center justify-center w-12 h-12 rounded-full overflow-hidden">
                                        <img :src="testimonial.photo" :alt="testimonial.name + ' photo'" class="w-full h-full object-cover" />
                                    </div>
                                    <div class="ml-4 text-left">
                                        <p class="text-base font-bold text-gray-900 font-pj">{{ $t(testimonial.name) }}</p>
                                        <p class="mt-0.5 text-sm font-pj text-gray-600">{{ $t(testimonial.role) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-8 text-center md:mt-16 md:order-3">
                    <button
                        @click="showAllReviews = !showAllReviews"
                        class="px-4 py-2 text-base font-medium leading-7 text-gray-900 transition-all duration-200 border border-gray-400 rounded-md hover:text-[#2196f3] hover:bg-white"
                    >
                        {{ $t(showAllReviews ? "Show less reviews" : "Check all reviews") }}
                    </button>
                </div>
            </div>
        </div>
    </section>
</template>

<script>
export default {
    data() {
        return {
            showAllReviews: false,
            testimonials: [
                {
                    name: "Esteban Salsano",
                    role: "CEO at Odev.tech",
                    text: "“We use Go Platform and we love Go Platform!”",
                    photo: require("@/assets/Images/Esteban-Salsano.jpg"),
                },
                {
                    name: "Daniel Redmen",
                    role: "Business Performance Advisor at Insperity",
                    text: "“Love this, I'm referring all my network to your business.”",
                    photo: require("@/assets/Images/Daniel-Redmen.png"),
                },
                {
                    name: "Meriem Terki",
                    role: "General Manager at MT Consulting LTD",
                    text: "“Simple, efficient, and underrated. You should increase your pricing.”",
                    photo: require("@/assets/Images/Meriem-Terki.png"),
                },
                {
                    name: "Mounir Taibi",
                    role: "People & Organization Head at Holcim",
                    text: "“We never thought that we can use AI with skills, this is a game changer for Talents in our organization.”",
                    photo: require("@/assets/Images/Mounir-Taibi.png"),
                },
            ],
        };
    },

    computed: {
        visibleTestimonials() {
            return this.showAllReviews ? this.testimonials : this.testimonials.slice(0, 3);
        },
    },
};
</script>
