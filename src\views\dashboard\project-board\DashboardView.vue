<template>
    <InviteCandidate :showEmail="showEmail" :assessmentsLength="project.assessments" :toggleEmail="toggleEmail" :projectId="project._id" />
    <AllRatings :viewAllRatings="viewAllRatings" :toggleRatings="toggleRatings" :candidatesRating="candidatesRating" />
    <ProjectPopup v-if="this.showpopup" @close="closePopup" @invite-candidates="handleInviteCandidates" @try-assessment="handleTryAssessment" />
    <div v-if="isLoading" class="loader">
        <LoadingComponent type="job-details" />
    </div>
    <div v-else class="mb-40">
        <!-- Header Stats -->
        <div class="grid grid-cols-4 gap-4">
            <StatsCard title="Success Rate" :value="getSuccessRate() + '%'" :iconIndex="2" />

            <StatsCard title="Completion" :value="isNaN(compitionRate) ? 0 : compitionRate + '%'" :iconIndex="3" />

            <StatsCard title="Applicants" :value="this.candidates.length" :iconIndex="0" />

            <StatsCard title="Assessements" :value="this.project.assessments?.length" :iconIndex="1" />
        </div>
        <div class="board-container">
            <div class="w-full flex flex-col-reverse lg:flex-row justify-between p-3 bg-white border-b rounded mb-5 shadow-card">
                <div class="flex">
                    <Popper :content="$t('Back')" placement="top" :hover="true">
                        <button
                            class="menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 my-1 hidden lg:block"
                            @click="
                                () => {
                                    this.$router.go(-1);
                                }
                            "
                        >
                            <font-awesome-icon :icon="['fas', 'angle-left']" />
                        </button>
                    </Popper>
                    <div class="mx-4 flex flex-col">
                        <h2 class="projData bg-custom-gradient bg-clip-text text-transparent">
                            {{ project.name ? project.name : "Untitled" }}
                            {{ project.seniority && " - " + project.seniority }}
                            {{ project.jobTitle && " - " + project.jobTitle }}
                        </h2>
                        <div class="flex" style="color: #2196f3">
                            <div class="flex items-center">
                                <font-awesome-icon :icon="['far', 'file-lines']" class="mx-2" />
                                <p>{{ filteredAssessmentsLength }} {{ $t("tests") }}</p>
                            </div>
                            <div class="flex mx-2 items-center">
                                <font-awesome-icon :icon="['far', 'clock']" class="mx-2" />
                                <p>{{ totalDuration }} {{ $t("minutes") }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex gap-4 items-center justify-start relative">
                    <button
                        class="menuBtn bg-[#E0E4EC] text-gray-700 hover:bg-black/10 lg:hidden block"
                        @click="
                            () => {
                                this.$router.go(-1);
                            }
                        "
                    >
                        <font-awesome-icon :icon="['fas', 'angle-left']" />
                    </button>
                    <Popper :content="$t('Edit, Duplicate and Delete')" placement="top" :hover="true">
                        <button
                            @click="toggleProjectMenu"
                            style="cursor: pointer"
                            class="menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 text-slate-700 px-4 font-light text-sm py-3 my-1 items-center justify-center"
                        >
                            <font-awesome-icon :icon="['fas', 'ellipsis-vertical']" />
                        </button>
                    </Popper>
                    <ProjectMenu
                        :showDetails="showMenu"
                        :toggleMenu="toggleProjectMenu"
                        :editable="invitations.length > 0 ? false : true"
                        :thisProject="project"
                        :hideSample="true"
                        class="absolute top-0 right-0"
                        style="position: absolute; right: 20%"
                    />
                    <Popper :content="$t('Preview')" placement="top" :hover="true">
                        <div
                            class="text-slate-700 px-4 font-light text-sm py-3 my-1 menuBtn text-gray-700 border-[1.5px] border-gray-200 hover:bg-gray-50 flex items-center justify-center cursor-pointer"
                            @click.stop
                            @click="navigateToPreview"
                        >
                            <!-- <p>{{ $t("Preview") }}</p> -->
                            <font-awesome-icon :icon="['fas', 'eye']" class="w-5 h-5" />
                        </div>
                    </Popper>
                    <Popper :content="$t('Invite candidates')" placement="top" :hover="true">
                        <button
                            ref="targetElement3"
                            @click="toggleEmail"
                            class="nextStep ml-auto px-4 py-2 text-white bg-NeonBlue rounded-md hover:bg-white hover:text-NeonBlue border border-[#2196f3] transition font-normal tracking-[0.6018px]"
                        >
                            {{ $t("Invite") }}
                        </button>
                    </Popper>
                </div>
            </div>

            <div class="navigation-tabs bg-white h-[62px] rounded shadow-card">
                <div class="nav-links font-inter text-md pt-5 font-bold text-left decoration-skip-ink-none pt-5 flex flex-row justify-between items-center gap-4 text-[#343637] h-full">
                    <router-link
                        to="#"
                        @click.prevent="navigateToDashBoard(project)"
                        class="text-gray-600 hover:text-blue-500 focus:text-blue-600 visited:text-purple-600 transition"
                        :class="`${this.$route.path == `/${locale}/boards` ? 'active' : ''}`"
                    >
                        <span>{{ $t("Summary") }}</span>
                    </router-link>
                    <router-link to="#" @click.prevent="navigateToDetailBoard(project)" :class="`${this.$route.path == `/${locale}/Details` ? 'active' : ''}`">
                        <span>{{ $t("Details") }}</span>
                    </router-link>

                    <router-link to="#" @click.prevent="navigateToCheatBoard(project)" :class="`${this.$route.path == `/${locale}/CheatTab` ? 'active ' : ''}`">
                        <span>{{ $t("Anti cheat") }}</span>
                    </router-link>

                    <router-link to="#" class="disabled" @click.prevent="navigateToVideoBoard(project)" :class="`${this.$route.path == `/${locale}/DashVideo` ? 'active ' : ''}`">
                        <div class="absolute top-[-15px] right-[-25px] rounded-[15px] text-[12px] px-2 text-[#fff] bg-[#2371b6]">Soon</div>
                        <span class="hidden lg:block">{{ $t("Video Interview") }}</span>
                    </router-link>
                </div>
            </div>

            <div class="flex flex-col gap-4 mb-4">
                <!-- Top row with Job Summary and Included Tests -->
                <div class="flex flex-row gap-4">
                    <!-- Job Summary -->
                    <CardDetail width="w-3/5">
                        <template #header>
                            <span class="recentapp">{{ $t("Job Summary") }}</span>
                        </template>
                        <template #body>
                            <p class="text-gray-700">
                                {{
                                    $t(
                                        "A flexible and responsible team member focused on supporting tasks, improving processes, and contributing to overall success.Works well with others, adapts to change, and helps achieve shared goals.",
                                    )
                                }}
                                <b>{{ project.jobTitle && " " + project.jobTitle }} </b>
                            </p>
                        </template>
                    </CardDetail>

                    <div class="w-2/5 lg:w-2/5 p-2 rounded-md shadow-md bg-white">
                        <div class="flex justify-between mb-4 items-center mt-7 p-3">
                            <span class="recentapp bg-custom-gradient bg-clip-text text-transparent"> {{ $t("Included Tests") }}</span>
                            <ButtonComponent v-if="!isLoading" :action="toggleWeighting" intent="primary">
                                <p class="whitespace-nowrap">
                                    {{ $t("Edit weights") }}
                                </p>
                            </ButtonComponent>
                        </div>
                        <div class="flex flex-col items-center mt-1">
                            <div class="w-[100%] p-1">
                                <table class="w-full table-auto text-sm" v-if="project?.assessments?.length > 0">
                                    <!-- Standard Assessments Header -->
                                    <thead class="text-black-700 font-semibold">
                                        <tr class="border-b m-[4px] text-black font-bold">
                                            <th colspan="10" class="h-11 px-4 text-left font-medium">{{ $t("Test") }}</th>
                                            <th colspan="2" class="h-11 px-4 font-medium text-center">{{ $t("Weight") }}</th>
                                            <th colspan="2" class="h-11 px-4 text-center font-medium">{{ $t("Duration") }}</th>
                                        </tr>
                                    </thead>

                                    <!-- Standard Assessments Body -->
                                    <tbody>
                                        <tr v-for="(assessment, index) in filteredAssessments" :key="index" class="transition m-[4px]">
                                            <td colspan="10" class="px-4 py-3 w-fit pr-9 font-bold bg-custom-gradient text-white border-b border-white">
                                                {{ assessment.name }}
                                            </td>
                                            <td colspan="2" class="px-4 py-3 text-center">
                                                {{ assessment.weight || 1 }}
                                            </td>
                                            <td colspan="2" class="px-4 py-3 items-center text-center">
                                                {{ assessment?.questions_nbr > 25 ? parseInt((20 * 35) / 60) : parseInt((assessment?.questions_nbr * 35) / 60) }}'
                                            </td>
                                        </tr>
                                    </tbody>

                                    <!-- Custom Assessments Header -->
                                    <thead v-if="customAssessments?.questions_list.length > 0" class="">
                                        <tr class="border-b">
                                            <th colspan="10" class="h-11 px-4 text-left font-medium">{{ $t("Custom questions") }}</th>
                                            <th colspan="2" class="h-11 px-4 text-center font-medium">{{ $t("Duration") }}</th>
                                        </tr>
                                    </thead>

                                    <!-- Custom Assessments Body -->
                                    <tbody>
                                        <tr v-for="(question, index) in customAssessments?.questions_list" :key="index" class="border-b border-gray-200 transition hover:bg-[#f5f5f5]">
                                            <td colspan="10" class="px-4 py-3">
                                                {{ question?.title || question?.name }}
                                            </td>
                                            <td colspan="2" class="px-4 py-3 text-center">
                                                <!--font-awesome-icon :icon="['far', 'clock']" /-->
                                                {{ question?.time }}'
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <AssessmentsWeight v-if="showWeightning" :toggleWeighting="toggleWeighting" :assessments="project?.assessments" :projectId="project._id" />
                    </div>
                </div>

                <!-- Bottom row with CandidatesTable and Talent Map -->
                <div class="w-full">
                    <div class="rounded-md p-2 shadow-md bg-white mb-4">
                        <CandidatesTable
                            :toggleEmail="toggleEmail"
                            :candidates="candidates"
                            :projectAssessments="project.assessments"
                            :minScore="this.project.min_score ? this.project.min_score : this.project.recommanded_score"
                        />
                    </div>
                    <div class="w-full rounded-md p-2 shadow-md bg-white">
                        <div class="flex flex-row items-center w-full justify-between mb-[50px] p-3">
                            <div class="flex flex-row content-start items-center w-[50%]">
                                <span class="recentapp bg-custom-gradient bg-clip-text text-transparent">{{ $t("Talent Map") }}</span>
                            </div>
                            <div class="flex flex-row content-start items-center">
                                <span class="on-track"><img loading="lazy" decoding="async" src="@/assets/Images/icons/check-green.svg" alt="on-treack" /> {{ $t("On Track") }}</span>
                            </div>
                        </div>
                        <div class="w-full mb-3 flex justify-center items-center">
                            <div class="w-[85%]">
                                <TalentsMappingChart :toggleEmail="toggleEmail" :graphData="this.graphData" :chartWidth="windowWidth" :chartHeight="600" class="hidden lg:block" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import TalentsMappingChart from "@/charts/TalentsMappingChart.vue";
import { useStore } from "@/store/index";
import CardDetail from "../../../components/dashboard/project-board/comp/detailCard.vue";
import ProjectMenu from "@/components/dashboard/project/ProjectMenu.vue";
import LoadingComponent from "@/components/LoadingComponent.vue";
import CandidatesTable from "@/components/dashboard/project-board/CandidatesTable.vue";
import InviteCandidate from "@/components/dashboard/project-board/InviteCandidate.vue";
import AllRatings from "@/components/dashboard/project-board/AllRatings.vue";
import axios from "axios";
import introJs from "intro.js";
import StatsCard from "../../../components/dashboard/project-board/comp/StatsCard.vue";
import "intro.js/introjs.css"; // Import Intro.js CSS
import AssessmentsWeight from "@/components/dashboard/project-board/AssessmentsWeight.vue";
import ButtonComponent from "@/components/ReusableComponents/ButtonComponent.vue";
import { BASE_URL } from "@/constants";

export default {
    name: "DashboardView",
    components: {
        TalentsMappingChart,
        AllRatings,
        CardDetail,
        StatsCard,
        ProjectMenu,
        LoadingComponent,
        CandidatesTable,
        AssessmentsWeight,
        InviteCandidate,
        ButtonComponent,
    },
    data() {
        return {
            showpopup: false,
            imagePath: require(`@/assets/onBoardingGIFs/inviteCandidate.gif`),
            showEmail: false,
            windowWidth: 500,
            graphData: [],
            selected: [true, false, false],
            current: 0,
            linkIndex: 0,
            invitations: [],
            allCheaters: [],
            candidates: [],
            compitionRate: 0,
            project: {},
            candidatesScores: [],
            threeCandidates: [],
            candidatesRating: [],
            diskData: [],
            histogramData: [],
            histogramData2: [],
            ThreeRatings: [],
            behindSchedule: null,
            id: "",
            isCheatingCardOpen: false,
            candidateCard: {},
            showMenu: false,
            isLoading: true,
            viewAllRatings: false,
            viewAllCheaters: false,
            users: [
                { name: "User 1", email: "<EMAIL>" },
                { name: "User 2", email: "<EMAIL>" },
                { name: "User 3", email: "<EMAIL>" },
            ],
            showWeightning: false,
        };
    },
    methods: {
        handleTryAssessment() {
            this.showpopup = false;
            const locale = this.$route.params.locale || "en";

            const url = this.$router.resolve({
                path: `/${locale}/preview-project`,
                query: { id: this.project._id },
            }).href;

            // Open the route in a new tab
            window.open(url, "_blank");

            const updatedState = { ...history.state, show: false };
            window.history.replaceState(updatedState, "", window.location.href);
            // Additional logic for handling assessment
        },
        closePopup() {
            this.showpopup = false;
            const updatedState = { ...history.state, show: false };
            window.history.replaceState(updatedState, "", window.location.href);
        },
        handleInviteCandidates() {
            this.showpopup = false;
            this.showEmail = !this.showEmail;
            const updatedState = { ...history.state, show: false };
            window.history.replaceState(updatedState, "", window.location.href);
        },
        toggleWeighting() {
            this.showWeightning = !this.showWeightning;
        },
        async getCandidateInv() {
            // if (!this.candidateInfo || !this.candidate || !this.projectId) {
            //     console.error("Candidate information is incomplete.");
            //     return;
            // }

            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/candidates/candidateInfo`,
                headers: {
                    "Content-Type": "application/json",
                },
                params: {
                    email: this.candidateEmail,
                    projectId: this.projectId,
                },
                withCredentials: true,
            };

            axios
                .request(config)
                .then((response) => {
                    this.candidateInv = response.data.CandidateInvitation;
                    this.Store.candidateInfoAct = response.data.candidateInfo;
                    this.Store.candidateActivity = response.data.candidateScore;
                    this.candidateInfo = response.data.candidateInfo;
                    this.candidate = response.data.candidateScore;
                    this.candidateInterpretations = response.data.interpretations;
                    this.candidateRating = response.data.candidateRating;
                })
                .catch((error) => {
                    console.log(error);
                });
        },
        startIntro() {
            // Initialize Intro.js
            const intro = introJs();

            // Set Intro.js options
            intro.setOptions({
                steps: [
                    {
                        element: this.$refs.targetElement3,
                        // <img src="${this.imagePath}" alt="Image Description">
                        intro: `<h3>Invite candidate</h3>
                        <p>Invite all candidates you want to assess their skills.</p>`,
                        position: "bottom",
                    },

                    // Add more steps as needed
                ],
            });

            // Start the introduction
            intro.start();
        },

        toggleProjectMenu() {
            this.showMenu = !this.showMenu;
        },

        toggleRatings() {
            this.viewAllRatings = !this.viewAllRatings;
        },
        goToCheatingCard(id) {
            const locale = this.$route.params.locale || "en";

            this.$router.push({
                path: `/${locale}/anti-cheat/${id}`,
                // query: { id: this.project._id },
            });
        },
        toggleEmail() {
            this.showEmail = !this.showEmail;
        },

        openCheatingCard() {
            this.isCheatingCardOpen = true;
        },
        calculateAverageTime() {
            let averageTime = 0;
            this.candidates.map((candidate) => {
                averageTime += candidate.candidateTime;
            });
            averageTime = averageTime / this.candidates.length;
            return this.calculateTime(averageTime);
        },

        ///////////////////////////////////

        navigateToDashBoard(project) {
            this.linkIndex = 1;
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/boards`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToDetailBoard(project) {
            this.linkIndex = 2;
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/Details`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToCheatBoard(project) {
            this.linkIndex = 3;
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/CheatTab`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },

        navigateToVideoBoard(project) {
            this.linkIndex = 4;
            const locale = this.$route.params.locale || "en"; // Get the current locale or fallback to "en"
            this.$router.push({
                path: `/${locale}/DashVideo`, // Include the locale in the path
                query: { id: project._id }, // Add query parameters
            });
        },
        calculateTime(time) {
            let minutes = Math.floor(time / (60 * 1000));
            if (minutes > 59) minutes = 59;

            let seconds = time % (60 * 1000);
            if (seconds > 59) seconds = 59;

            minutes = minutes.toString().padStart(2, "0");

            if (seconds < 10) {
                seconds = "0" + seconds.toString();
            } else {
                seconds = seconds.toString();
            }
            if (isNaN(minutes)) {
                return "00 min 00 sec";
            }
            return `${minutes} min ${seconds} sec`;
        },
        selectTab(num) {
            this.selected[this.current] = false;
            this.selected[num] = true;
            this.current = num;
        },
        getAllCheaters(id) {
            axios.get(`${BASE_URL}/anticheat/potential-cheaters/${id}`).then((response) => {
                const potentialCheaters = response.data;

                const cheaters = response.data;
                this.allCheaters = [...potentialCheaters, ...cheaters];
            });
        },

        getCandidatesPassed() {
            let candidatesPassed = this.candidates.filter((candidate) => {
                let score = 0;
                candidate.results.forEach((element) => {
                    score += (element.totalPoints * 100) / element.quesionsNbr;
                });
                const averageScore = score / candidate.results.length;
                // const roundedScore = (averageScore * 100).toFixed(2); // Convert to percentage
                return averageScore > this.project.min_score; // Filter candidates with scores over 80%
            });
            return candidatesPassed.length > 0 ? candidatesPassed.length : 0;
        },
        calculateOverAllScore(results) {
            if (!Array.isArray(results) || results.length === 0) return 0;

            // Filter out results with 'personalityResults'
            const filteredResults = results.filter((result) => !result.personalityResults && !result.customResults);
            let score = 0;
            filteredResults.forEach((result) => {
                const assessment = this.project.assessments.find((assessment) => assessment.name === result.assessmentName);

                if (result.totalPoints) {
                    // score = result.totalPoints;
                    // possibleScore = result.quesionsNbr; // Max possible score is the number of questions
                    score += ((result.totalPoints * 100) / result.quesionsNbr) * (assessment.weight || 1);
                } else if (result.rangesPoint) {
                    score += (result.rangesPoint / (result.quesionsNbr * 5)) * 100 * (assessment.weight ? assessment.weight : 1);
                }
            });
            let totalWeights = 0;
            this.project.assessments.filter((assess) => assess.category != "Custom" && assess.category != "Personality").forEach((assess) => (totalWeights += 1 * (assess.weight || 1)));

            // Avoid division by zero
            let finalScore = score / totalWeights;

            return finalScore.toFixed(0);
        },

        getSuccessRate() {
            if (this.candidates.length > 0) {
                const candidatesAbove80Percent = this.candidates.filter((candidate) => {
                    // let score = 0;
                    // candidate.results.forEach((element) => {
                    //     score += (element.totalPoints * 100) / element.quesionsNbr;
                    // });
                    // const averageScore = score / candidate.results.length;
                    // const roundedScore = (averageScore * 100).toFixed(2); // Convert to percentage
                    let myScore = this.calculateOverAllScore(candidate.results);
                    return myScore >= this.project.min_score; // Filter candidates with scores over 80%
                });

                const numberOfCandidatesAbove80Percent = candidatesAbove80Percent.length;
                const totalCandidates = this.candidates.length;

                const percentageAbove80Percent = (numberOfCandidatesAbove80Percent / totalCandidates) * 100;
                return percentageAbove80Percent.toFixed(2);
            } else return 0;
        },
        async getBestCandidate(id) {
            if (id === "") {
                // this.filteredCandidates = this.candidatesResults;
                return;
            }
            this.histogramData = [];
            let config = {
                method: "get",
                maxBodyLength: Infinity,
                url: `${BASE_URL}/company/bestCandidate/${id}`,
                headers: {},
            };
            this.isLoading = true;
            axios
                .request(config)
                .then(async (response) => {
                    this.histogramData2 = response.data.candidatesScore;

                    // this.averageResults = response.data.averageData;
                    // this.averageScore();
                    // this.isLoading = false;
                })
                .catch((error) => {
                    console.log(error);
                    // this.isLoading = false;
                });
        },
        navigateToPreview() {
            const locale = this.$route.params.locale || "en";

            const url = this.$router.resolve({
                path: `/${locale}/preview-project`,
                query: { id: this.project._id },
            }).href;

            // Open the route in a new tab
            window.open(url, "_blank");
        },
    },
    computed: {
        filteredAssessmentsLength() {
            if (!this.project || !this.project.assessments) {
                return 0;
            }

            const hasCustomAssessment = this.project.assessments.some((assessment) => assessment.category === "Custom");

            return hasCustomAssessment ? this.project?.assessments?.length - 1 : this.project?.assessments?.length;
        },
        filteredAssessments() {
            return this.project.assessments.filter((assessment) => assessment.category !== "Custom");
        },
        customAssessments() {
            return this.project.assessments.find((assessment) => assessment.category == "Custom");
        },
        totalDuration: {
            get() {
                if (this.project?.assessments?.length > 0) {
                    const totalSeconds = this.filteredAssessments.reduce((acc, assessment) => {
                        if (assessment?.questions_nbr > 25) {
                            return acc + 20 * 35;
                        } else {
                            return acc + assessment?.questions_nbr * 35;
                        }
                    }, 0);

                    const minutes = Math.floor(totalSeconds / 60);
                    const seconds = totalSeconds % 60;
                    return `${minutes}:${seconds}`;
                } else {
                    return { minutes: 0, seconds: 0 };
                }
            },
        },
    },
    setup() {
        const Store = useStore();
        return { Store };
    },
    async mounted() {
        const stateData = history.state;
        if (stateData.show) {
            this.showpopup = true;
        }

        this.getCandidateInv();
        const headers = {
            "Content-Type": "application/json",
            withCredentials: true,
        };

        await axios
            .get(`${BASE_URL}/tooltip/get`, {
                headers,
                withCredentials: true,
            })
            .then((res) => {
                //alert(res.data);
                if (res.data != 2) {
                    setTimeout(() => {
                        this.startIntro();
                    }, 1500);

                    axios.get(`${BASE_URL}/tooltip/post2`, {
                        headers,
                        withCredentials: true,
                    });
                }
            })
            .catch((e) => {
                console.log(e);
            });

        //this.startIntro();

        //?
        this.id = this.$route.query.id;
        this.getAllCheaters(this.id);
        this.getBestCandidate(this.id);

        await this.Store.fetchProjects();
        // this.Store.fetchCandidates();
        this.Store.getCompanyAssessments();
        this.Store.fetchInvitations();

        this.project = this.Store.projects[0]?._id;

        let config = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/projects/projectData`,
            headers: {
                "Content-Type": "application/json",
            },
            params: {
                id: this.id,
            },
            withCredentials: true,
        };

        await axios
            .request(config)
            .then((response) => {
                this.invitations = response.data.invited;
                this.candidates = response.data.candidates;
                this.behindSchedule = this.invitations.length - this.candidates.length;
                this.threeCandidates = this.candidates.slice(0, 3);
                this.project = response.data.project;
                this.compitionRate = (this.candidates.length / this.invitations.length).toFixed(2);
                this.graphData = response.data.chartData;
                this.histogramData = [
                    {
                        label: "Invited",
                        value: this.invitations.length,
                    },
                    {
                        label: "Attempted",
                        value: this.candidates.length,
                    },
                    {
                        label: "Passed",
                        value: this.getCandidatesPassed(),
                    },
                ];
                this.isLoading = false;
                // this.yourAssessment = response.data.assessments;
                // this.score = response.data.score;
            })
            .catch((error) => {
                console.log(error);
            });
        this.diskData = this.Store.createDistributionData(this.id);

        let config2 = {
            method: "get",
            maxBodyLength: Infinity,
            url: `${BASE_URL}/AssessmentTest/candidateRating/${this.id}`,
            headers: {},
            withCredentials: true,
        };
        axios
            .request(config2)
            .then((response) => {
                this.candidatesRating = response.data;
                this.ThreeRatings = this.candidatesRating.slice(0, 3);
            })
            .catch((error) => {
                console.log({ error });
            });
        console.log("this.project.assessments.filter((assessment) => assessment.category");
        console.log(this.project.assessments);
        console.log("this.project.assessments.filter((assessment) => assessment.category");
    },
};
</script>

<style scoped lang="scss">
.board-container {
    display: flex;
    flex-direction: column;
    padding-top: 40px;
}

.menuBtn {
    width: 60px;
    height: 50px;

    border-radius: 6px;
}

.projData {
    font-size: 20px;
    font-weight: 700;
}

.on-track {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 700;
    font-size: 16px;
    color: #05cd99;
    gap: 8px;
}

/*----------------------------------------------------------------*/

.navigation-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 32px;
    padding-left: 48px;
    border-bottom: 1px solid #edeff2;
    margin-bottom: 25px;
}

.nav-links span {
    font-size: 16px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
    color: black;
}

.nav-links > a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding-top: 0px;
    padding-bottom: 24px;
    text-decoration: none;
    font-weight: 700;
    font-size: 14px;
    color: #7d8fb3;
    margin-right: 24px;
    position: relative;
}

.nav-links .nav-icon {
    font-size: 20px;
}

.nav-links img {
    margin-right: 12px;
}

.nav-links a::after {
    content: "";
    background: #2196f3;
    position: absolute;
    bottom: 2.4px;
    left: 0;
    width: 0;
    height: 2px;
    transition: width 0.4s ease-in-out;
    border-radius: 25px;
}

.nav-links > a:hover::after {
    width: 100%;
    color: #2196f3;
}

.nav-links a.active::after {
    background: #2196f3;
    width: 100%;
}
.disabled {
    pointer-events: none;
    span {
        opacity: 0.5;
    }
}

.recentapp {
    font-size: 20px;
    color: #2196f3;
    font-style: normal;
    font-weight: 700;
    line-height: 32px;
    letter-spacing: -0.4px;
}

.nextStep {
    width: 100px;
    height: 50px;
    font-weight: 500;

    &:hover {
        opacity: 0.85;
    }
}

.loader {
    width: 100%;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.nav-links span:hover {
    color: #2196f3;
}
</style>
